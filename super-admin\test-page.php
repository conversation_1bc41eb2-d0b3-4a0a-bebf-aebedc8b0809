<?php
/**
 * Test page for super admin system
 */

session_start();

// Include required files
require_once 'includes/auth.php';

// Require super admin authentication
requireSuperAdminAuth();

$page_title = 'Test Page';
$page_subtitle = 'Testing super admin system';

// Include header
include 'includes/header.php';
?>

<div class="card">
    <div class="card-header">
        <h5>Super Admin System Test</h5>
    </div>
    <div class="card-body">
        <h6>✅ Authentication Working</h6>
        <p>If you can see this page, the super admin authentication system is working correctly.</p>
        
        <h6>📊 Session Information</h6>
        <ul>
            <li><strong>Username:</strong> <?php echo htmlspecialchars($_SESSION['super_admin_username'] ?? 'Not set'); ?></li>
            <li><strong>Login Time:</strong> <?php echo isset($_SESSION['super_admin_login_time']) ? date('Y-m-d H:i:s', $_SESSION['super_admin_login_time']) : 'Not set'; ?></li>
            <li><strong>IP Address:</strong> <?php echo htmlspecialchars($_SESSION['super_admin_ip'] ?? 'Not set'); ?></li>
        </ul>
        
        <h6>🔧 Database Test</h6>
        <?php
        try {
            require_once '../config/database.php';
            $db = getDB();
            echo "<p class='text-success'>✅ Database connection successful</p>";
            
            // Test super_admin_settings table
            $result = $db->query("SELECT COUNT(*) as count FROM super_admin_settings");
            if ($result) {
                $row = $result->fetch_assoc();
                echo "<p class='text-success'>✅ super_admin_settings table accessible (". $row['count'] ." records)</p>";
            } else {
                echo "<p class='text-warning'>⚠️ super_admin_settings table not found</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='text-danger'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        ?>
        
        <h6>📁 File System Test</h6>
        <?php
        $files_to_check = [
            'includes/auth.php',
            'includes/header.php',
            'includes/footer.php',
            '../config/database.php'
        ];
        
        foreach ($files_to_check as $file) {
            if (file_exists($file)) {
                echo "<p class='text-success'>✅ $file exists</p>";
            } else {
                echo "<p class='text-danger'>❌ $file missing</p>";
            }
        }
        ?>
        
        <div class="mt-3">
            <a href="dashboard.php" class="btn btn-primary">Back to Dashboard</a>
            <a href="system-settings.php" class="btn btn-secondary">Test System Settings</a>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
