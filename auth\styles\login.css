/**
 * Modern User Login Page Styles
 * Clean, professional design matching admin login quality
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.container-fluid {
    min-height: 100vh;
    padding: 0;
}

.row {
    min-height: 100vh;
    margin: 0;
}

/* Left Panel - Login Form */
.left-panel {
    background: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 60px 80px;
    position: relative;
}

.back-link {
    position: absolute;
    top: 20px;
    left: 20px;
    color: #6b7280;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: color 0.2s ease;
}

.back-link:hover {
    color: #4f46e5;
}

.logo {
    margin-bottom: 40px;
    text-align: left;
    width: 100%;
}

.logo img {
    max-width: 200px;
    height: auto;
    max-height: 80px;
    object-fit: contain;
    background: transparent;
    mix-blend-mode: multiply;
}

.logo-fallback {
    font-size: 24px;
    font-weight: 700;
    color: #4f46e5;
    margin: 0;
}

.welcome-text {
    text-align: left;
    margin-bottom: 40px;
    width: 100%;
}

.welcome-text h1 {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 10px;
}

.welcome-text p {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

.login-form {
    width: 100%;
    max-width: 400px;
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
    width: 100%;
}

.form-label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.form-control {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: white;
    width: 100%;
    box-sizing: border-box;
}

.form-control:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    outline: none;
}

.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 16px;
}

.input-group .form-control {
    padding-left: 40px;
}

.forgot-password {
    text-align: right;
    margin-top: 8px;
}

.forgot-password a {
    color: #4f46e5;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.forgot-password a:hover {
    text-decoration: underline;
}

.btn-login {
    background: #4f46e5;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 16px;
    width: 100%;
    transition: all 0.2s ease;
    cursor: pointer;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-login:hover {
    background: #4338ca;
    transform: translateY(-1px);
}

.btn-login:active {
    transform: translateY(0);
}

.signup-link {
    text-align: center;
    margin-top: 20px;
    color: #6b7280;
    font-size: 14px;
}

.signup-link a {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 500;
}

.signup-link a:hover {
    text-decoration: underline;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    padding: 12px 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.alert-danger {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
}

.alert-warning {
    background: #fffbeb;
    color: #d97706;
    border-color: #fed7aa;
}

.alert-success {
    background: #f0fdf4;
    color: #16a34a;
    border-color: #bbf7d0;
}

.alert-info {
    background: #eff6ff;
    color: #2563eb;
    border-color: #bfdbfe;
}

.alert ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.alert li {
    margin-bottom: 4px;
}

/* Right Panel - Enhanced Visual Design */
.right-panel {
    background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 50%, #2563eb 100%);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Layer 1: Background Image */
.right-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><linearGradient id="bg1" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23667eea;stop-opacity:0.1"/><stop offset="100%" style="stop-color:%23764ba2;stop-opacity:0.1"/></linearGradient></defs><rect width="1200" height="800" fill="url(%23bg1)"/><g opacity="0.1"><rect x="100" y="100" width="200" height="120" rx="8" fill="white"/><rect x="350" y="150" width="180" height="100" rx="8" fill="white"/><rect x="580" y="120" width="220" height="140" rx="8" fill="white"/><rect x="850" y="180" width="200" height="110" rx="8" fill="white"/><circle cx="200" cy="400" r="60" fill="white"/><circle cx="500" cy="450" r="45" fill="white"/><circle cx="800" cy="420" r="55" fill="white"/><path d="M150 600 L350 550 L550 580 L750 520 L950 560" stroke="white" stroke-width="3" fill="none"/><path d="M150 650 L350 620 L550 640 L750 600 L950 630" stroke="white" stroke-width="2" fill="none" opacity="0.7"/></g></svg>');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.15;
    z-index: 1;
}

/* Layer 2: Gradient Overlay */
.right-panel::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(79, 70, 229, 0.8) 0%,
        rgba(59, 130, 246, 0.7) 50%,
        rgba(37, 99, 235, 0.8) 100%);
    z-index: 2;
}

.right-panel-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    z-index: 3;
    position: relative;
    padding: 40px;
}

.feature-illustration {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40px;
    position: relative;
}

/* SVG Dashboard Container */
.dashboard-svg-container {
    width: 100%;
    max-width: 400px;
    height: auto;
    position: relative;
    z-index: 4;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
}

/* SVG Dashboard Elements */
.dashboard-svg {
    width: 100%;
    height: auto;
    opacity: 0.95;
}

/* SVG Animation Classes */
.dashboard-card {
    animation: float 6s ease-in-out infinite;
    transform-origin: center;
}

.dashboard-card:nth-child(2) {
    animation-delay: -2s;
}

.dashboard-card:nth-child(3) {
    animation-delay: -4s;
}

.chart-line {
    stroke-dasharray: 200;
    stroke-dashoffset: 200;
    animation: drawLine 3s ease-in-out infinite;
}

.chart-line:nth-child(2) {
    animation-delay: 1s;
}

.chart-line:nth-child(3) {
    animation-delay: 2s;
}

.pulse-dot {
    animation: pulse 2s ease-in-out infinite;
}

.pulse-dot:nth-child(2) {
    animation-delay: 0.5s;
}

.pulse-dot:nth-child(3) {
    animation-delay: 1s;
}

/* Keyframe Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes drawLine {
    0% {
        stroke-dashoffset: 200;
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        stroke-dashoffset: 0;
        opacity: 1;
    }
    100% {
        stroke-dashoffset: -200;
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

.bottom-text {
    flex: 0 0 auto;
    text-align: center;
    color: white;
}

.bottom-text h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    opacity: 0.95;
}

.bottom-text p {
    font-size: 14px;
    opacity: 0.8;
    margin: 0;
    line-height: 1.5;
}

/* Security Notice */
.security-notice {
    background: #f9fafb;
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
    text-align: left;
    border: 1px solid #e5e7eb;
}

.security-notice h6 {
    color: #374151;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.security-notice p {
    color: #6b7280;
    font-size: 13px;
    margin: 0;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-svg-container {
        max-width: 350px;
    }

    .right-panel-content {
        padding: 30px;
    }
}

@media (max-width: 992px) {
    .dashboard-svg-container {
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    .left-panel {
        padding: 40px 30px;
    }

    .right-panel {
        display: none;
    }

    .welcome-text h1 {
        font-size: 24px;
    }

    .logo img {
        max-width: 150px;
        max-height: 60px;
    }
}

@media (max-width: 576px) {
    .left-panel {
        padding: 30px 20px;
    }

    .welcome-text h1 {
        font-size: 22px;
    }

    .back-link {
        top: 15px;
        left: 15px;
    }
}

/* High-DPI Display Optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .dashboard-svg-container {
        filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
    }
}

/* Enhanced Visual Effects */
.right-panel-content {
    backdrop-filter: blur(1px);
    -webkit-backdrop-filter: blur(1px);
}

/* SVG Performance Optimizations */
.dashboard-svg {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* Smooth transitions for interactive elements */
.dashboard-card {
    transition: transform 0.3s ease;
}

.dashboard-svg-container:hover .dashboard-card {
    animation-play-state: paused;
}

/* Additional visual depth */
.right-panel {
    box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.1);
}

/* Text readability enhancement */
.bottom-text {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .dashboard-card,
    .chart-line,
    .pulse-dot {
        animation: none;
    }

    .dashboard-svg-container {
        filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
    }
}

/* Loading State */
.btn-login.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-login.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Focus Styles for Accessibility */
.form-control:focus,
.btn-login:focus,
.back-link:focus,
.forgot-password a:focus,
.signup-link a:focus {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .form-control {
        border-width: 2px;
    }
    
    .btn-login {
        border: 2px solid #4f46e5;
    }
}
