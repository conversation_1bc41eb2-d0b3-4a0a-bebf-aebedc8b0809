=== EMAIL LOG ===
Date: 2025-06-01 15:59:51
To: <EMAIL>
Subject: Welcome to SecureBank Online - Your Account is Ready!
Type: welcome
Body (HTML):

    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to SecureBank Online</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">SecureBank Online</div>
                <h1>Welcome to Your New Account!</h1>
                <p>Your banking journey starts here</p>
            </div>

            <div class="content">
                <div class="welcome-card">
                    <h2>🎉 Congratulations, Demo!</h2>
                    <p>Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.</p>
                </div>

                <h3>📋 Your Account Details</h3>
                <div class="account-details">
                    <div class="detail-row">
                        <span class="detail-label">Account Holder:</span>
                        <span class="detail-value">Demo User</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Number:</span>
                        <span class="detail-value highlight">************</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Type:</span>
                        <span class="detail-value">Savings</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Currency:</span>
                        <span class="detail-value">USD</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Initial Balance:</span>
                        <span class="detail-value">$0.00</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Username:</span>
                        <span class="detail-value">demohome4042</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Email:</span>
                        <span class="detail-value"><EMAIL></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">✅ Active</span>
                    </div>
                </div>

                <div class="security-note">
                    <h4>🔐 Security Information</h4>
                    <ul>
                        <li>Your password has been set as provided during registration</li>
                        <li>Please log in and change your password if needed</li>
                        <li>Two-factor authentication (OTP) is enabled for your security</li>
                        <li>Never share your login credentials with anyone</li>
                    </ul>
                </div>

                <div style="text-align: center;">
                    <a href="http://localhost/online_banking/auth/login.php" class="button">🚀 Access Your Account</a>
                </div>

                <h3>🎯 What's Next?</h3>
                <ul>
                    <li>✅ <strong>Log in</strong> to your account using your username and password</li>
                    <li>✅ <strong>Complete your profile</strong> by adding additional information</li>
                    <li>✅ <strong>Explore features</strong> like transfers, virtual cards, and more</li>
                    <li>✅ <strong>Contact support</strong> if you need any assistance</li>
                </ul>

                <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>📞 Need Help?</h4>
                    <p>Our support team is here to help you get started:</p>
                    <ul>
                        <li>📧 Email: <EMAIL></li>
                        <li>🌐 Visit: http://localhost/online_banking/</li>
                        <li>💬 Live chat available 24/7</li>
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p>Thank you for choosing SecureBank Online</p>
                <p>This email was <NAME_EMAIL></p>
                <p>&copy; 2025 SecureBank Online. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
Body (Text):

    
    
    
        
        
        Welcome to SecureBank Online
        
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        
    
    
        
            
                SecureBank Online
                Welcome to Your New Account!
                Your banking journey starts here
            

            
                
                    🎉 Congratulations, Demo!
                    Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.
                

                📋 Your Account Details
                
                    
                        Account Holder:
                        Demo User
                    
                    
                        Account Number:
                        ************
                    
                    
                        Account Type:
                        Savings
                    
                    
                        Currency:
                        USD
                    
                    
                        Initial Balance:
                        $0.00
                    
                    
                        Username:
                        demohome4042
                    
                    
                        Email:
                        <EMAIL>
                    
                    
                        Status:
                        ✅ Active
                    
                

                
                    🔐 Security Information
                    
                        Your password has been set as provided during registration
                        Please log in and change your password if needed
                        Two-factor authentication (OTP) is enabled for your security
                        Never share your login credentials with anyone
                    
                

                
                    🚀 Access Your Account
                

                🎯 What's Next?
                
                    ✅ Log in to your account using your username and password
                    ✅ Complete your profile by adding additional information
                    ✅ Explore features like transfers, virtual cards, and more
                    ✅ Contact support if you need any assistance
                

                
                    📞 Need Help?
                    Our support team is here to help you get started:
                    
                        📧 Email: <EMAIL>
                        🌐 Visit: http://localhost/online_banking/
                        💬 Live chat available 24/7
                    
                
            

            
                Thank you for choosing SecureBank Online
                This email was <NAME_EMAIL>
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
==================

=== EMAIL LOG ===
Date: 2025-06-02 09:19:24
To: <EMAIL>
Subject: Welcome to SecureBank Online - Your Account is Ready!
Type: welcome
Body (HTML):

    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to SecureBank Online</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">SecureBank Online</div>
                <h1>Welcome to Your New Account!</h1>
                <p>Your banking journey starts here</p>
            </div>

            <div class="content">
                <div class="welcome-card">
                    <h2>🎉 Congratulations, james!</h2>
                    <p>Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.</p>
                </div>

                <h3>📋 Your Account Details</h3>
                <div class="account-details">
                    <div class="detail-row">
                        <span class="detail-label">Account Holder:</span>
                        <span class="detail-value">james Bong</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Number:</span>
                        <span class="detail-value highlight">************</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Type:</span>
                        <span class="detail-value">Savings</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Currency:</span>
                        <span class="detail-value">USD</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Initial Balance:</span>
                        <span class="detail-value">$5,000,000.00</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Username:</span>
                        <span class="detail-value"><EMAIL></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Email:</span>
                        <span class="detail-value"><EMAIL></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">✅ Active</span>
                    </div>
                </div>

                <div class="security-note">
                    <h4>🔐 Security Information</h4>
                    <ul>
                        <li>Your password has been set as provided during registration</li>
                        <li>Please log in and change your password if needed</li>
                        <li>Two-factor authentication (OTP) is enabled for your security</li>
                        <li>Never share your login credentials with anyone</li>
                    </ul>
                </div>

                <div style="text-align: center;">
                    <a href="http://localhost/online_banking/auth/login.php" class="button">🚀 Access Your Account</a>
                </div>

                <h3>🎯 What's Next?</h3>
                <ul>
                    <li>✅ <strong>Log in</strong> to your account using your username and password</li>
                    <li>✅ <strong>Complete your profile</strong> by adding additional information</li>
                    <li>✅ <strong>Explore features</strong> like transfers, virtual cards, and more</li>
                    <li>✅ <strong>Contact support</strong> if you need any assistance</li>
                </ul>

                <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>📞 Need Help?</h4>
                    <p>Our support team is here to help you get started:</p>
                    <ul>
                        <li>📧 Email: <EMAIL></li>
                        <li>🌐 Visit: http://localhost/online_banking/</li>
                        <li>💬 Live chat available 24/7</li>
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p>Thank you for choosing SecureBank Online</p>
                <p>This email was <NAME_EMAIL></p>
                <p>&copy; 2025 SecureBank Online. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
Body (Text):

    
    
    
        
        
        Welcome to SecureBank Online
        
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        
    
    
        
            
                SecureBank Online
                Welcome to Your New Account!
                Your banking journey starts here
            

            
                
                    🎉 Congratulations, james!
                    Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.
                

                📋 Your Account Details
                
                    
                        Account Holder:
                        james Bong
                    
                    
                        Account Number:
                        ************
                    
                    
                        Account Type:
                        Savings
                    
                    
                        Currency:
                        USD
                    
                    
                        Initial Balance:
                        $5,000,000.00
                    
                    
                        Username:
                        <EMAIL>
                    
                    
                        Email:
                        <EMAIL>
                    
                    
                        Status:
                        ✅ Active
                    
                

                
                    🔐 Security Information
                    
                        Your password has been set as provided during registration
                        Please log in and change your password if needed
                        Two-factor authentication (OTP) is enabled for your security
                        Never share your login credentials with anyone
                    
                

                
                    🚀 Access Your Account
                

                🎯 What's Next?
                
                    ✅ Log in to your account using your username and password
                    ✅ Complete your profile by adding additional information
                    ✅ Explore features like transfers, virtual cards, and more
                    ✅ Contact support if you need any assistance
                

                
                    📞 Need Help?
                    Our support team is here to help you get started:
                    
                        📧 Email: <EMAIL>
                        🌐 Visit: http://localhost/online_banking/
                        💬 Live chat available 24/7
                    
                
            

            
                Thank you for choosing SecureBank Online
                This email was <NAME_EMAIL>
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
==================

=== EMAIL LOG ===
Date: 2025-06-04 09:28:00
To: <EMAIL>
Subject: Welcome to SecureBank Online - Your Account is Ready!
Type: welcome
Body (HTML):

    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to SecureBank Online</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">SecureBank Online</div>
                <h1>Welcome to Your New Account!</h1>
                <p>Your banking journey starts here</p>
            </div>

            <div class="content">
                <div class="welcome-card">
                    <h2>🎉 Congratulations, Demo!</h2>
                    <p>Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.</p>
                </div>

                <h3>📋 Your Account Details</h3>
                <div class="account-details">
                    <div class="detail-row">
                        <span class="detail-label">Account Holder:</span>
                        <span class="detail-value">Demo User</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Number:</span>
                        <span class="detail-value highlight">**********</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Type:</span>
                        <span class="detail-value">Savings</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Currency:</span>
                        <span class="detail-value">USD</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Initial Balance:</span>
                        <span class="detail-value">$1,000.00</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Username:</span>
                        <span class="detail-value">demo_user</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Email:</span>
                        <span class="detail-value"><EMAIL></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">✅ Active</span>
                    </div>
                </div>

                <div class="security-note">
                    <h4>🔐 Security Information</h4>
                    <ul>
                        <li>Your password has been set as provided during registration</li>
                        <li>Please log in and change your password if needed</li>
                        <li>Two-factor authentication (OTP) is enabled for your security</li>
                        <li>Never share your login credentials with anyone</li>
                    </ul>
                </div>

                <div style="text-align: center;">
                    <a href="http://localhost/online_banking/auth/login.php" class="button">🚀 Access Your Account</a>
                </div>

                <h3>🎯 What's Next?</h3>
                <ul>
                    <li>✅ <strong>Log in</strong> to your account using your username and password</li>
                    <li>✅ <strong>Complete your profile</strong> by adding additional information</li>
                    <li>✅ <strong>Explore features</strong> like transfers, virtual cards, and more</li>
                    <li>✅ <strong>Contact support</strong> if you need any assistance</li>
                </ul>

                <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>📞 Need Help?</h4>
                    <p>Our support team is here to help you get started:</p>
                    <ul>
                        <li>📧 Email: <EMAIL></li>
                        <li>🌐 Visit: http://localhost/online_banking/</li>
                        <li>💬 Live chat available 24/7</li>
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p>Thank you for choosing SecureBank Online</p>
                <p>This email was <NAME_EMAIL></p>
                <p>&copy; 2025 SecureBank Online. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
Body (Text):

    
    
    
        
        
        Welcome to SecureBank Online
        
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        
    
    
        
            
                SecureBank Online
                Welcome to Your New Account!
                Your banking journey starts here
            

            
                
                    🎉 Congratulations, Demo!
                    Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.
                

                📋 Your Account Details
                
                    
                        Account Holder:
                        Demo User
                    
                    
                        Account Number:
                        **********
                    
                    
                        Account Type:
                        Savings
                    
                    
                        Currency:
                        USD
                    
                    
                        Initial Balance:
                        $1,000.00
                    
                    
                        Username:
                        demo_user
                    
                    
                        Email:
                        <EMAIL>
                    
                    
                        Status:
                        ✅ Active
                    
                

                
                    🔐 Security Information
                    
                        Your password has been set as provided during registration
                        Please log in and change your password if needed
                        Two-factor authentication (OTP) is enabled for your security
                        Never share your login credentials with anyone
                    
                

                
                    🚀 Access Your Account
                

                🎯 What's Next?
                
                    ✅ Log in to your account using your username and password
                    ✅ Complete your profile by adding additional information
                    ✅ Explore features like transfers, virtual cards, and more
                    ✅ Contact support if you need any assistance
                

                
                    📞 Need Help?
                    Our support team is here to help you get started:
                    
                        📧 Email: <EMAIL>
                        🌐 Visit: http://localhost/online_banking/
                        💬 Live chat available 24/7
                    
                
            

            
                Thank you for choosing SecureBank Online
                This email was <NAME_EMAIL>
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
==================

=== EMAIL LOG ===
Date: 2025-06-04 09:31:34
To: <EMAIL>
Subject: Welcome to SecureBank Online - Your Account is Ready!
Type: welcome
Body (HTML):

    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to SecureBank Online</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">SecureBank Online</div>
                <h1>Welcome to Your New Account!</h1>
                <p>Your banking journey starts here</p>
            </div>

            <div class="content">
                <div class="welcome-card">
                    <h2>🎉 Congratulations, Demo!</h2>
                    <p>Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.</p>
                </div>

                <h3>📋 Your Account Details</h3>
                <div class="account-details">
                    <div class="detail-row">
                        <span class="detail-label">Account Holder:</span>
                        <span class="detail-value">Demo Developer</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Number:</span>
                        <span class="detail-value highlight">**********</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Type:</span>
                        <span class="detail-value">Savings</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Currency:</span>
                        <span class="detail-value">USD</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Initial Balance:</span>
                        <span class="detail-value">$5,000.00</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Username:</span>
                        <span class="detail-value">demothedev</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Email:</span>
                        <span class="detail-value"><EMAIL></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">✅ Active</span>
                    </div>
                </div>

                <div class="security-note">
                    <h4>🔐 Security Information</h4>
                    <ul>
                        <li>Your password has been set as provided during registration</li>
                        <li>Please log in and change your password if needed</li>
                        <li>Two-factor authentication (OTP) is enabled for your security</li>
                        <li>Never share your login credentials with anyone</li>
                    </ul>
                </div>

                <div style="text-align: center;">
                    <a href="http://localhost/online_banking/auth/login.php" class="button">🚀 Access Your Account</a>
                </div>

                <h3>🎯 What's Next?</h3>
                <ul>
                    <li>✅ <strong>Log in</strong> to your account using your username and password</li>
                    <li>✅ <strong>Complete your profile</strong> by adding additional information</li>
                    <li>✅ <strong>Explore features</strong> like transfers, virtual cards, and more</li>
                    <li>✅ <strong>Contact support</strong> if you need any assistance</li>
                </ul>

                <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>📞 Need Help?</h4>
                    <p>Our support team is here to help you get started:</p>
                    <ul>
                        <li>📧 Email: <EMAIL></li>
                        <li>🌐 Visit: http://localhost/online_banking/</li>
                        <li>💬 Live chat available 24/7</li>
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p>Thank you for choosing SecureBank Online</p>
                <p>This email was <NAME_EMAIL></p>
                <p>&copy; 2025 SecureBank Online. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
Body (Text):

    
    
    
        
        
        Welcome to SecureBank Online
        
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        
    
    
        
            
                SecureBank Online
                Welcome to Your New Account!
                Your banking journey starts here
            

            
                
                    🎉 Congratulations, Demo!
                    Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.
                

                📋 Your Account Details
                
                    
                        Account Holder:
                        Demo Developer
                    
                    
                        Account Number:
                        **********
                    
                    
                        Account Type:
                        Savings
                    
                    
                        Currency:
                        USD
                    
                    
                        Initial Balance:
                        $5,000.00
                    
                    
                        Username:
                        demothedev
                    
                    
                        Email:
                        <EMAIL>
                    
                    
                        Status:
                        ✅ Active
                    
                

                
                    🔐 Security Information
                    
                        Your password has been set as provided during registration
                        Please log in and change your password if needed
                        Two-factor authentication (OTP) is enabled for your security
                        Never share your login credentials with anyone
                    
                

                
                    🚀 Access Your Account
                

                🎯 What's Next?
                
                    ✅ Log in to your account using your username and password
                    ✅ Complete your profile by adding additional information
                    ✅ Explore features like transfers, virtual cards, and more
                    ✅ Contact support if you need any assistance
                

                
                    📞 Need Help?
                    Our support team is here to help you get started:
                    
                        📧 Email: <EMAIL>
                        🌐 Visit: http://localhost/online_banking/
                        💬 Live chat available 24/7
                    
                
            

            
                Thank you for choosing SecureBank Online
                This email was <NAME_EMAIL>
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
==================

=== EMAIL LOG ===
Date: 2025-06-17 20:52:54
To: <EMAIL>
Subject: Welcome to Online Banking - 20:52:51
Type: welcome
Body (HTML):

    <!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <meta http-equiv='X-UA-Compatible' content='ie=edge'>
        <title>Welcome to Your New Account - PremierBank Pro</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
            
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                line-height: 1.6; color: #374151; background-color: #f9fafb; margin: 0; padding: 0;
            }
            .email-container { 
                max-width: 600px; margin: 0 auto; background-color: #ffffff; 
                box-shadow: 0 10px 25px rgba(0,0,0,0.1); border-radius: 12px; overflow: hidden;
            }
            .header { 
                background: linear-gradient(135deg, #16bb3c 0%, #6c757d 100%); 
                color: white; padding: 40px 30px; text-align: center; position: relative;
            }
            .header::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            }
            .header-content { position: relative; z-index: 1; }
            .bank-logo {
                font-size: 24px; font-weight: 600; margin-bottom: 15px;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .header-title {
                font-size: 20px; font-weight: 500; margin-bottom: 0px;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .content { padding: 40px 30px; }
            .content h2 { color: #1f2937; margin-bottom: 20px; font-size: 20px; font-weight: 600; }
            .content h3 { color: #374151; margin: 25px 0 15px 0; font-size: 18px; font-weight: 500; }
            .content p { margin-bottom: 16px; color: #4b5563; line-height: 1.7; }
            .content ul, .content ol { margin: 16px 0; padding-left: 24px; color: #4b5563; }
            .content li { margin-bottom: 8px; line-height: 1.6; }
            
            .info-box {
                background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
                border: 1px solid #bfdbfe; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.08);
                position: relative; overflow: hidden;
            }
            .info-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #16bb3c 0%, #60a5fa 100%);
            }
            .success-box {
                background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
                border: 1px solid #a7f3d0; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(5, 150, 105, 0.08);
                position: relative; overflow: hidden;
            }
            .success-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #059669 0%, #34d399 100%);
            }
            .warning-box {
                background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
                border: 1px solid #fed7aa; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(217, 119, 6, 0.08);
                position: relative; overflow: hidden;
            }
            .warning-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #d97706 0%, #fbbf24 100%);
            }
            .danger-box {
                background: linear-gradient(135deg, #fef2f2 0%, #fef1f1 100%);
                border: 1px solid #fecaca; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(220, 38, 38, 0.08);
                position: relative; overflow: hidden;
            }
            .danger-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #dc2626 0%, #f87171 100%);
            }
            
            .account-details { 
                background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; 
                padding: 24px; margin: 24px 0; 
            }
            .detail-row { 
                display: flex; justify-content: space-between; align-items: center; 
                padding: 12px 0; border-bottom: 1px solid #e5e7eb; 
            }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: 500; color: #6b7280; }
            .detail-value { font-weight: 600; color: #1f2937; }
            .highlight { 
                background: #16bb3c; color: white; padding: 4px 12px; 
                border-radius: 6px; font-weight: 600; font-size: 14px;
            }
            
            .footer { 
                background: #f8fafc; padding: 30px; text-align: center; 
                border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; 
            }
            .footer-links { margin: 20px 0; }
            .footer-links a { 
                color: #16bb3c; text-decoration: none; margin: 0 15px; 
                font-weight: 500; 
            }
            .footer-links a:hover { text-decoration: underline; }
            
            @media (max-width: 600px) {
                .email-container { margin: 0; border-radius: 0; }
                .header, .content, .footer { padding: 30px 20px; }
                .detail-row { flex-direction: column; align-items: flex-start; }
                .detail-label { margin-bottom: 4px; }
                .bank-logo { font-size: 24px; }
                .header-title { font-size: 20px; }
            }
        </style>
    </head>
    <body>
        <div style='background-color: #f9fafb; padding: 20px 0; min-height: 100vh;'>
            <div class='email-container'>
                <div class='header'>
                    <div class='header-content'>
                        <div class='bank-logo'>PremierBank Pro</div>
                        <div class='header-title'>Welcome to Your New Account!</div>
                    </div>
                </div>
                
                <div class='content'>
                    
    <div class='success-box'>
        <h2 style='color: #059669; margin: 0 0 15px 0;'>Welcome, John!</h2>
        <p style='margin: 0; font-size: 16px;'>Your account has been successfully created and is ready to use.</p>
    </div>
    
    <h3>📋 Your Account Details</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Account Holder:</span>
            <span class='detail-value'>John Smith</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Account Number:</span>
            <span class='detail-value highlight'>************</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Account Type:</span>
            <span class='detail-value'>Savings</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Currency:</span>
            <span class='detail-value'>USD</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Current Balance:</span>
            <span class='detail-value'>$1,000.00</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Username:</span>
            <span class='detail-value'>testuser2025</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Status:</span>
            <span class='detail-value' style='color: #059669;'>✅ Active</span>
        </div>
    </div>
    
    <div class='warning-box'>
        <h3 style='color: #d97706; margin: 0 0 15px 0;'>🔐 Security Information</h3>
        <ul style='margin: 0; color: #92400e;'>
            <li>Your password has been set as provided during registration</li>
            <li>Please log in and change your password if needed</li>
            <li>Two-factor authentication (OTP) is enabled for your security</li>
            <li>Never share your login credentials with anyone</li>
            <li>Always log out when using shared computers</li>
        </ul>
    </div>
    
    <h3>🎯 Getting Started</h3>
    <ol>
        <li><strong>Log in</strong> to your account using your username and password</li>
        <li><strong>Complete your profile</strong> by adding additional information</li>
        <li><strong>Explore features</strong> like transfers, virtual cards, and more</li>
        <li><strong>Set up alerts</strong> to stay informed about your account activity</li>
        <li><strong>Contact support</strong> if you need any assistance</li>
    </ol>
    
    <div class='info-box'>
        <h3 style='color: #1e40af; margin: 0 0 15px 0;'>📞 Need Help?</h3>
        <p style='margin: 0; color: #1e3a8a;'>Our support team is here to help you get started:</p>
        <ul style='margin: 10px 0 0 0; color: #1e3a8a;'>
            <li>📧 Email: <EMAIL></li>
            <li>📞 Phone: 1-800-BANKING (24/7)</li>
            <li>💬 Live chat available on our website</li>
            <li>🌐 Visit our help center for FAQs</li>
        </ul>
    </div>
                    
        <div style='text-align: center; margin: 30px 0;'>
            <a href='http://localhost/online_banking/auth/login.php' 
               style='background: #059669; color: white; padding: 15px 30px; text-decoration: none; 
                      border-radius: 8px; display: inline-block; font-weight: bold; font-size: 16px;
                      box-shadow: 0 4px 6px rgba(0,0,0,0.1); transition: all 0.3s ease;'>
                🚀 Access Your Account
            </a>
        </div>
                </div>
                
                <div class='footer'>
                    <div style='margin-bottom: 20px;'>
                        <strong>PremierBank Pro</strong><br>
                        Your trusted financial partner - Now with improved email templates!
                    </div>

                    <div class='footer-links'>
                        <a href='http://localhost/online_banking'>Online Banking</a>
                        <a href='http://localhost/online_banking/help-center.php'>Support Center</a>
                        <a href='mailto:<EMAIL>'>Contact Us</a>
                        <a href='mailto:<EMAIL>'>Security</a>
                    </div>
                    
                    <div style='margin: 20px 0; padding: 15px; background: #f1f5f9; border-radius: 6px; font-style: italic;'>This email contains important account information. Please keep it for your records.</div>
                    
                    <div style='margin-top: 20px; font-size: 12px; color: #9ca3af;'>
                        <p>This email was sent from a secure, monitored system. Please do not reply to this email.</p>
                        <p>&copy; 2025 PremierBank Pro. All rights reserved.</p>
                        <p>If you have questions, please contact our support team.</p>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
Body (Text):

    
    
    
        
        
        
        Welcome to Your New Account - PremierBank Pro
        
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
            
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                line-height: 1.6; color: #374151; background-color: #f9fafb; margin: 0; padding: 0;
            }
            .email-container { 
                max-width: 600px; margin: 0 auto; background-color: #ffffff; 
                box-shadow: 0 10px 25px rgba(0,0,0,0.1); border-radius: 12px; overflow: hidden;
            }
            .header { 
                background: linear-gradient(135deg, #16bb3c 0%, #6c757d 100%); 
                color: white; padding: 40px 30px; text-align: center; position: relative;
            }
            .header::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                background: url('data:image/svg+xml,') repeat;
            }
            .header-content { position: relative; z-index: 1; }
            .bank-logo {
                font-size: 24px; font-weight: 600; margin-bottom: 15px;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .header-title {
                font-size: 20px; font-weight: 500; margin-bottom: 0px;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .content { padding: 40px 30px; }
            .content h2 { color: #1f2937; margin-bottom: 20px; font-size: 20px; font-weight: 600; }
            .content h3 { color: #374151; margin: 25px 0 15px 0; font-size: 18px; font-weight: 500; }
            .content p { margin-bottom: 16px; color: #4b5563; line-height: 1.7; }
            .content ul, .content ol { margin: 16px 0; padding-left: 24px; color: #4b5563; }
            .content li { margin-bottom: 8px; line-height: 1.6; }
            
            .info-box {
                background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
                border: 1px solid #bfdbfe; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.08);
                position: relative; overflow: hidden;
            }
            .info-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #16bb3c 0%, #60a5fa 100%);
            }
            .success-box {
                background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
                border: 1px solid #a7f3d0; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(5, 150, 105, 0.08);
                position: relative; overflow: hidden;
            }
            .success-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #059669 0%, #34d399 100%);
            }
            .warning-box {
                background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
                border: 1px solid #fed7aa; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(217, 119, 6, 0.08);
                position: relative; overflow: hidden;
            }
            .warning-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #d97706 0%, #fbbf24 100%);
            }
            .danger-box {
                background: linear-gradient(135deg, #fef2f2 0%, #fef1f1 100%);
                border: 1px solid #fecaca; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(220, 38, 38, 0.08);
                position: relative; overflow: hidden;
            }
            .danger-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #dc2626 0%, #f87171 100%);
            }
            
            .account-details { 
                background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; 
                padding: 24px; margin: 24px 0; 
            }
            .detail-row { 
                display: flex; justify-content: space-between; align-items: center; 
                padding: 12px 0; border-bottom: 1px solid #e5e7eb; 
            }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: 500; color: #6b7280; }
            .detail-value { font-weight: 600; color: #1f2937; }
            .highlight { 
                background: #16bb3c; color: white; padding: 4px 12px; 
                border-radius: 6px; font-weight: 600; font-size: 14px;
            }
            
            .footer { 
                background: #f8fafc; padding: 30px; text-align: center; 
                border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; 
            }
            .footer-links { margin: 20px 0; }
            .footer-links a { 
                color: #16bb3c; text-decoration: none; margin: 0 15px; 
                font-weight: 500; 
            }
            .footer-links a:hover { text-decoration: underline; }
            
            @media (max-width: 600px) {
                .email-container { margin: 0; border-radius: 0; }
                .header, .content, .footer { padding: 30px 20px; }
                .detail-row { flex-direction: column; align-items: flex-start; }
                .detail-label { margin-bottom: 4px; }
                .bank-logo { font-size: 24px; }
                .header-title { font-size: 20px; }
            }
        
    
    
        
            
                
                    
                        PremierBank Pro
                        Welcome to Your New Account!
                    
                
                
                
                    
    
        Welcome, John!
        Your account has been successfully created and is ready to use.
    
    
    📋 Your Account Details
    
        
            Account Holder:
            John Smith
        
        
            Account Number:
            ************
        
        
            Account Type:
            Savings
        
        
            Currency:
            USD
        
        
            Current Balance:
            $1,000.00
        
        
            Username:
            testuser2025
        
        
            Status:
            ✅ Active
        
    
    
    
        🔐 Security Information
        
            Your password has been set as provided during registration
            Please log in and change your password if needed
            Two-factor authentication (OTP) is enabled for your security
            Never share your login credentials with anyone
            Always log out when using shared computers
        
    
    
    🎯 Getting Started
    
        Log in to your account using your username and password
        Complete your profile by adding additional information
        Explore features like transfers, virtual cards, and more
        Set up alerts to stay informed about your account activity
        Contact support if you need any assistance
    
    
    
        📞 Need Help?
        Our support team is here to help you get started:
        
            📧 Email: <EMAIL>
            📞 Phone: 1-800-BANKING (24/7)
            💬 Live chat available on our website
            🌐 Visit our help center for FAQs
        
    
                    
        
            
                🚀 Access Your Account
            
        
                
                
                
                    
                        PremierBank Pro
                        Your trusted financial partner - Now with improved email templates!
                    

                    
                        Online Banking
                        Support Center
                        Contact Us
                        Security
                    
                    
                    This email contains important account information. Please keep it for your records.
                    
                    
                        This email was sent from a secure, monitored system. Please do not reply to this email.
                        &copy; 2025 PremierBank Pro. All rights reserved.
                        If you have questions, please contact our support team.
                    
                
            
        
    
    
==================

