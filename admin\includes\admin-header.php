<?php
// Get bank name from super admin settings
require_once '../config/config.php';
$bank_name = getBankName();
?>
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title><?php echo isset($page_title) ? $page_title . ' - Admin - ' . $bank_name : 'Admin - ' . $bank_name; ?></title>
    
    <!-- CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler-flags.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler-payments.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler-vendors.min.css" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet"/>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        :root {
            --admin-primary: #206bc4;
            --admin-sidebar-bg: #ffffff;
            --admin-sidebar-border: #e9ecef;
            --admin-text-primary: #1e293b;
            --admin-text-secondary: #64748b;
            --admin-text-muted: #94a3b8;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 0;
        }

        /* Admin Layout */
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Admin Sidebar */
        .admin-sidebar {
            width: 280px;
            background: var(--admin-sidebar-bg);
            border-right: 1px solid var(--admin-sidebar-border);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            scroll-behavior: smooth;
        }

        /* Hide scrollbar but keep functionality */
        .admin-sidebar::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        .admin-sidebar::-webkit-scrollbar-thumb {
            background: transparent;
        }

        /* For Firefox */
        .admin-sidebar {
            scrollbar-width: none;
        }

        /* Sidebar Brand */
        .admin-sidebar-brand {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            border-bottom: 1px solid var(--admin-sidebar-border);
            margin-bottom: 1rem;
        }

        .admin-brand-link {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--admin-text-primary);
        }

        .admin-brand-icon {
            width: 32px;
            height: 32px;
            background: var(--admin-primary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 12px;
        }

        .admin-brand-text {
            font-size: 1.25rem;
            font-weight: 700;
        }

        /* Sidebar Navigation */
        .admin-sidebar-nav {
            padding: 0 1rem 1rem 1rem;
        }

        .admin-nav-section {
            margin-bottom: 1.5rem;
        }

        .admin-nav-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--admin-text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 0.75rem;
            margin-bottom: 0.5rem;
        }

        .admin-nav-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .admin-nav-item {
            margin-bottom: 0.25rem;
        }

        .admin-nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 8px;
            text-decoration: none;
            color: var(--admin-text-secondary);
            transition: all 0.2s ease;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .admin-nav-link:hover {
            background: #f1f5f9;
            color: var(--admin-text-primary);
        }

        .admin-nav-link.active {
            background: var(--admin-primary);
            color: white;
        }

        .admin-nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        /* Main Content */
        .admin-main {
            flex: 1;
            margin-left: 280px;
            min-height: 100vh;
            background: #f8fafc;
        }

        /* Top Bar */
        .admin-topbar {
            background: white;
            border-bottom: 1px solid var(--admin-sidebar-border);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-topbar-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--admin-text-primary);
            margin: 0;
        }

        .admin-topbar-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .admin-page-actions {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .admin-page-actions .btn {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: var(--admin-primary);
            color: white;
            border: 1px solid var(--admin-primary);
        }

        .btn-primary:hover {
            background: #1a5490;
            border-color: #1a5490;
            color: white;
        }

        .admin-user-menu {
            position: relative;
        }

        .admin-user-trigger {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
            border-radius: 8px;
            text-decoration: none;
            color: var(--admin-text-primary);
            transition: background 0.2s ease;
            cursor: pointer;
            border: none;
            background: none;
        }

        .admin-user-trigger:hover {
            background: #f1f5f9;
            color: var(--admin-text-primary);
        }

        .admin-user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
        }

        .admin-user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .admin-user-dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: var(--admin-text-secondary);
            transition: background 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            font-size: 0.875rem;
        }

        .admin-user-dropdown-item:hover {
            background: #f8fafc;
            color: var(--admin-text-primary);
        }

        .admin-user-dropdown-item:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        .admin-user-dropdown-item:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
        }

        .admin-user-dropdown-divider {
            height: 1px;
            background: #e5e7eb;
            margin: 0.5rem 0;
        }

        .admin-user-avatar {
            width: 36px;
            height: 36px;
            background: var(--admin-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .admin-user-info {
            text-align: right;
        }

        .admin-user-name {
            font-weight: 600;
            font-size: 0.875rem;
            margin: 0;
        }

        .admin-user-role {
            font-size: 0.75rem;
            color: var(--admin-text-muted);
            margin: 0;
        }

        /* Content Area */
        .admin-content {
            padding: 2rem;
        }

        /* Breadcrumb Styling */
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: var(--admin-text-muted);
        }

        .breadcrumb-item a {
            color: var(--admin-primary);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            text-decoration: underline;
        }

        .breadcrumb-item.active {
            color: var(--admin-text-secondary);
        }

        /* Enhanced Input Field Sizes for 24-inch Screens */
        .form-control, .form-select {
            padding: 0.75rem 1rem;
            font-size: 1rem;
            line-height: 1.5;
            min-height: 48px;
        }

        .form-control-lg, .form-select-lg {
            padding: 1rem 1.25rem;
            font-size: 1.125rem;
            line-height: 1.5;
            min-height: 56px;
        }

        .form-control-sm, .form-select-sm {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.5;
            min-height: 40px;
        }

        /* Enhanced textarea sizing */
        textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }

        textarea.form-control-lg {
            min-height: 150px;
        }

        /* Better spacing for form groups */
        .mb-3 {
            margin-bottom: 1.5rem !important;
        }

        /* Enhanced form labels */
        .form-label {
            font-weight: 600;
            margin-bottom: 0.75rem;
            font-size: 0.95rem;
        }

        /* Better button sizing */
        .btn {
            padding: 0.75rem 1.5rem;
            font-size: 0.95rem;
            font-weight: 500;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1.125rem;
        }

        /* Enhanced modal sizing for better readability */
        .modal-body {
            padding: 2rem;
        }

        .modal-header {
            padding: 1.5rem 2rem;
        }

        .modal-footer {
            padding: 1.5rem 2rem;
        }

        /* Better table cell padding */
        .table td, .table th {
            padding: 1rem 0.75rem;
            vertical-align: middle;
        }

        .table-sm td, .table-sm th {
            padding: 0.75rem 0.5rem;
        }

        /* Enhanced card padding */
        .card-body {
            padding: 2rem;
        }

        .card-header {
            padding: 1.5rem 2rem;
        }

        /* Better form hints */
        .form-hint {
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        /* Enhanced input group sizing */
        .input-group .form-control,
        .input-group .form-select {
            min-height: 48px;
        }

        .input-group-text {
            padding: 0.75rem 1rem;
            font-size: 1rem;
            min-height: 48px;
        }

        /* Better spacing for form rows */
        .row.g-2 > * {
            padding-right: 1rem;
            padding-left: 1rem;
        }

        /* Enhanced alert padding */
        .alert {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        /* Better breadcrumb spacing */
        .breadcrumb {
            padding: 1rem 0;
            margin-bottom: 1.5rem;
        }

        /* Enhanced statistics cards */
        .card-sm .card-body {
            padding: 1.5rem;
        }

        /* Better pagination sizing */
        .pagination .page-link {
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
        }

        /* Enhanced dropdown sizing */
        .dropdown-menu {
            font-size: 0.95rem;
        }

        .dropdown-item {
            padding: 0.75rem 1.5rem;
        }

        /* Better checkbox and radio sizing */
        .form-check-input {
            width: 1.25rem;
            height: 1.25rem;
            margin-top: 0.125rem;
        }

        .form-check-label {
            font-size: 0.95rem;
            margin-left: 0.5rem;
        }

        /* Enhanced badge sizing */
        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
        }

        .badge-sm {
            font-size: 0.75rem;
            padding: 0.375rem 0.5rem;
        }

        /* Better avatar sizing */
        .avatar {
            width: 2.5rem;
            height: 2.5rem;
            font-size: 1rem;
        }

        .avatar-sm {
            width: 2rem;
            height: 2rem;
            font-size: 0.875rem;
        }

        .avatar-xs {
            width: 1.5rem;
            height: 1.5rem;
            font-size: 0.75rem;
        }

        /* Enhanced responsive design for larger screens */
        @media (min-width: 1400px) {
            .container-xl {
                max-width: 1320px;
            }

            .form-control, .form-select {
                font-size: 1.05rem;
                min-height: 52px;
            }

            .form-control-lg, .form-select-lg {
                font-size: 1.2rem;
                min-height: 60px;
            }

            .card-body {
                padding: 2.5rem;
            }

            .modal-body {
                padding: 2.5rem;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .admin-sidebar.show {
                transform: translateX(0);
            }

            .admin-main {
                margin-left: 0;
            }
        }

        /* Responsive improvements for admin pages */
        @media (max-width: 1200px) {
            .form-control-lg, .form-select-lg {
                font-size: 0.9rem;
                padding: 0.5rem 0.75rem;
            }
        }

        @media (max-width: 992px) {
            .table-responsive {
                font-size: 0.85rem;
            }

            .btn-group .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.8rem;
            }

            .card-title {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 768px) {
            .form-control, .form-select {
                font-size: 0.9rem;
            }

            .table-responsive {
                font-size: 0.8rem;
            }

            .btn-sm {
                padding: 0.2rem 0.4rem;
                font-size: 0.75rem;
            }

            .alert {
                padding: 0.5rem;
                font-size: 0.85rem;
            }

            .card-body {
                padding: 0.75rem;
            }
        }

        /* Prevent horizontal overflow */
        .admin-content {
            overflow-x: hidden;
        }

        .row {
            margin-left: -0.5rem;
            margin-right: -0.5rem;
        }

        .row > * {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Admin Sidebar -->
        <div class="admin-sidebar">
            <!-- Brand -->
            <div class="admin-sidebar-brand">
                <a href="<?php echo url('admin/'); ?>" class="admin-brand-link">
                    <div class="admin-brand-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <div class="admin-brand-text"><?php echo htmlspecialchars($bank_name); ?></div>
                </a>
            </div>

            <!-- Navigation -->
            <div class="admin-sidebar-nav">
                <!-- Main Section -->
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Main</div>
                    <ul class="admin-nav-list">
                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php' && strpos($_SERVER['REQUEST_URI'], '/admin') !== false) ? 'active' : ''; ?>">
                                <i class="fas fa-tachometer-alt admin-nav-icon"></i>
                                Dashboard
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- User Management -->
                <div class="admin-nav-section">
                    <div class="admin-nav-title">User Management</div>
                    <ul class="admin-nav-list">
                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/users.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'users.php') ? 'active' : ''; ?>">
                                <i class="fas fa-users admin-nav-icon"></i>
                                Active Users
                            </a>
                        </li>
                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/pending-users.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'pending-users.php') ? 'active' : ''; ?>">
                                <i class="fas fa-exclamation-triangle admin-nav-icon" style="color: #dc3545;"></i>
                                Pending Users
                            </a>
                        </li>
                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/add-user.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'add-user.php') ? 'active' : ''; ?>">
                                <i class="fas fa-user-plus admin-nav-icon"></i>
                                Add User
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Financial -->
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Financial</div>
                    <ul class="admin-nav-list">
                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/transactions.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'transactions.php') ? 'active' : ''; ?>">
                                <i class="fas fa-exchange-alt admin-nav-icon"></i>
                                Transactions
                            </a>
                        </li>
                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/transfers.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'transfers.php') ? 'active' : ''; ?>">
                                <i class="fas fa-paper-plane admin-nav-icon"></i>
                                Transfers
                            </a>
                        </li>
                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/credit-debit.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'credit-debit.php') ? 'active' : ''; ?>">
                                <i class="fas fa-coins admin-nav-icon"></i>
                                Credit/Debit Users
                            </a>
                        </li>
                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/virtual-cards.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'virtual-cards.php') ? 'active' : ''; ?>">
                                <i class="fas fa-credit-card admin-nav-icon"></i>
                                Virtual Cards
                            </a>
                        </li>
                    </ul>
                </div>



                <!-- Management -->
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Management</div>
                    <ul class="admin-nav-list">
                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/user-status-management.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'user-status-management.php') ? 'active' : ''; ?>">
                                <i class="fas fa-user-cog admin-nav-icon"></i>
                                User Status Management
                            </a>
                        </li>

                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/bank-account-settings.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'bank-account-settings.php') ? 'active' : ''; ?>">
                                <i class="fas fa-university admin-nav-icon"></i>
                                Bank Account Settings
                            </a>
                        </li>

                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/cheque-deposits.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'cheque-deposits.php') ? 'active' : ''; ?>">
                                <i class="fas fa-file-invoice admin-nav-icon"></i>
                                Cheque Deposits
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Email & Security -->
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Email & Security</div>
                    <ul class="admin-nav-list">


                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/security-settings.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'security-settings.php') ? 'active' : ''; ?>">
                                <i class="fas fa-shield-alt admin-nav-icon"></i>
                                Security Settings
                            </a>
                        </li>

                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/email-logs.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'email-logs.php') ? 'active' : ''; ?>">
                                <i class="fas fa-history admin-nav-icon"></i>
                                Email Logs
                            </a>
                        </li>

                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/user-security-management.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'user-security-management.php') ? 'active' : ''; ?>">
                                <i class="fas fa-user-shield admin-nav-icon"></i>
                                User Security
                            </a>
                        </li>

                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/configure-2fa.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'configure-2fa.php') ? 'active' : ''; ?>">
                                <i class="fas fa-qrcode admin-nav-icon"></i>
                                2FA Configuration
                            </a>
                        </li>

                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/crypto-wallets.php'); ?>" class="admin-nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'crypto-wallets.php') ? 'active' : ''; ?>">
                                <i class="fas fa-wallet admin-nav-icon"></i>
                                Crypto Wallets
                            </a>
                        </li>
                    </ul>
                </div>



                <!-- Logout Section -->
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Account</div>
                    <ul class="admin-nav-list">
                        <li class="admin-nav-item">
                            <a href="<?php echo url('admin/logout.php'); ?>" class="admin-nav-link" style="color: #dc2626;">
                                <i class="fas fa-sign-out-alt admin-nav-icon"></i>
                                Sign Out
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="admin-main">
            <!-- Top Bar -->
            <div class="admin-topbar">
                <h1 class="admin-topbar-title"><?php echo isset($page_title) ? $page_title : 'Admin Dashboard'; ?></h1>
                <div class="admin-topbar-actions">
                    <?php if (isset($page_actions) && !empty($page_actions)): ?>
                    <div class="admin-page-actions">
                        <?php foreach ($page_actions as $action): ?>
                        <a href="<?php echo $action['url']; ?>" class="btn btn-primary">
                            <?php if (isset($action['icon'])): ?>
                            <i class="<?php echo $action['icon']; ?> me-2"></i>
                            <?php endif; ?>
                            <?php echo $action['label']; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>

                    <div class="admin-user-menu">
                        <button class="admin-user-trigger" onclick="toggleUserMenu(event)">
                            <div class="admin-user-info">
                                <div class="admin-user-name"><?php echo htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']); ?></div>
                                <div class="admin-user-role">System Administrator</div>
                            </div>
                            <div class="admin-user-avatar">
                                <?php echo strtoupper(substr($_SESSION['first_name'], 0, 1) . substr($_SESSION['last_name'], 0, 1)); ?>
                            </div>
                        </button>

                        <div class="admin-user-dropdown" id="userDropdown">
                            <a href="<?php echo url('admin/settings/'); ?>" class="admin-user-dropdown-item">
                                <i class="fas fa-user-cog"></i>
                                Profile Settings
                            </a>
                            <a href="<?php echo url('admin/security-settings.php'); ?>" class="admin-user-dropdown-item">
                                <i class="fas fa-shield-alt"></i>
                                Security Settings
                            </a>
                            <div class="admin-user-dropdown-divider"></div>
                            <a href="<?php echo url('admin/logout.php'); ?>" class="admin-user-dropdown-item">
                                <i class="fas fa-sign-out-alt"></i>
                                Sign Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="admin-content">

    <script>
        function toggleUserMenu(event) {
            event.preventDefault();
            event.stopPropagation();

            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.admin-user-menu');
            const dropdown = document.getElementById('userDropdown');

            if (!userMenu.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.remove('show');
            }
        });
    </script>
