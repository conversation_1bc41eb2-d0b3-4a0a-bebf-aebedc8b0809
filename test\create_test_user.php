<?php
require_once 'config/config.php';

$message = '';
$error = '';

if (isset($_GET['create'])) {
    try {
        $db = getDB();
        
        // Check if test user already exists
        $check_sql = "SELECT id FROM accounts WHERE username = 'testuser' OR email = '<EMAIL>'";
        $check_result = $db->query($check_sql);
        
        if ($check_result && $check_result->num_rows > 0) {
            $error = "Test user already exists!";
        } else {
            // Create test user with valid email
            $account_number = generateAccountNumber();
            $password_hash = hashPassword('password123');
            
            $sql = "INSERT INTO accounts (
                username, password, email, first_name, last_name, 
                account_number, account_type, balance, status, 
                kyc_status, is_admin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                'testuser',
                $password_hash,
                '<EMAIL>',
                'John',
                'Doe',
                $account_number,
                'savings',
                1000.00,
                'active',
                'verified',
                0
            ];
            
            $db->query($sql, $params);
            $message = "Test user created successfully!<br>
                       Username: testuser<br>
                       Password: password123<br>
                       Email: <EMAIL><br>
                       Account Number: $account_number";
        }
        
    } catch (Exception $e) {
        $error = "Error creating test user: " . $e->getMessage();
    }
}

if (isset($_GET['create_real'])) {
    try {
        $db = getDB();
        
        // Check if real test user already exists
        $check_sql = "SELECT id FROM accounts WHERE username = 'realuser' OR email = '<EMAIL>'";
        $check_result = $db->query($check_sql);
        
        if ($check_result && $check_result->num_rows > 0) {
            $error = "Real test user already exists!";
        } else {
            // Create test user with your real email
            $account_number = generateAccountNumber();
            $password_hash = hashPassword('password123');
            
            $sql = "INSERT INTO accounts (
                username, password, email, first_name, last_name, 
                account_number, account_type, balance, status, 
                kyc_status, is_admin, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                'realuser',
                $password_hash,
                '<EMAIL>',
                'Real',
                'User',
                $account_number,
                'savings',
                2500.00,
                'active',
                'verified',
                0
            ];
            
            $db->query($sql, $params);
            $message = "Real test user created successfully!<br>
                       Username: realuser<br>
                       Password: password123<br>
                       Email: <EMAIL><br>
                       Account Number: $account_number<br>
                       <strong>This user should receive real emails!</strong>";
        }
        
    } catch (Exception $e) {
        $error = "Error creating real test user: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Create Test Users</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .message { color: green; padding: 15px; background: #f0f8ff; border: 1px solid green; border-radius: 5px; margin: 15px 0; }
        .error { color: red; padding: 15px; background: #ffe0e0; border: 1px solid red; border-radius: 5px; margin: 15px 0; }
        .button { background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 10px 10px 0; }
        .button:hover { background: #005a8b; }
        .button.secondary { background: #28a745; }
        .button.secondary:hover { background: #1e7e34; }
        h1 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007cba; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Create Test Users for OTP Testing</h1>
        
        <div class="info">
            <strong>Purpose:</strong> Create test users to test the OTP system with different email scenarios.
        </div>
        
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!$message && !$error): ?>
            <h3>Option 1: Test User (Fake Email)</h3>
            <p>Creates a user with a fake email address to test email failure handling:</p>
            <a href="?create=1" class="button">Create Test User (<EMAIL>)</a>
            
            <h3>Option 2: Real User (Your Email)</h3>
            <p>Creates a user with your real email address to test actual email delivery:</p>
            <a href="?create_real=1" class="button secondary">Create Real User (<EMAIL>)</a>
            
            <div class="info">
                <strong>Note:</strong> Both users will have the password: <code>password123</code>
            </div>
        <?php endif; ?>
        
        <?php if ($message): ?>
            <h3>Next Steps:</h3>
            <p>1. <a href="setup_otp_table.php">Set up OTP table</a> (if not done already)</p>
            <p>2. <a href="auth/login.php">Test the login process</a></p>
            <p>3. <a href="admin/users.php">View users in admin panel</a></p>
        <?php endif; ?>
    </div>
</body>
</html>
