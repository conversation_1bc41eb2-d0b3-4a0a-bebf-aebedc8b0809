<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'My Accounts';
$site_name = getBankName();

// Get user account information
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get user account details
    $account_sql = "SELECT * FROM accounts WHERE id = ?";
    $account_result = $db->query($account_sql, [$user_id]);
    $account = $account_result->fetch_assoc();
    
    // Get account statistics
    $stats_sql = "SELECT 
                    COUNT(CASE WHEN transaction_type = 'credit' THEN 1 END) as total_credits,
                    COUNT(CASE WHEN transaction_type = 'debit' THEN 1 END) as total_debits,
                    SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE 0 END) as total_credit_amount,
                    SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE 0 END) as total_debit_amount
                  FROM transactions WHERE user_id = ?";
    $stats_result = $db->query($stats_sql, [$user_id]);
    $stats = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Accounts page error: " . $e->getMessage());
    $account = $_SESSION;
    $stats = ['total_credits' => 0, 'total_debits' => 0, 'total_credit_amount' => 0, 'total_debit_amount' => 0];
}

include '../includes/dashboard/header.php';
include '../includes/dashboard/sidebar.php';
?>

<div class="main-content">
    <div class="banking-container">
        <div class="banking-dashboard">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1>My Accounts</h1>
                    <p>View and manage your banking accounts</p>
                </div>
                <div class="page-actions">
                    <a href="../statements/" class="btn-outline">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                        </svg>
                        View Statements
                    </a>
                    <a href="../transfers/" class="btn-primary">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                        </svg>
                        Transfer Money
                    </a>
                </div>
            </div>

            <!-- Account Overview -->
            <div class="account-overview">
                <div class="primary-account-card">
                    <div class="account-header">
                        <div class="account-info">
                            <h2><?php echo htmlspecialchars($account['account_type'] ?? 'Savings'); ?> Account</h2>
                            <p class="account-number">Account: <?php echo htmlspecialchars($account['account_number'] ?? ''); ?></p>
                        </div>
                        <div class="account-status">
                            <span class="status-badge <?php echo strtolower($account['status'] ?? 'active'); ?>">
                                <?php echo ucfirst($account['status'] ?? 'Active'); ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="balance-section">
                        <div class="current-balance">
                            <h3>Available Balance</h3>
                            <div class="balance-amount">
                                <?php echo formatCurrency($account['balance'] ?? 0, $account['currency'] ?? 'USD'); ?>
                            </div>
                        </div>
                        
                        <div class="balance-actions">
                            <a href="../transfers/" class="action-btn primary">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                </svg>
                                Transfer
                            </a>
                            <a href="../payments/" class="action-btn secondary">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                                </svg>
                                Pay Bills
                            </a>
                            <a href="../cards/" class="action-btn secondary">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z" clip-rule="evenodd"/>
                                </svg>
                                Cards
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Details -->
            <div class="account-details-section">
                <div class="details-grid">
                    <div class="detail-card">
                        <div class="detail-header">
                            <h4>Account Information</h4>
                        </div>
                        <div class="detail-content">
                            <div class="detail-row">
                                <span class="label">Account Holder</span>
                                <span class="value"><?php echo htmlspecialchars(($account['first_name'] ?? '') . ' ' . ($account['last_name'] ?? '')); ?></span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Account Type</span>
                                <span class="value"><?php echo ucfirst($account['account_type'] ?? 'Savings'); ?></span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Currency</span>
                                <span class="value"><?php echo strtoupper($account['currency'] ?? 'USD'); ?></span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Account Status</span>
                                <span class="value status-<?php echo strtolower($account['status'] ?? 'active'); ?>">
                                    <?php echo ucfirst($account['status'] ?? 'Active'); ?>
                                </span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Member Since</span>
                                <span class="value"><?php echo date('F j, Y', strtotime($account['created_at'] ?? 'now')); ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="detail-card">
                        <div class="detail-header">
                            <h4>Transaction Summary</h4>
                        </div>
                        <div class="detail-content">
                            <div class="summary-stats">
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo number_format($stats['total_credits']); ?></div>
                                    <div class="stat-label">Credits</div>
                                    <div class="stat-amount positive">
                                        +<?php echo formatCurrency($stats['total_credit_amount'], $account['currency'] ?? 'USD'); ?>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo number_format($stats['total_debits']); ?></div>
                                    <div class="stat-label">Debits</div>
                                    <div class="stat-amount negative">
                                        -<?php echo formatCurrency($stats['total_debit_amount'], $account['currency'] ?? 'USD'); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="summary-actions">
                                <a href="../transactions/" class="summary-link">
                                    View All Transactions
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/dashboard/footer.php'; ?>
