<?php
/**
 * Super Admin Google Authenticator Verification
 * Handles 2FA verification for super administrators
 */

session_start();

// Include required files
require_once 'includes/auth.php';

// Redirect if not in 2FA pending state
if (!isset($_SESSION['super_admin_logged_in']) || !isset($_SESSION['super_admin_2fa_pending'])) {
    header('Location: login.php');
    exit;
}

// Redirect if already verified
if (isset($_SESSION['super_admin_2fa_verified']) && $_SESSION['super_admin_2fa_verified'] === true) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';
$username = $_SESSION['super_admin_username'] ?? 'superadmin';

// Check if account is locked
if (isSuperAdmin2FALocked($username)) {
    $error = 'Your account is temporarily locked due to too many failed attempts. Please try again later.';
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isSuperAdmin2FALocked($username)) {
    $verification_code = trim($_POST['verification_code'] ?? '');
    
    if (empty($verification_code)) {
        $error = 'Please enter the verification code from your Google Authenticator app.';
    } elseif (!preg_match('/^\d{6}$/', $verification_code)) {
        $error = 'Please enter a valid 6-digit verification code.';
    } else {
        // Get 2FA settings
        $settings = getSuperAdmin2FASettings($username);
        
        if (!$settings || !$settings['google_2fa_enabled'] || !$settings['google_2fa_secret']) {
            $error = 'Google Authenticator is not properly configured. Please contact system administrator.';
        } else {
            // Verify the code
            if (verifySuperAdmin2FACode($verification_code, $settings['google_2fa_secret'], $username)) {
                // 2FA verification successful
                $_SESSION['super_admin_2fa_verified'] = true;
                unset($_SESSION['super_admin_2fa_pending']);
                
                logSuperAdminAction('2fa_verification_success', 'Super admin 2FA verification completed');

                header('Location: dashboard.php');
                exit;
            } else {
                $error = 'Invalid verification code. Please check your Google Authenticator app and try again.';
            }
        }
    }
}

// Get remaining lockout time if locked
$lockout_time_remaining = '';
if (isSuperAdmin2FALocked($username)) {
    $settings = getSuperAdmin2FASettings($username);
    if ($settings && $settings['locked_until']) {
        $remaining_seconds = strtotime($settings['locked_until']) - time();
        if ($remaining_seconds > 0) {
            $minutes = ceil($remaining_seconds / 60);
            $lockout_time_remaining = $minutes . ' minute' . ($minutes > 1 ? 's' : '');
        }
    }
}

$page_title = 'Two-Factor Authentication';
$page_subtitle = 'Enter your Google Authenticator code to continue';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?> - Super Admin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .verify-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }

        .verify-header {
            background: linear-gradient(135deg, #4361ee 0%, #3730a3 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .verify-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .verify-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin: 10px 0 0 0;
        }

        .verify-body {
            padding: 40px 30px;
        }

        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 1.5rem;
            text-align: center;
            letter-spacing: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #4361ee;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
        }

        .btn-verify {
            background: linear-gradient(135deg, #4361ee 0%, #3730a3 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-verify:hover {
            background: linear-gradient(135deg, #3730a3 0%, #312e81 100%);
            transform: translateY(-2px);
            color: white;
        }

        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 25px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .card-header {
            border-radius: 15px 15px 0 0 !important;
            border: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="verify-container">
        <div class="verify-header">
            <h1 class="verify-title">
                <i class="fas fa-shield-alt"></i> Two-Factor Authentication
            </h1>
            <p class="verify-subtitle"><?php echo htmlspecialchars($page_subtitle); ?></p>
        </div>

        <div class="verify-body">
<!-- Success/Error Messages -->
<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <?php if ($lockout_time_remaining): ?>
            <br><small>Account will be unlocked in <?php echo htmlspecialchars($lockout_time_remaining); ?>.</small>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>
                
            <!-- Main 2FA Verification -->
            <?php if (!isSuperAdmin2FALocked($username)): ?>
                <div class="alert alert-info">
                    <h6><i class="fas fa-mobile-alt"></i> Verification Steps</h6>
                    <ol class="mb-0">
                        <li>Open your Google Authenticator app</li>
                        <li>Find the "SecureBank Online - Super Admin" entry</li>
                        <li>Enter the 6-digit code shown in the app</li>
                    </ol>
                </div>

                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="verification_code" class="form-label">
                            <i class="fas fa-key"></i> Verification Code
                        </label>
                        <input type="text"
                               class="form-control"
                               id="verification_code"
                               name="verification_code"
                               placeholder="000000"
                               maxlength="6"
                               pattern="\d{6}"
                               autocomplete="off"
                               required>
                        <div class="form-text">Enter the 6-digit code from your Google Authenticator app</div>
                    </div>

                    <button type="submit" class="btn btn-verify">
                        <i class="fas fa-unlock"></i> Verify & Continue
                    </button>
                </form>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-clock"></i>
                    <strong>Account Temporarily Locked</strong><br>
                    Too many failed verification attempts. Please wait <?php echo htmlspecialchars($lockout_time_remaining); ?> before trying again.
                </div>

                <a href="login.php" class="btn btn-secondary w-100">
                    <i class="fas fa-arrow-left"></i> Back to Login
                </a>
            <?php endif; ?>

            <div class="mt-4 text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    This additional security step helps protect your super admin account.
                </small>
            </div>

            <div class="mt-3 text-center">
                <a href="logout.php" class="text-muted text-decoration-none me-3">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
                <a href="login.php" class="text-muted text-decoration-none">
                    <i class="fas fa-arrow-left"></i> Back to Login
                </a>
            </div>
        </div>
    </div>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // Auto-focus on verification code input
    const verificationInput = document.getElementById('verification_code');
    if (verificationInput) {
        verificationInput.focus();

        // Auto-submit when 6 digits are entered
        verificationInput.addEventListener('input', function(e) {
            if (e.target.value.length === 6) {
                // Small delay to allow user to see the complete code
                setTimeout(() => {
                    e.target.form.submit();
                }, 500);
            }
        });

        // Only allow numeric input
        verificationInput.addEventListener('keypress', function(e) {
            if (!/\d/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
                e.preventDefault();
            }
        });
    }
    </script>
</body>
</html>
