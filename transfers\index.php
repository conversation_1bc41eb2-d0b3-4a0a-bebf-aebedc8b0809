<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Transfer Money';
$site_name = getBankName();

// Handle transfer form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];
        
        $recipient_account = sanitizeInput($_POST['recipient_account']);
        $amount = floatval($_POST['amount']);
        $description = sanitizeInput($_POST['description']);
        $transfer_type = sanitizeInput($_POST['transfer_type'] ?? 'internal');
        
        // Validation
        if (empty($recipient_account) || $amount <= 0) {
            throw new Exception('Please provide valid recipient account and amount.');
        }
        
        // Check user balance
        $balance_sql = "SELECT balance FROM accounts WHERE id = ?";
        $balance_result = $db->query($balance_sql, [$user_id]);
        $user_balance = $balance_result->fetch_assoc()['balance'];
        
        if ($amount > $user_balance) {
            throw new Exception('Insufficient balance for this transfer.');
        }
        
        // Create transfer record
        $transfer_sql = "INSERT INTO transfers (sender_id, recipient_account, amount, description, transfer_type, status) 
                        VALUES (?, ?, ?, ?, ?, 'pending')";
        $result = $db->query($transfer_sql, [$user_id, $recipient_account, $amount, $description, $transfer_type]);
        
        if ($result) {
            $success_message = 'Transfer initiated successfully! It will be processed shortly.';
        } else {
            throw new Exception('Failed to initiate transfer.');
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user's beneficiaries
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    $beneficiaries_sql = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY is_favorite DESC, name ASC LIMIT 10";
    $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
    $beneficiaries = [];
    while ($beneficiary = $beneficiaries_result->fetch_assoc()) {
        $beneficiaries[] = $beneficiary;
    }
    
    // Get user balance
    $balance_sql = "SELECT balance, currency FROM accounts WHERE id = ?";
    $balance_result = $db->query($balance_sql, [$user_id]);
    $account_info = $balance_result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Transfers page error: " . $e->getMessage());
    $beneficiaries = [];
    $account_info = ['balance' => 0, 'currency' => 'USD'];
}

include '../includes/dashboard/header.php';
include '../includes/dashboard/sidebar.php';
?>

<div class="main-content">
    <div class="banking-container">
        <div class="banking-dashboard">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1>Transfer Money</h1>
                    <p>Send money to accounts and beneficiaries</p>
                </div>
                <div class="page-actions">
                    <a href="../beneficiaries/" class="btn-outline">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                        Manage Beneficiaries
                    </a>
                    <a href="../transactions/" class="btn-primary">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                        Transaction History
                    </a>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Transfer Form -->
            <div class="transfer-container">
                <div class="transfer-form-section">
                    <div class="form-card">
                        <div class="form-header">
                            <h3>Send Money</h3>
                            <p>Available Balance: <?php echo formatCurrency($account_info['balance'], $account_info['currency']); ?></p>
                        </div>
                        
                        <form method="POST" class="transfer-form">
                            <div class="form-group">
                                <label for="recipient_account">Recipient Account Number</label>
                                <input type="text" id="recipient_account" name="recipient_account" class="form-control" 
                                       placeholder="Enter account number" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="amount">Amount</label>
                                <div class="amount-input">
                                    <span class="currency-symbol"><?php echo $account_info['currency']; ?></span>
                                    <input type="number" id="amount" name="amount" class="form-control" 
                                           placeholder="0.00" step="0.01" min="0.01" max="<?php echo $account_info['balance']; ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="transfer_type">Transfer Type</label>
                                <select id="transfer_type" name="transfer_type" class="form-control">
                                    <option value="internal">Internal Transfer</option>
                                    <option value="external">External Transfer</option>
                                    <option value="international">International Transfer</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="description">Description (Optional)</label>
                                <input type="text" id="description" name="description" class="form-control" 
                                       placeholder="Payment description">
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn-secondary" onclick="clearForm()">Clear</button>
                                <button type="submit" class="btn-primary">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                    </svg>
                                    Send Money
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Quick Transfer to Beneficiaries -->
                <?php if (!empty($beneficiaries)): ?>
                <div class="quick-transfer-section">
                    <div class="section-header">
                        <h3>Quick Transfer</h3>
                        <p>Send money to your saved beneficiaries</p>
                    </div>
                    
                    <div class="beneficiaries-grid">
                        <?php foreach ($beneficiaries as $beneficiary): ?>
                            <div class="beneficiary-quick-card" onclick="selectBeneficiary('<?php echo htmlspecialchars($beneficiary['account_number']); ?>', '<?php echo htmlspecialchars($beneficiary['name']); ?>')">
                                <div class="beneficiary-avatar">
                                    <?php echo strtoupper(substr($beneficiary['name'], 0, 2)); ?>
                                </div>
                                <div class="beneficiary-info">
                                    <h4><?php echo htmlspecialchars($beneficiary['name']); ?></h4>
                                    <p><?php echo htmlspecialchars($beneficiary['bank_name']); ?></p>
                                    <span class="account-number">•••• <?php echo substr($beneficiary['account_number'], -4); ?></span>
                                </div>
                                <?php if ($beneficiary['is_favorite']): ?>
                                    <div class="favorite-badge">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function selectBeneficiary(accountNumber, name) {
    document.getElementById('recipient_account').value = accountNumber;
    document.getElementById('recipient_account').focus();
}

function clearForm() {
    document.querySelector('.transfer-form').reset();
}
</script>

<?php include '../includes/dashboard/footer.php'; ?>
