<?php
/**
 * Super Admin Google Authenticator Setup and Management
 * Handles 2FA configuration for super administrators
 */

session_start();

// Include required files
require_once 'includes/auth.php';

// Require super admin authentication
requireSuperAdminAuth();

$error = '';
$success = '';
$qr_code_url = '';
$secret = '';
$backup_codes = [];
$username = $_SESSION['super_admin_username'] ?? 'superadmin';

// Get current 2FA settings
$current_settings = getSuperAdmin2FASettings($username);
$is_2fa_enabled = $current_settings && $current_settings['google_2fa_enabled'];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'generate_qr':
                // Generate new secret and QR code
                $secret = generateSuperAdmin2FASecret();
                $qr_code_url = generateSuperAdmin2FAQRCode($secret, $username);
                
                // Store secret temporarily in session
                $_SESSION['temp_super_admin_2fa_secret'] = $secret;
                
                logSuperAdmin2FAAction($username, 'qr_generated', 'QR code generated for 2FA setup');
                break;
                
            case 'verify_setup':
                $verification_code = trim($_POST['verification_code'] ?? '');
                $temp_secret = $_SESSION['temp_super_admin_2fa_secret'] ?? '';
                
                if (empty($verification_code)) {
                    $error = 'Please enter the verification code from your Google Authenticator app.';
                } elseif (!preg_match('/^\d{6}$/', $verification_code)) {
                    $error = 'Please enter a valid 6-digit verification code.';
                } elseif (empty($temp_secret)) {
                    $error = 'Setup session expired. Please generate a new QR code.';
                } else {
                    $google2fa = getGoogle2FA();
                    if ($google2fa->verifyKey($temp_secret, $verification_code)) {
                        // Generate backup codes
                        $backup_codes = generateSuperAdmin2FABackupCodes(10);
                        
                        // Save 2FA settings
                        if (saveSuperAdmin2FASettings($username, $temp_secret, $backup_codes)) {
                            unset($_SESSION['temp_super_admin_2fa_secret']);
                            $success = 'Google Authenticator has been successfully enabled! Please save your backup codes in a secure location.';
                            $current_settings = getSuperAdmin2FASettings($username);
                            $is_2fa_enabled = true;
                        } else {
                            $error = 'Failed to save 2FA settings. Please try again.';
                        }
                    } else {
                        $error = 'Invalid verification code. Please check your Google Authenticator app and try again.';
                        // Keep the QR code visible for retry
                        $secret = $temp_secret;
                        $qr_code_url = generateSuperAdmin2FAQRCode($secret, $username);
                    }
                }
                break;
                
            case 'disable_2fa':
                $confirmation = $_POST['disable_confirmation'] ?? '';
                if ($confirmation === 'DISABLE') {
                    if (disableSuperAdmin2FA($username)) {
                        unset($_SESSION['temp_super_admin_2fa_secret']);
                        $success = 'Google Authenticator has been disabled for your account.';
                        $current_settings = getSuperAdmin2FASettings($username);
                        $is_2fa_enabled = false;
                    } else {
                        $error = 'Failed to disable 2FA. Please try again.';
                    }
                } else {
                    $error = 'Please type "DISABLE" to confirm disabling 2FA.';
                }
                break;
                
            case 'regenerate_backup_codes':
                if ($is_2fa_enabled) {
                    $new_backup_codes = generateSuperAdmin2FABackupCodes(10);
                    $backup_codes_json = json_encode($new_backup_codes);
                    
                    require_once __DIR__ . '/../config/database.php';
                    $db = getDB();
                    $sql = "UPDATE super_admin_2fa_settings SET backup_codes = ? WHERE super_admin_username = ?";
                    
                    if ($db->query($sql, [$backup_codes_json, $username])) {
                        $backup_codes = $new_backup_codes;
                        $success = 'New backup codes have been generated. Please save them in a secure location.';
                        logSuperAdmin2FAAction($username, 'backup_codes_regenerated', 'Backup codes regenerated');
                    } else {
                        $error = 'Failed to regenerate backup codes. Please try again.';
                    }
                } else {
                    $error = '2FA must be enabled to regenerate backup codes.';
                }
                break;
        }
    } catch (Exception $e) {
        error_log("Super Admin 2FA Setup Error: " . $e->getMessage());
        $error = 'An error occurred while processing your request. Please try again.';
    }
}

// Get existing backup codes if 2FA is enabled
if ($is_2fa_enabled && empty($backup_codes) && !empty($current_settings['backup_codes'])) {
    $backup_codes = json_decode($current_settings['backup_codes'], true) ?: [];
}

$page_title = 'Two-Factor Authentication';
$page_subtitle = 'Configure Google Authenticator for enhanced security';

// Include header
include 'includes/header.php';
?>

<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="dashboard.php">Super Admin</a>
        </li>
        <li class="breadcrumb-item active" aria-current="page">2FA Setup</li>
    </ol>
</nav>
                
<!-- Success/Error Messages -->
<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>
                
<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-mobile-alt"></i> Google Authenticator Setup
                </h5>
            </div>
                            <div class="card-body">
                                <?php if (!$is_2fa_enabled): ?>
                                    <?php if (empty($qr_code_url)): ?>
                                    <!-- Initial setup -->
                                    <div class="alert alert-info border-0">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Enhanced Security:</strong> Enable Google Authenticator to add an extra layer of security to your super admin account.
                                    </div>
                                        
                                    <form method="POST" action="">
                                        <input type="hidden" name="action" value="generate_qr">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-qrcode"></i> Generate QR Code
                                        </button>
                                    </form>
                                    <?php else: ?>
                                        <!-- QR Code display and verification -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Step 1: Scan QR Code</h6>
                                                <div class="text-center mb-3">
                                                    <img src="<?php echo htmlspecialchars($qr_code_url); ?>" 
                                                         alt="QR Code for Google Authenticator" 
                                                         class="img-fluid border rounded"
                                                         style="max-width: 200px;">
                                                </div>
                                                <p class="small text-muted">
                                                    Scan this QR code with your Google Authenticator app.
                                                </p>
                                                
                                                <h6>Manual Entry</h6>
                                                <p class="small">If you can't scan the QR code, enter this secret manually:</p>
                                                <div class="input-group">
                                                    <input type="text" class="form-control font-monospace" 
                                                           value="<?php echo htmlspecialchars($secret); ?>" readonly>
                                                    <button class="btn btn-outline-secondary" type="button" 
                                                            onclick="copyToClipboard('<?php echo htmlspecialchars($secret); ?>')">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <h6>Step 2: Verify Setup</h6>
                                                <form method="POST" action="">
                                                    <input type="hidden" name="action" value="verify_setup">
                                                    <div class="mb-3">
                                                        <label for="verification_code" class="form-label">Verification Code</label>
                                                        <input type="text" class="form-control" id="verification_code" 
                                                               name="verification_code" placeholder="000000" 
                                                               maxlength="6" pattern="\d{6}" required>
                                                        <div class="form-text">Enter the 6-digit code from your app</div>
                                                    </div>
                                                    <button type="submit" class="btn btn-success">
                                                        <i class="fas fa-check"></i> Verify & Enable
                                                    </button>
                                                    <a href="2fa-setup.php" class="btn btn-secondary">
                                                        <i class="fas fa-times"></i> Cancel
                                                    </a>
                                                </form>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                <!-- 2FA is enabled -->
                                <div class="alert alert-success border-0">
                                    <i class="fas fa-check-circle"></i>
                                    <strong>Google Authenticator is enabled</strong> for your super admin account.
                                </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Backup Codes</h6>
                                            <p class="text-muted">Use these codes if you lose access to your authenticator app:</p>
                                            
                                            <?php if (!empty($backup_codes)): ?>
                                                <div class="backup-codes bg-light p-3 rounded font-monospace">
                                                    <?php foreach ($backup_codes as $code): ?>
                                                        <div><?php echo htmlspecialchars($code); ?></div>
                                                    <?php endforeach; ?>
                                                </div>
                                                <div class="mt-2">
                                                    <button class="btn btn-sm btn-outline-primary" 
                                                            onclick="printBackupCodes()">
                                                        <i class="fas fa-print"></i> Print Codes
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-secondary" 
                                                            onclick="copyBackupCodes()">
                                                        <i class="fas fa-copy"></i> Copy Codes
                                                    </button>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <form method="POST" action="" class="mt-3">
                                                <input type="hidden" name="action" value="regenerate_backup_codes">
                                                <button type="submit" class="btn btn-warning btn-sm">
                                                    <i class="fas fa-sync"></i> Generate New Backup Codes
                                                </button>
                                            </form>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <h6>Disable 2FA</h6>
                                            <p class="text-muted">
                                                <strong>Warning:</strong> Disabling 2FA will reduce your account security.
                                            </p>
                                            
                                            <form method="POST" action="" onsubmit="return confirmDisable()">
                                                <input type="hidden" name="action" value="disable_2fa">
                                                <div class="mb-3">
                                                    <label for="disable_confirmation" class="form-label">
                                                        Type "DISABLE" to confirm:
                                                    </label>
                                                    <input type="text" class="form-control" id="disable_confirmation" 
                                                           name="disable_confirmation" placeholder="DISABLE" required>
                                                </div>
                                                <button type="submit" class="btn btn-danger">
                                                    <i class="fas fa-times"></i> Disable 2FA
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> About Google Authenticator
                </h6>
            </div>
                            <div class="card-body">
                                <p class="small">
                                    Google Authenticator provides an additional layer of security by requiring 
                                    a time-based code from your mobile device when logging in.
                                </p>
                                
                                <h6>Setup Instructions:</h6>
                                <ol class="small">
                                    <li>Install Google Authenticator on your mobile device</li>
                                    <li>Scan the QR code or enter the secret manually</li>
                                    <li>Enter the 6-digit code to verify setup</li>
                                    <li>Save your backup codes in a secure location</li>
                                </ol>
                                
                                <div class="mt-3">
                                    <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" 
                                       target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fab fa-android"></i> Android
                                    </a>
                                    <a href="https://apps.apple.com/app/google-authenticator/id388497605" 
                                       target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fab fa-apple"></i> iOS
                                    </a>
                                </div>
                            </div>
                        </div>
                        
        <?php if ($is_2fa_enabled): ?>
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i> Security Status
                </h6>
            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span class="small">2FA Enabled</span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-key text-primary me-2"></i>
                                    <span class="small"><?php echo count($backup_codes); ?> Backup Codes</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock text-info me-2"></i>
                                    <span class="small">
                                        Setup: <?php echo date('M j, Y', strtotime($current_settings['created_at'])); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Action Buttons -->
<div class="d-flex justify-content-between">
    <a href="dashboard.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>

    <?php if (!$is_2fa_enabled): ?>
        <a href="setup-2fa.php" class="btn btn-outline-info">
            <i class="fas fa-cog"></i> Setup Requirements
        </a>
    <?php endif; ?>
</div>
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        alert('Secret copied to clipboard!');
    });
}

function copyBackupCodes() {
    const codes = <?php echo json_encode($backup_codes); ?>;
    const text = codes.join('\n');
    navigator.clipboard.writeText(text).then(() => {
        alert('Backup codes copied to clipboard!');
    });
}

function printBackupCodes() {
    const codes = <?php echo json_encode($backup_codes); ?>;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head><title>Super Admin Backup Codes</title></head>
        <body>
            <h2>Super Admin 2FA Backup Codes</h2>
            <p>Keep these codes in a safe place. Each code can only be used once.</p>
            <div style="font-family: monospace; line-height: 1.5;">
                ${codes.map(code => `<div>${code}</div>`).join('')}
            </div>
            <p><small>Generated: ${new Date().toLocaleString()}</small></p>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

function confirmDisable() {
    return confirm('Are you sure you want to disable Two-Factor Authentication? This will reduce your account security.');
}

// Auto-focus and format verification code input
const verificationInput = document.getElementById('verification_code');
if (verificationInput) {
    verificationInput.focus();

    verificationInput.addEventListener('keypress', function(e) {
        if (!/\d/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
            e.preventDefault();
        }
    });
}
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
