name: tests

on:
  push:
  pull_request:

jobs:
  tests:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        php: ['7.0', '7.1', '7.2', '7.3', '7.4', '8.0']

    name: PHP ${{ matrix.php }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: curl
          tools: composer:v2
          coverage: none

      - name: Install PHP 7 dependencies
        run: composer update --prefer-dist --no-interaction --no-progress
        if: "matrix.php != '8.0'"

      - name: Install PHP 8 dependencies
        run: composer update --prefer-dist --no-interaction --no-progress --ignore-platform-reqs
        if: "matrix.php == '8.0'"

      - name: Execute tests
        run: vendor/bin/phpunit -c tests/phpunit.xml.dist
