<?php
/**
 * Browser Test for View User Page
 * Tests the actual page rendering and functionality in browser
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include configuration
require_once '../../config/config.php';

// Check if we have a test user
$test_user_id = null;
try {
    $db = getDB();
    $result = $db->query("SELECT id FROM accounts WHERE is_admin = 0 LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $test_user_id = $user['id'];
    }
} catch (Exception $e) {
    // Will create a test user if none exists
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View User Browser Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
        .test-results { margin-top: 20px; }
        iframe { width: 100%; height: 600px; border: 1px solid #ddd; border-radius: 4px; }
        .component-test { border: 1px solid #e9ecef; margin: 10px 0; padding: 15px; border-radius: 4px; }
        .component-test h4 { margin: 0 0 10px 0; color: #495057; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌐 View User Browser Test Suite</h1>
        <p><strong>Test Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <?php if ($test_user_id): ?>
        <div class="success">
            ✅ Test user found (ID: <?php echo $test_user_id; ?>). Ready to run tests.
        </div>
        <?php else: ?>
        <div class="warning">
            ⚠️ No test user found. Creating a test user first...
        </div>
        <?php
        // Create a test user
        try {
            $db = getDB();
            $test_data = [
                'account_number' => 'TEST' . time(),
                'username' => 'testuser_' . time(),
                'password' => password_hash('test123', PASSWORD_DEFAULT),
                'email' => 'test' . time() . '@example.com',
                'first_name' => 'Test',
                'last_name' => 'User',
                'phone' => '+**********',
                'address' => '123 Test Street, Test City',
                'date_of_birth' => '1990-01-01',
                'occupation' => 'Software Tester',
                'gender' => 'male',
                'marital_status' => 'single',
                'account_type' => 'savings',
                'balance' => 1000.00,
                'status' => 'active',
                'kyc_status' => 'verified'
            ];
            
            $query = "INSERT INTO accounts (" . implode(', ', array_keys($test_data)) . ") VALUES (" . str_repeat('?,', count($test_data) - 1) . "?)";
            $stmt = $db->prepare($query);
            $stmt->execute(array_values($test_data));
            $test_user_id = $db->lastInsertId();
            
            echo "<div class='success'>✅ Test user created successfully (ID: {$test_user_id})</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Failed to create test user: " . $e->getMessage() . "</div>";
        }
        ?>
        <?php endif; ?>

        <div class="test-section">
            <h2>🧪 Component Tests</h2>
            
            <div class="component-test">
                <h4>1. File Existence Test</h4>
                <?php
                $files_to_check = [
                    'Original view-user.php' => '../../admin/view-user.php',
                    'New view-user-new.php' => '../../admin/view-user-new.php',
                    'Data loader' => '../../admin/includes/user-data-loader.php',
                    'CSS file' => '../../assets/admin/css/view-user.css',
                    'JS file' => '../../assets/admin/js/view-user.js',
                    'User header component' => '../../includes/components/user-header.php',
                    'Overview cards component' => '../../includes/components/user-overview-cards.php',
                    'Personal info component' => '../../includes/components/user-personal-info.php',
                    'Account info component' => '../../includes/components/user-account-info.php',
                    'Security section component' => '../../includes/components/user-security-section.php',
                    'Financial section component' => '../../includes/components/user-financial-section.php',
                    'Cards & crypto component' => '../../includes/components/user-cards-crypto-section.php',
                    'Modals component' => '../../includes/components/user-modals.php'
                ];
                
                $all_exist = true;
                foreach ($files_to_check as $name => $file) {
                    if (file_exists($file)) {
                        $size = filesize($file);
                        echo "<div class='success'>✅ {$name}: EXISTS ({$size} bytes)</div>";
                    } else {
                        echo "<div class='error'>❌ {$name}: MISSING</div>";
                        $all_exist = false;
                    }
                }
                
                if ($all_exist) {
                    echo "<div class='success'><strong>All component files exist!</strong></div>";
                } else {
                    echo "<div class='error'><strong>Some files are missing!</strong></div>";
                }
                ?>
            </div>

            <div class="component-test">
                <h4>2. CSS Validation Test</h4>
                <?php
                $css_file = '../../assets/admin/css/view-user.css';
                if (file_exists($css_file)) {
                    $css_content = file_get_contents($css_file);
                    $open_braces = substr_count($css_content, '{');
                    $close_braces = substr_count($css_content, '}');
                    
                    if ($open_braces === $close_braces) {
                        echo "<div class='success'>✅ CSS syntax valid: {$open_braces} rule blocks</div>";
                    } else {
                        echo "<div class='error'>❌ CSS syntax error: {$open_braces} open braces, {$close_braces} close braces</div>";
                    }
                    
                    // Check for key CSS classes
                    $required_classes = ['.credit-card', '.fade-in', '.loading-spinner', '.user-avatar-large'];
                    foreach ($required_classes as $class) {
                        if (strpos($css_content, $class) !== false) {
                            echo "<div class='success'>✅ Found CSS class: {$class}</div>";
                        } else {
                            echo "<div class='warning'>⚠️ Missing CSS class: {$class}</div>";
                        }
                    }
                } else {
                    echo "<div class='error'>❌ CSS file not found</div>";
                }
                ?>
            </div>

            <div class="component-test">
                <h4>3. JavaScript Validation Test</h4>
                <?php
                $js_file = '../../assets/admin/js/view-user.js';
                if (file_exists($js_file)) {
                    $js_content = file_get_contents($js_file);
                    
                    // Check for key functions
                    $required_functions = ['generateOTPForUser', 'showOTPHistory', 'initializePageAnimations', 'copyToClipboard'];
                    foreach ($required_functions as $func) {
                        if (strpos($js_content, $func) !== false) {
                            echo "<div class='success'>✅ Found JS function: {$func}</div>";
                        } else {
                            echo "<div class='warning'>⚠️ Missing JS function: {$func}</div>";
                        }
                    }
                    
                    // Check for syntax issues (basic)
                    $open_braces = substr_count($js_content, '{');
                    $close_braces = substr_count($js_content, '}');
                    
                    if ($open_braces === $close_braces) {
                        echo "<div class='success'>✅ JavaScript syntax appears valid</div>";
                    } else {
                        echo "<div class='warning'>⚠️ Possible JavaScript syntax issue: mismatched braces</div>";
                    }
                } else {
                    echo "<div class='error'>❌ JavaScript file not found</div>";
                }
                ?>
            </div>
        </div>

        <?php if ($test_user_id): ?>
        <div class="test-section">
            <h2>🌐 Live Page Tests</h2>
            <p>Click the buttons below to test different aspects of the view-user page:</p>
            
            <button class="test-button" onclick="testOriginalPage()">Test Original view-user.php</button>
            <button class="test-button" onclick="testNewPage()">Test New view-user-new.php</button>
            <button class="test-button" onclick="testComponentsOnly()">Test Components Only</button>
            <button class="test-button" onclick="testResponsive()">Test Responsive Design</button>
            
            <div class="test-results" id="testResults">
                <div class="info">Select a test above to begin...</div>
            </div>
        </div>
        <?php endif; ?>

        <div class="test-section">
            <h2>📋 Test Summary</h2>
            <div class="info">
                <h4>What This Test Covers:</h4>
                <ul>
                    <li>✅ File existence and structure validation</li>
                    <li>✅ CSS syntax and required classes</li>
                    <li>✅ JavaScript function availability</li>
                    <li>🌐 Live page rendering (if test user available)</li>
                    <li>📱 Responsive design testing</li>
                    <li>🔧 Component integration testing</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function testOriginalPage() {
            const results = document.getElementById('testResults');
            results.innerHTML = `
                <h3>Testing Original view-user.php</h3>
                <iframe src="../../admin/view-user.php?id=<?php echo $test_user_id; ?>" 
                        onload="checkPageLoad(this, 'Original Page')"
                        onerror="showError('Original Page')"></iframe>
            `;
        }

        function testNewPage() {
            const results = document.getElementById('testResults');
            results.innerHTML = `
                <h3>Testing New view-user-new.php</h3>
                <iframe src="../../admin/view-user-new.php?id=<?php echo $test_user_id; ?>" 
                        onload="checkPageLoad(this, 'New Page')"
                        onerror="showError('New Page')"></iframe>
            `;
        }

        function testComponentsOnly() {
            const results = document.getElementById('testResults');
            results.innerHTML = `
                <h3>Testing Individual Components</h3>
                <div class="info">This would test each component individually. Implementation needed.</div>
            `;
        }

        function testResponsive() {
            const results = document.getElementById('testResults');
            results.innerHTML = `
                <h3>Testing Responsive Design</h3>
                <div style="width: 320px; height: 400px; border: 1px solid #ddd; margin: 10px 0;">
                    <iframe src="../../admin/view-user-new.php?id=<?php echo $test_user_id; ?>" 
                            style="width: 100%; height: 100%; transform: scale(0.5); transform-origin: 0 0;"
                            onload="checkPageLoad(this, 'Mobile View')"></iframe>
                </div>
            `;
        }

        function checkPageLoad(iframe, testName) {
            try {
                const doc = iframe.contentDocument || iframe.contentWindow.document;
                if (doc.readyState === 'complete') {
                    console.log(`${testName} loaded successfully`);
                    // Could add more checks here
                }
            } catch (e) {
                console.error(`Error checking ${testName}:`, e);
            }
        }

        function showError(testName) {
            console.error(`${testName} failed to load`);
        }
    </script>
</body>
</html>
