<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    bootstrap="./tests/Bootstrap.php"
    cacheResult="false"
    forceCoversAnnotation="false"
    stopOnFailure="true"
    convertDeprecationsToExceptions="true"
    colors="true"
    verbose="true"
>
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">library/</directory>
        </include>
        <exclude>
            <directory>library/Mockery/Adapter/Phpunit/Legacy</directory>
            <file>library/Mockery/Adapter/Phpunit/TestListener.php</file>
            <file>library/Mockery/Adapter/Phpunit/MockeryPHPUnitIntegrationAssertPostConditions.php</file>
            <file>library/Mockery/Adapter/Phpunit/MockeryTestCaseSetUp.php</file>
        </exclude>
        <report>
            <html outputDirectory=".cache/phpunit/coverage-html"/>
            <clover outputFile=".cache/phpunit/clover.xml"/>
            <text outputFile=".cache/phpunit/coverage.txt"/>
        </report>
    </coverage>
    <testsuites>
        <testsuite name="default">
            <directory>tests</directory>
            <exclude>fixtures</exclude>
            <exclude>tests/Fixture</exclude>
            <exclude>tests/PHP*</exclude>
            <exclude>tests/Unit/PHP*</exclude>
            <exclude>tests/Unit/Regression</exclude>
        </testsuite>
        <testsuite name="versioned">
            <!--<directory phpVersion="7.3.0-dev">tests/Unit/*</directory> -->
            <!--<directory phpVersion="7.3.0-dev">tests</directory> -->
            <!--<directory phpVersion="7.4.0-dev">tests/PHP74</directory>-->
            <!--<directory phpVersion="7.4.0-dev">tests/Unit/PHP74</directory>-->
            <!--<directory phpVersion="8.0.0-dev">tests/Unit/PHP80</directory>-->
            <!--<directory phpVersion="8.3.0-dev">tests/PHP83</directory>-->
            <!--<directory phpVersion="8.4.0-dev">tests/PHP84</directory>-->
            <!--<directory phpVersion="8.4.0-dev">tests/Unit/PHP84</directory>-->
            <directory phpVersion="8.0.0-dev">tests/PHP80</directory>
            <directory phpVersion="8.0.0-dev">tests/Unit/PHP80</directory>
            <directory phpVersion="8.0.0-dev">tests/Unit/Regression</directory>
            <directory phpVersion="8.1.0-dev">tests/PHP81</directory>
            <directory phpVersion="8.1.0-dev">tests/Unit/PHP81</directory>
            <directory phpVersion="8.2.0-dev">tests/PHP82</directory>
            <directory phpVersion="8.2.0-dev">tests/Unit/PHP82</directory>
            <directory phpVersion="8.3.0-dev">tests/Unit/PHP83</directory>
            <exclude>fixtures</exclude>
            <exclude>tests/Fixture</exclude>
        </testsuite>
    </testsuites>
</phpunit>
