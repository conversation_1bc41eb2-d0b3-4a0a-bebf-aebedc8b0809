<?php
/**
 * User Modals Component
 * Contains all modal dialogs for the user details page
 */
?>

<!-- OTP History Modal -->
<div class="modal modal-blur fade" id="otpHistoryModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">OTP History</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if (!empty($otp_history)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>OTP Code</th>
                                <th>Source</th>
                                <th>Created</th>
                                <th>Expires</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($otp_history as $otp): ?>
                            <tr>
                                <td>
                                    <span class="font-monospace badge bg-secondary">
                                        <?php echo htmlspecialchars($otp['otp_code']); ?>
                                    </span>
                                </td>
                                <td><?php echo ucfirst($otp['source'] ?? 'login'); ?></td>
                                <td><?php echo formatDate($otp['created_at'], 'M j, Y g:i A'); ?></td>
                                <td><?php echo formatDate($otp['expires_at'], 'M j, Y g:i A'); ?></td>
                                <td>
                                    <?php
                                    $is_expired = strtotime($otp['expires_at']) < time();
                                    $is_used = $otp['used'];
                                    
                                    if ($is_used) {
                                        echo '<span class="badge bg-success-lt">Used</span>';
                                    } elseif ($is_expired) {
                                        echo '<span class="badge bg-danger-lt">Expired</span>';
                                    } else {
                                        echo '<span class="badge bg-warning-lt">Active</span>';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-key mb-3" style="font-size: 3rem; opacity: 0.3;"></i>
                    <h4>No OTP History</h4>
                    <p>No OTP codes have been generated for this user.</p>
                </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Document Preview Modal -->
<div class="modal modal-blur fade" id="documentPreviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Document Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body document-preview-modal" id="documentPreviewContent">
                <!-- Document details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div class="modal modal-blur fade" id="errorModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="errorModalContent">
                <!-- Error message will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal modal-blur fade" id="successModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-success">
                    <i class="fas fa-check-circle me-2"></i>
                    Success
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="successModalContent">
                <!-- Success message will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
