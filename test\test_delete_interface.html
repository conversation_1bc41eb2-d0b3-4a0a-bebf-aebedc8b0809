<!DOCTYPE html>
<html>
<head>
    <title>Delete User Test Interface</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .test-section { border: 1px solid #ddd; padding: 15px; margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .delete-btn { background: #dc3545; color: white; border: none; }
        .test-btn { background: #007bff; color: white; border: none; }
        #result { background: #f8f9fa; padding: 10px; border: 1px solid #ddd; margin-top: 10px; }
    </style>
</head>
<body>
    <h1>Delete User Test Interface</h1>
    <hr>

    <div class="test-section">
        <h3>Available Users (Non-Admin):</h3>
        <div id="usersList">
            <?php
            require_once 'config/config.php';
            
            // Simulate admin session
            session_start();
            $_SESSION['user_id'] = 1;
            $_SESSION['username'] = 'admin';
            $_SESSION['is_admin'] = true;
            
            try {
                $db = getDB();
                $users_query = "SELECT id, username, email, first_name, last_name, balance FROM accounts WHERE is_admin = 0 ORDER BY id";
                $users_result = $db->query($users_query);
                
                if ($users_result->num_rows > 0) {
                    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Balance</th><th>Action</th></tr>";
                    
                    while ($user = $users_result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . $user['id'] . "</td>";
                        echo "<td>" . $user['username'] . "</td>";
                        echo "<td>" . $user['email'] . "</td>";
                        echo "<td>" . $user['first_name'] . " " . $user['last_name'] . "</td>";
                        echo "<td>$" . number_format($user['balance'], 2) . "</td>";
                        echo "<td>";
                        echo "<button class='delete-btn' onclick=\"deleteUser(" . $user['id'] . ", '" . htmlspecialchars($user['username']) . "')\">Delete</button>";
                        echo "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p class='info'>No non-admin users found.</p>";
                    echo "<button class='test-btn' onclick='createTestUser()'>Create Test User</button>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>Database error: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
    </div>

    <div class="test-section">
        <h3>Test Results:</h3>
        <div id="result">Ready to test delete functionality...</div>
    </div>

    <script>
    function deleteUser(userId, username) {
        if (!confirm(`Are you sure you want to delete user '${username}' (ID: ${userId})?`)) {
            return;
        }
        
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = '<div class="info">Deleting user... Please wait.</div>';
        
        fetch(`admin/delete-user.php?id=${userId}&ajax=1`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = `<div class="success">✅ SUCCESS: ${data.message}</div>`;
                // Refresh the page to update the user list
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                resultDiv.innerHTML = `<div class="error">❌ ERROR: ${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultDiv.innerHTML = `<div class="error">❌ AJAX Error: ${error.message}</div>`;
        });
    }
    
    function createTestUser() {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = '<div class="info">Creating test user... Please wait.</div>';
        
        fetch('create_test_user.php', {
            method: 'POST'
        })
        .then(response => response.text())
        .then(data => {
            resultDiv.innerHTML = `<div class="info">${data}</div>`;
            setTimeout(() => {
                location.reload();
            }, 2000);
        })
        .catch(error => {
            resultDiv.innerHTML = `<div class="error">❌ Error creating test user: ${error.message}</div>`;
        });
    }
    </script>

    <br>
    <a href="admin/users.php">← Go to Admin User Management</a>
</body>
</html>
