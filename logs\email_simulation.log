=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:33:20
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    719890
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:40:50
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    468483
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:46:27
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    123057
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:46:43
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    598468
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:46:45
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    358315
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:46:55
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    802208
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:46:57
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    586809
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:47:06
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    943931
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:47:19
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    501317
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:47:30
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    020052
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 11:47:46
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    211658
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 15:54:59
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    896517
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 15:55:19
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    153584
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Welcome to SecureBank Online - Your Account is Ready!
Time: 2025-06-01 15:59:51
Type: welcome
Message: 
    
    
    
        
        
        Welcome to SecureBank Online
        
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        
    
    
        
            
                SecureBank Online
                Welcome to Your New Account!
                Your banking journey starts here
            

            
                
                    🎉 Congratulations, Demo!
                    Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.
                

                📋 Your Account Details
                
                    
                        Account Holder:
                        Demo User
                    
                    
                        Account Number:
                        ************
                    
                    
                        Account Type:
                        Savings
                    
                    
                        Currency:
                        USD
                    
                    
                        Initial Balance:
                        $0.00
                    
                    
                        Username:
                        demohome4042
                    
                    
                        Email:
                        <EMAIL>
                    
                    
                        Status:
                        ✅ Active
                    
                

                
                    🔐 Security Information
                    
                        Your password has been set as provided during registration
                        Please log in and change your password if needed
                        Two-factor authentication (OTP) is enabled for your security
                        Never share your login credentials with anyone
                    
                

                
                    🚀 Access Your Account
                

                🎯 What's Next?
                
                    ✅ Log in to your account using your username and password
                    ✅ Complete your profile by adding additional information
                    ✅ Explore features like transfers, virtual cards, and more
                    ✅ Contact support if you need any assistance
                

                
                    📞 Need Help?
                    Our support team is here to help you get started:
                    
                        📧 Email: <EMAIL>
                        🌐 Visit: http://localhost/online_banking/
                        💬 Live chat available 24/7
                    
                
            

            
                Thank you for choosing SecureBank Online
                This email was <NAME_EMAIL>
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 16:41:53
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello AJAX Test,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    028889
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 16:42:45
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello AJAX Test,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    314947
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 16:50:13
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    243425
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 17:15:24
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello Jane Smith,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    005197
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 17:15:55
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello Jane Smith,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    229532
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 17:17:32
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    223982
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 17:17:41
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello Jane Smith,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    019473
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-01 17:17:49
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello John Doe,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    928333
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Welcome to SecureBank Online - Your Account is Ready!
Time: 2025-06-02 09:19:24
Type: welcome
Message: 
    
    
    
        
        
        Welcome to SecureBank Online
        
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        
    
    
        
            
                SecureBank Online
                Welcome to Your New Account!
                Your banking journey starts here
            

            
                
                    🎉 Congratulations, james!
                    Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.
                

                📋 Your Account Details
                
                    
                        Account Holder:
                        james Bong
                    
                    
                        Account Number:
                        ************
                    
                    
                        Account Type:
                        Savings
                    
                    
                        Currency:
                        USD
                    
                    
                        Initial Balance:
                        $5,000,000.00
                    
                    
                        Username:
                        <EMAIL>
                    
                    
                        Email:
                        <EMAIL>
                    
                    
                        Status:
                        ✅ Active
                    
                

                
                    🔐 Security Information
                    
                        Your password has been set as provided during registration
                        Please log in and change your password if needed
                        Two-factor authentication (OTP) is enabled for your security
                        Never share your login credentials with anyone
                    
                

                
                    🚀 Access Your Account
                

                🎯 What's Next?
                
                    ✅ Log in to your account using your username and password
                    ✅ Complete your profile by adding additional information
                    ✅ Explore features like transfers, virtual cards, and more
                    ✅ Contact support if you need any assistance
                

                
                    📞 Need Help?
                    Our support team is here to help you get started:
                    
                        📧 Email: <EMAIL>
                        🌐 Visit: http://localhost/online_banking/
                        💬 Live chat available 24/7
                    
                
            

            
                Thank you for choosing SecureBank Online
                This email was <NAME_EMAIL>
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: SMTP Connection Test - SecureBank Online
Time: 2025-06-02 17:16:32
Message: 
    
    
    
        
        
        SMTP Connection Test
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    🧪 Email System Test
                
                
                
                    
        This is a test email to verify SMTP connectivity and email delivery.
        
            📧 Test Configuration
            
                SMTP Host:smtp.hostinger.com
                SMTP Port:587
                Encryption:tls
                From Email:<EMAIL>
                Test Time:2025-06-02 17:16:32
            
        
        If you received this email, the SMTP configuration is working correctly!
        
                
            
            
            
            
                Note: This email confirms that the SMTP server connection and email delivery system are functioning properly.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your Login Verification Code - SecureBank Online
Time: 2025-06-02 17:16:43
Message: 
    
    
    
        
        
        Login Verification Code
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    🔐 Your OTP Code
                
                
                
                    
        Dear Test User,
        You have requested to log in to your SecureBank Online account. Please use the verification code below:
        
            741833
        
        Important Security Information:
        
            This code will expire in 10 minutes
            Do not share this code with anyone
            Our staff will never ask for this code
            If you didn't request this code, please contact us immediately
        
        
                
            
            
            
            
                Note: This is a test OTP email. The code above is for testing purposes only.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Transaction Alert - SecureBank Online
Time: 2025-06-02 17:16:53
Message: 
    
    
    
        
        
        Transaction Alert
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    💰 Account Activity Notification
                
                
                
                    
        Dear Test User,
        A transaction has been processed on your SecureBank Online account:
        
            ✅ Credit Transaction
            
                Amount:+$250.00
                Transaction ID:TXN-TEST-**********
                Date & Time:2025-06-02 17:16:53
                Description:Test Credit Transaction
                New Balance:$1,250.00
            
        
        Security Reminder:
        
            If you did not authorize this transaction, contact us immediately
            Monitor your account regularly for unauthorized activity
            Never share your account credentials with anyone
        
        
                
            
            
            
            
                Note: This is a test transaction email. No actual transaction has been processed.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Welcome to SecureBank Online - Test Email
Time: 2025-06-02 17:17:04
Message: 
    
    
    
        
        
        Welcome to Our Banking System!
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    🎉 Account Created Successfully
                
                
                
                    
        Dear Test User,
        Welcome to SecureBank Online! Your account has been successfully created and is ready to use.
        
            ✅ Account Details
            Account Number: TEST123456789
            Account Type: Savings Account
            Initial Balance: $0.00
        
        Next Steps:
        
            Log in to your account using your credentials
            Complete your profile information
            Set up your security preferences
            Explore our banking services
        
        If you have any questions, please don't hesitate to contact our customer support team.
        
                
            
            
            
            
                Note: This is a test email sent from the email testing system.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your Login Verification Code - SecureBank Online
Time: 2025-06-02 17:17:06
Message: 
    
    
    
        
        
        Login Verification Code
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    🔐 Your OTP Code
                
                
                
                    
        Dear Test User,
        You have requested to log in to your SecureBank Online account. Please use the verification code below:
        
            316034
        
        Important Security Information:
        
            This code will expire in 10 minutes
            Do not share this code with anyone
            Our staff will never ask for this code
            If you didn't request this code, please contact us immediately
        
        
                
            
            
            
            
                Note: This is a test OTP email. The code above is for testing purposes only.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Password Reset Request - SecureBank Online
Time: 2025-06-02 17:17:08
Message: 
    
    
    
        
        
        Password Reset Request
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    🔒 Reset Your Password
                
                
                
                    
        Dear Test User,
        We received a request to reset your password for your SecureBank Online account.
        
            ⚠️ Security Notice
            If you did not request this password reset, please ignore this email and contact our security team immediately.
        
        To reset your password, click the button below:
        
            Reset Password
        
        Security Information:
        
            This link will expire in 1 hour
            You can only use this link once
            Reset Token: c91e21bc5561f0d49be077c70cc1c2455fb4797495880c03c11439ba238f255d
        
        
                
            
            
            
            
                Note: This is a test password reset email. The reset link above is not functional.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Account Suspension Notice - SecureBank Online
Time: 2025-06-02 17:17:10
Message: 
    
    
    
        
        
        Account Suspension Notice
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    ⚠️ Account Temporarily Suspended
                
                
                
                    
        Dear Test User,
        
            🚫 Account Suspended
            Your account has been temporarily suspended due to security concerns.
        
        Suspension Details:
        
            Reason: Unusual activity detected
            Date: 2025-06-02 17:17:10
            Reference: SUSP-TEST-**********
        
        What happens next:
        
            Our security team will review your account
            You may be contacted for additional verification
            Account access will be restored once review is complete
        
        If you believe this suspension is in error, please contact our customer support immediately.
        
                
            
            
            
            
                Note: This is a test suspension email. Your account is not actually suspended.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Account Deletion Confirmation - SecureBank Online
Time: 2025-06-02 17:17:12
Message: 
    
    
    
        
        
        Account Deletion Confirmation
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    🗑️ Account Successfully Deleted
                
                
                
                    
        Dear Test User,
        This email confirms that your SecureBank Online account has been permanently deleted as requested.
        
            ℹ️ Deletion Summary
            Account Number: TEST123456789
            Deletion Date: 2025-06-02 17:17:12
            Final Balance: $0.00
            Reference: DEL-TEST-**********
        
        What has been deleted:
        
            All account information and transaction history
            Personal identification documents
            Saved payment methods and beneficiaries
            All associated virtual cards and crypto wallets
        
        Important: This action cannot be undone. If you need banking services in the future, you will need to create a new account.
        Thank you for banking with us. We're sorry to see you go!
        
                
            
            
            
            
                Note: This is a test deletion email. Your account has not been actually deleted.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Transaction Alert - SecureBank Online
Time: 2025-06-02 17:17:14
Message: 
    
    
    
        
        
        Transaction Alert
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    💰 Account Activity Notification
                
                
                
                    
        Dear Test User,
        A transaction has been processed on your SecureBank Online account:
        
            ✅ Credit Transaction
            
                Amount:+$250.00
                Transaction ID:TXN-TEST-**********
                Date & Time:2025-06-02 17:17:14
                Description:Test Credit Transaction
                New Balance:$1,250.00
            
        
        Security Reminder:
        
            If you did not authorize this transaction, contact us immediately
            Monitor your account regularly for unauthorized activity
            Never share your account credentials with anyone
        
        
                
            
            
            
            
                Note: This is a test transaction email. No actual transaction has been processed.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Security Alert - SecureBank Online
Time: 2025-06-02 17:17:16
Message: 
    
    
    
        
        
        Security Alert
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    🛡️ Account Security Notification
                
                
                
                    
        Dear Test User,
        
            🔔 Security Event Detected
            We detected a new login to your account from an unrecognized device.
        
        Login Details:
        
            Date & Time: 2025-06-02 17:17:16
            IP Address: *************
            Location: Test City, Test Country
            Device: Chrome Browser on Windows
        
        If this was you:
        
            No action is required
            Consider adding this device to your trusted devices
        
        If this was NOT you:
        
            Change your password immediately
            Review your recent account activity
            Contact our security team
            Consider enabling additional security features
        
        
            Secure My Account
        
        
                
            
            
            
            
                Note: This is a test security alert email. No actual security event has occurred.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: SMTP Connection Test - SecureBank Online
Time: 2025-06-02 17:17:18
Message: 
    
    
    
        
        
        SMTP Connection Test
    
    
        
            
            
                🏦 SecureBank Online
                Secure Online Banking
            
            
            
            
                
                    🧪 Email System Test
                
                
                
                    
        This is a test email to verify SMTP connectivity and email delivery.
        
            📧 Test Configuration
            
                SMTP Host:smtp.hostinger.com
                SMTP Port:587
                Encryption:tls
                From Email:<EMAIL>
                Test Time:2025-06-02 17:17:18
            
        
        If you received this email, the SMTP configuration is working correctly!
        
                
            
            
            
            
                Note: This email confirms that the SMTP server connection and email delivery system are functioning properly.
                This is an automated message from SecureBank Online.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-04 09:27:58
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello Demo User,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    295450
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Welcome to SecureBank Online - Your Account is Ready!
Time: 2025-06-04 09:28:00
Type: welcome
Message: 
    
    
    
        
        
        Welcome to SecureBank Online
        
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        
    
    
        
            
                SecureBank Online
                Welcome to Your New Account!
                Your banking journey starts here
            

            
                
                    🎉 Congratulations, Demo!
                    Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.
                

                📋 Your Account Details
                
                    
                        Account Holder:
                        Demo User
                    
                    
                        Account Number:
                        **********
                    
                    
                        Account Type:
                        Savings
                    
                    
                        Currency:
                        USD
                    
                    
                        Initial Balance:
                        $1,000.00
                    
                    
                        Username:
                        demo_user
                    
                    
                        Email:
                        <EMAIL>
                    
                    
                        Status:
                        ✅ Active
                    
                

                
                    🔐 Security Information
                    
                        Your password has been set as provided during registration
                        Please log in and change your password if needed
                        Two-factor authentication (OTP) is enabled for your security
                        Never share your login credentials with anyone
                    
                

                
                    🚀 Access Your Account
                

                🎯 What's Next?
                
                    ✅ Log in to your account using your username and password
                    ✅ Complete your profile by adding additional information
                    ✅ Explore features like transfers, virtual cards, and more
                    ✅ Contact support if you need any assistance
                

                
                    📞 Need Help?
                    Our support team is here to help you get started:
                    
                        📧 Email: <EMAIL>
                        🌐 Visit: http://localhost/online_banking/
                        💬 Live chat available 24/7
                    
                
            

            
                Thank you for choosing SecureBank Online
                This email was <NAME_EMAIL>
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: 🏦 Banking System Test - 09:28:00
Time: 2025-06-04 09:28:00
Message: 
    
        Banking System Email Test
        
            ✅ Test Successful!
            
                This email was sent directly using the banking system's sendEmail() function.
            
        
        
            Test Details:
            
                Recipient: <EMAIL>
                Test Time: 2025-06-04 09:28:00
                Function: sendEmail()
                OTP Code: 295450
            
        
        
            This is an automated test from the Online Banking System.
        
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: 🏦 Banking System Test - sendEmail Function 09:31:30
Time: 2025-06-04 09:31:30
Message: 
    
    Banking Email Test
    
        
            
                🏦 Online Banking System
                Email Test #2 - Banking Function
            
            
                
                    ✅ Banking Email Function Working!
                    This email was sent using the banking system's sendEmail() function.
                
                Configuration Details:
                
                    SMTP Host: smtp.hostinger.com
                    SMTP Port: 587
                    From Email: <EMAIL>
                    Test Time: 2025-06-04 09:31:30
                
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Your SecureBank Online Login Verification Code
Time: 2025-06-04 09:31:32
Message: 
    
    
    
        
        
        OTP Verification
        
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); padding: 2rem; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 1.5rem; }
            .content { padding: 2rem; }
            .otp-box { background: #f8f9fa; border: 2px dashed #6366f1; border-radius: 8px; padding: 2rem; text-align: center; margin: 2rem 0; }
            .otp-code { font-size: 2rem; font-weight: bold; color: #6366f1; letter-spacing: 0.5rem; font-family: 'Courier New', monospace; }
            .footer { background: #f8f9fa; padding: 1rem; text-align: center; font-size: 0.875rem; color: #6b7280; }
        
    
    
        
            
                SecureBank Online
            
            
                Hello Demo Developer,
                You have requested to log in to your SecureBank Online account. Please use the verification code below to complete your login:
                
                
                    190126
                
                
                Important:
                
                    This code will expire in 10 minutes
                    Do not share this code with anyone
                    If you didn't request this code, please contact our support team immediately
                
                
                Thank you for banking with us!
                Best regards,SecureBank Online Security Team
            
            
                This is an automated message. Please do not reply to this email.
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Welcome to SecureBank Online - Your Account is Ready!
Time: 2025-06-04 09:31:34
Type: welcome
Message: 
    
    
    
        
        
        Welcome to SecureBank Online
        
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #2e7d32, #246428); color: white; padding: 30px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .content { padding: 30px; }
            .welcome-card { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 20px 0; border-left: 4px solid #2e7d32; }
            .account-details { background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f1f3f4; }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: bold; color: #495057; }
            .detail-value { color: #212529; }
            .highlight { background: #2e7d32; color: white; padding: 2px 8px; border-radius: 4px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
            .button { background: #2e7d32; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
            @media (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px !important; }
                .detail-row { flex-direction: column; }
                .detail-label { margin-bottom: 5px; }
            }
        
    
    
        
            
                SecureBank Online
                Welcome to Your New Account!
                Your banking journey starts here
            

            
                
                    🎉 Congratulations, Demo!
                    Your account has been successfully created and is ready to use. We're excited to have you as part of the SecureBank Online family.
                

                📋 Your Account Details
                
                    
                        Account Holder:
                        Demo Developer
                    
                    
                        Account Number:
                        **********
                    
                    
                        Account Type:
                        Savings
                    
                    
                        Currency:
                        USD
                    
                    
                        Initial Balance:
                        $5,000.00
                    
                    
                        Username:
                        demothedev
                    
                    
                        Email:
                        <EMAIL>
                    
                    
                        Status:
                        ✅ Active
                    
                

                
                    🔐 Security Information
                    
                        Your password has been set as provided during registration
                        Please log in and change your password if needed
                        Two-factor authentication (OTP) is enabled for your security
                        Never share your login credentials with anyone
                    
                

                
                    🚀 Access Your Account
                

                🎯 What's Next?
                
                    ✅ Log in to your account using your username and password
                    ✅ Complete your profile by adding additional information
                    ✅ Explore features like transfers, virtual cards, and more
                    ✅ Contact support if you need any assistance
                

                
                    📞 Need Help?
                    Our support team is here to help you get started:
                    
                        📧 Email: <EMAIL>
                        🌐 Visit: http://localhost/online_banking/
                        💬 Live chat available 24/7
                    
                
            

            
                Thank you for choosing SecureBank Online
                This email was <NAME_EMAIL>
                &copy; 2025 SecureBank Online. All rights reserved.
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Registration Received - Pending Admin Approval
Time: 2025-06-05 09:07:03
Type: pending_approval
Message: 
    
    
    
        
        
        
        Registration Pending Approval - SecureBank Pro - Complete Banking Solution
        
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
            
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                line-height: 1.6; color: #374151; background-color: #f9fafb; margin: 0; padding: 0;
            }
            .email-container { 
                max-width: 600px; margin: 0 auto; background-color: #ffffff; 
                box-shadow: 0 10px 25px rgba(0,0,0,0.1); border-radius: 12px; overflow: hidden;
            }
            .header { 
                background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); 
                color: white; padding: 40px 30px; text-align: center; position: relative;
            }
            .header::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                background: url('data:image/svg+xml,') repeat;
            }
            .header-content { position: relative; z-index: 1; }
            .bank-logo { 
                font-size: 28px; font-weight: 700; margin-bottom: 10px; 
                text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            .header-title { 
                font-size: 24px; font-weight: 600; margin-bottom: 8px; 
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .header-subtitle { 
                font-size: 16px; opacity: 0.9; font-weight: 400; 
            }
            .content { padding: 40px 30px; }
            .content h2 { color: #1f2937; margin-bottom: 20px; font-size: 20px; font-weight: 600; }
            .content h3 { color: #374151; margin: 25px 0 15px 0; font-size: 18px; font-weight: 500; }
            .content p { margin-bottom: 16px; color: #4b5563; line-height: 1.7; }
            .content ul, .content ol { margin: 16px 0; padding-left: 24px; color: #4b5563; }
            .content li { margin-bottom: 8px; line-height: 1.6; }
            
            .info-box {
                background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
                border: 1px solid #bfdbfe; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.08);
                position: relative; overflow: hidden;
            }
            .info-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #1e40af 0%, #60a5fa 100%);
            }
            .success-box {
                background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
                border: 1px solid #a7f3d0; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(5, 150, 105, 0.08);
                position: relative; overflow: hidden;
            }
            .success-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #059669 0%, #34d399 100%);
            }
            .warning-box {
                background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
                border: 1px solid #fed7aa; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(217, 119, 6, 0.08);
                position: relative; overflow: hidden;
            }
            .warning-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #d97706 0%, #fbbf24 100%);
            }
            .danger-box {
                background: linear-gradient(135deg, #fef2f2 0%, #fef1f1 100%);
                border: 1px solid #fecaca; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(220, 38, 38, 0.08);
                position: relative; overflow: hidden;
            }
            .danger-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #dc2626 0%, #f87171 100%);
            }
            
            .account-details { 
                background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; 
                padding: 24px; margin: 24px 0; 
            }
            .detail-row { 
                display: flex; justify-content: space-between; align-items: center; 
                padding: 12px 0; border-bottom: 1px solid #e5e7eb; 
            }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: 500; color: #6b7280; }
            .detail-value { font-weight: 600; color: #1f2937; }
            .highlight { 
                background: #1e40af; color: white; padding: 4px 12px; 
                border-radius: 6px; font-weight: 600; font-size: 14px;
            }
            
            .footer { 
                background: #f8fafc; padding: 30px; text-align: center; 
                border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; 
            }
            .footer-links { margin: 20px 0; }
            .footer-links a { 
                color: #1e40af; text-decoration: none; margin: 0 15px; 
                font-weight: 500; 
            }
            .footer-links a:hover { text-decoration: underline; }
            
            @media (max-width: 600px) {
                .email-container { margin: 0; border-radius: 0; }
                .header, .content, .footer { padding: 30px 20px; }
                .detail-row { flex-direction: column; align-items: flex-start; }
                .detail-label { margin-bottom: 4px; }
                .bank-logo { font-size: 24px; }
                .header-title { font-size: 20px; }
            }
        
    
    
        
            
                
                    
                        🏦 SecureBank Pro - Complete Banking Solution
                        Registration Received - Pending Approval
                        Secure • Reliable • Professional
                    
                
                
                
                    
    
        ⏳ Registration Received Successfully!
        Thank you for registering with SecureBank Pro - Complete Banking Solution. Your account has been created and is currently pending admin approval.
    

    
        📋 Registration Details
        
            Full Name:
            Demo User
        
        
            Username:
            demohomexx
        
        
            Email:
            <EMAIL>
        
        
            Status:
            ⏳ Pending Approval
        
    

    
        ⚠️ What Happens Next?
        
            Our admin team will review your registration within 24-48 hours
            You'll receive an email notification once your account is approved
            You cannot log in until your account is activated
            No action is required from you at this time
        
    

    
        📞 Need Help?
        If you have any questions about your registration:
        
            📧 Email: <EMAIL>
            📞 Phone: 1-800-BANKING (24/7)
            💬 Live chat available on our website
        
    
                    
                
                
                
                    
                        SecureBank Pro - Complete Banking Solution
                        Your trusted financial partner - Now with improved email templates!
                    

                    
                        Online Banking
                        Support Center
                        Contact Us
                        Security
                    
                    
                    This email confirms your registration. Please keep it for your records.
                    
                    
                        This email was sent from a secure, monitored system. Please do not reply to this email.
                        &copy; 2025 SecureBank Pro - Complete Banking Solution. All rights reserved.
                        If you have questions, please contact our support team.
                    
                
            
        
    
    
========================

=== EMAIL SIMULATION ===
To: <EMAIL>
Subject: Welcome to Online Banking - 20:52:51
Time: 2025-06-17 20:52:54
Type: welcome
Message: 
    
    
    
        
        
        
        Welcome to Your New Account - PremierBank Pro
        
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
            
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                line-height: 1.6; color: #374151; background-color: #f9fafb; margin: 0; padding: 0;
            }
            .email-container { 
                max-width: 600px; margin: 0 auto; background-color: #ffffff; 
                box-shadow: 0 10px 25px rgba(0,0,0,0.1); border-radius: 12px; overflow: hidden;
            }
            .header { 
                background: linear-gradient(135deg, #16bb3c 0%, #6c757d 100%); 
                color: white; padding: 40px 30px; text-align: center; position: relative;
            }
            .header::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                background: url('data:image/svg+xml,') repeat;
            }
            .header-content { position: relative; z-index: 1; }
            .bank-logo {
                font-size: 24px; font-weight: 600; margin-bottom: 15px;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .header-title {
                font-size: 20px; font-weight: 500; margin-bottom: 0px;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .content { padding: 40px 30px; }
            .content h2 { color: #1f2937; margin-bottom: 20px; font-size: 20px; font-weight: 600; }
            .content h3 { color: #374151; margin: 25px 0 15px 0; font-size: 18px; font-weight: 500; }
            .content p { margin-bottom: 16px; color: #4b5563; line-height: 1.7; }
            .content ul, .content ol { margin: 16px 0; padding-left: 24px; color: #4b5563; }
            .content li { margin-bottom: 8px; line-height: 1.6; }
            
            .info-box {
                background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
                border: 1px solid #bfdbfe; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.08);
                position: relative; overflow: hidden;
            }
            .info-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #16bb3c 0%, #60a5fa 100%);
            }
            .success-box {
                background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
                border: 1px solid #a7f3d0; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(5, 150, 105, 0.08);
                position: relative; overflow: hidden;
            }
            .success-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #059669 0%, #34d399 100%);
            }
            .warning-box {
                background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
                border: 1px solid #fed7aa; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(217, 119, 6, 0.08);
                position: relative; overflow: hidden;
            }
            .warning-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #d97706 0%, #fbbf24 100%);
            }
            .danger-box {
                background: linear-gradient(135deg, #fef2f2 0%, #fef1f1 100%);
                border: 1px solid #fecaca; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(220, 38, 38, 0.08);
                position: relative; overflow: hidden;
            }
            .danger-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #dc2626 0%, #f87171 100%);
            }
            
            .account-details { 
                background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; 
                padding: 24px; margin: 24px 0; 
            }
            .detail-row { 
                display: flex; justify-content: space-between; align-items: center; 
                padding: 12px 0; border-bottom: 1px solid #e5e7eb; 
            }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: 500; color: #6b7280; }
            .detail-value { font-weight: 600; color: #1f2937; }
            .highlight { 
                background: #16bb3c; color: white; padding: 4px 12px; 
                border-radius: 6px; font-weight: 600; font-size: 14px;
            }
            
            .footer { 
                background: #f8fafc; padding: 30px; text-align: center; 
                border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; 
            }
            .footer-links { margin: 20px 0; }
            .footer-links a { 
                color: #16bb3c; text-decoration: none; margin: 0 15px; 
                font-weight: 500; 
            }
            .footer-links a:hover { text-decoration: underline; }
            
            @media (max-width: 600px) {
                .email-container { margin: 0; border-radius: 0; }
                .header, .content, .footer { padding: 30px 20px; }
                .detail-row { flex-direction: column; align-items: flex-start; }
                .detail-label { margin-bottom: 4px; }
                .bank-logo { font-size: 24px; }
                .header-title { font-size: 20px; }
            }
        
    
    
        
            
                
                    
                        PremierBank Pro
                        Welcome to Your New Account!
                    
                
                
                
                    
    
        Welcome, John!
        Your account has been successfully created and is ready to use.
    
    
    📋 Your Account Details
    
        
            Account Holder:
            John Smith
        
        
            Account Number:
            ************
        
        
            Account Type:
            Savings
        
        
            Currency:
            USD
        
        
            Current Balance:
            $1,000.00
        
        
            Username:
            testuser2025
        
        
            Status:
            ✅ Active
        
    
    
    
        🔐 Security Information
        
            Your password has been set as provided during registration
            Please log in and change your password if needed
            Two-factor authentication (OTP) is enabled for your security
            Never share your login credentials with anyone
            Always log out when using shared computers
        
    
    
    🎯 Getting Started
    
        Log in to your account using your username and password
        Complete your profile by adding additional information
        Explore features like transfers, virtual cards, and more
        Set up alerts to stay informed about your account activity
        Contact support if you need any assistance
    
    
    
        📞 Need Help?
        Our support team is here to help you get started:
        
            📧 Email: <EMAIL>
            📞 Phone: 1-800-BANKING (24/7)
            💬 Live chat available on our website
            🌐 Visit our help center for FAQs
        
    
                    
        
            
                🚀 Access Your Account
            
        
                
                
                
                    
                        PremierBank Pro
                        Your trusted financial partner - Now with improved email templates!
                    

                    
                        Online Banking
                        Support Center
                        Contact Us
                        Security
                    
                    
                    This email contains important account information. Please keep it for your records.
                    
                    
                        This email was sent from a secure, monitored system. Please do not reply to this email.
                        &copy; 2025 PremierBank Pro. All rights reserved.
                        If you have questions, please contact our support team.
                    
                
            
        
    
    
========================

