<?php
/**
 * User Header - Clean header for regular users without admin functionality
 * Users should never see any admin references or options
 */

// Ensure user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

// Block admin users from accessing user interface directly
if (isAdmin()) {
    redirect('admin/');
}

// Get appearance settings
$appearance = getAppearanceSettings();
$bank_name = getBankName();
$theme = $appearance['theme'] ?? 'light';
$color_scheme = $appearance['color_scheme'] ?? 'blue';
$header_fixed = ($appearance['header_fixed'] ?? 'true') === 'true';
$animations_enabled = ($appearance['animations_enabled'] ?? 'true') === 'true';

// Get user information
$user_name = $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];
$user_email = $_SESSION['email'] ?? '';
$user_avatar = strtoupper(substr($_SESSION['first_name'], 0, 1) . substr($_SESSION['last_name'], 0, 1));
?>
<!DOCTYPE html>
<html lang="en" data-theme="<?php echo htmlspecialchars($theme); ?>" data-color-scheme="<?php echo htmlspecialchars($color_scheme); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' : ''; ?><?php echo htmlspecialchars($bank_name); ?></title>
    
    <!-- Favicon -->
    <?php if (!empty($appearance['favicon_url'])): ?>
    <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($appearance['favicon_url']); ?>">
    <?php else: ?>
    <link rel="icon" type="image/x-icon" href="<?php echo asset('favicon.ico'); ?>">
    <?php endif; ?>
    
    <!-- CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons@1.39.1/icons-sprite.svg" rel="preload" as="image">
    
    <!-- Font Awesome for additional icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom styles -->
    <style>
        :root {
            --bank-primary: <?php echo $color_scheme === 'blue' ? '#0054a6' : ($color_scheme === 'green' ? '#2e7d32' : '#d32f2f'); ?>;
            --bank-secondary: <?php echo $color_scheme === 'blue' ? '#0066cc' : ($color_scheme === 'green' ? '#388e3c' : '#f44336'); ?>;
        }
        
        <?php if ($theme === 'dark'): ?>
        .navbar-dark {
            background-color: #1a1a1a !important;
            border-bottom: 1px solid #333;
        }
        
        .dropdown-menu {
            background-color: #2d2d2d;
            border-color: #444;
        }
        
        .dropdown-item {
            color: #e0e0e0;
        }
        
        .dropdown-item:hover {
            background-color: #404040;
            color: #fff;
        }
        <?php endif; ?>
        
        .navbar-brand {
            font-weight: 600;
            color: var(--bank-primary) !important;
        }
        
        .bank-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--bank-primary) 0%, var(--bank-secondary) 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
        }
        
        <?php if (!$animations_enabled): ?>
        * {
            animation: none !important;
            transition: none !important;
        }
        <?php endif; ?>
        
        .user-avatar {
            width: 32px;
            height: 32px;
            background: var(--bank-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }
        
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
        }
        
        .navbar-nav .nav-link:hover {
            color: var(--bank-primary) !important;
        }
        
        .dropdown-toggle::after {
            display: none;
        }
        
        .balance-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .transaction-item {
            border-left: 3px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .transaction-item:hover {
            border-left-color: #206bc4;
            background-color: #f8f9fa;
        }
        
        .status-pending { color: #f59f00; }
        .status-completed { color: #2fb344; }
        .status-failed { color: #d63384; }
        .status-cancelled { color: #6c757d; }
        
        .quick-action-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
        }
        
        .quick-action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="<?php echo $theme === 'dark' ? 'theme-dark' : ''; ?>">
    
    <!-- Navigation Header -->
    <header class="navbar navbar-expand-md navbar-light d-print-none <?php echo $header_fixed ? 'navbar-sticky' : ''; ?>">
        <div class="container-xl">
            <!-- Mobile menu toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Brand -->
            <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                <a href="<?php echo url('dashboard.php'); ?>" class="d-flex align-items-center text-decoration-none">
                    <?php if (!empty($appearance['logo_url'])): ?>
                        <img src="<?php echo htmlspecialchars($appearance['logo_url']); ?>" alt="<?php echo htmlspecialchars($bank_name); ?>" class="bank-logo" style="background: none;">
                    <?php else: ?>
                        <div class="bank-logo">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M3 21l18 0"/>
                                <path d="M3 10l18 0"/>
                                <path d="M5 6l7 -3l7 3"/>
                                <path d="M4 10l0 11"/>
                                <path d="M20 10l0 11"/>
                                <path d="M8 14l0 3"/>
                                <path d="M12 14l0 3"/>
                                <path d="M16 14l0 3"/>
                            </svg>
                        </div>
                    <?php endif; ?>
                    <?php echo htmlspecialchars($bank_name); ?>
                </a>
            </h1>
            
            <!-- User Menu -->
            <div class="navbar-nav flex-row order-md-last">
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" aria-label="Open user menu">
                        <div class="user-avatar">
                            <?php echo htmlspecialchars($user_avatar); ?>
                        </div>
                        <div class="d-none d-xl-block ps-2">
                            <div><?php echo htmlspecialchars($user_name); ?></div>
                            <div class="mt-1 small text-muted"><?php echo htmlspecialchars($_SESSION['account_number'] ?? 'N/A'); ?></div>
                        </div>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                        <a href="<?php echo url('dashboard/profile.php'); ?>" class="dropdown-item">
                            <i class="fas fa-user me-2"></i>
                            Profile
                        </a>
                        <a href="<?php echo url('dashboard/settings.php'); ?>" class="dropdown-item">
                            <i class="fas fa-cog me-2"></i>
                            Settings
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="<?php echo url('auth/logout.php'); ?>" class="dropdown-item">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbar-menu">
                <div class="d-flex flex-column flex-md-row flex-fill align-items-stretch align-items-md-center">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php' && strpos($_SERVER['REQUEST_URI'], '/dashboard') !== false) ? 'active' : ''; ?>" href="<?php echo url('dashboard/'); ?>">
                                <i class="fas fa-home me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/accounts') !== false) ? 'active' : ''; ?>" href="<?php echo url('accounts/'); ?>">
                                <i class="fas fa-wallet me-2"></i>
                                Accounts
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/transactions') !== false) ? 'active' : ''; ?>" href="<?php echo url('transactions/'); ?>">
                                <i class="fas fa-exchange-alt me-2"></i>
                                Transactions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/transfers') !== false) ? 'active' : ''; ?>" href="<?php echo url('transfers/'); ?>">
                                <i class="fas fa-paper-plane me-2"></i>
                                Transfer
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/beneficiaries') !== false) ? 'active' : ''; ?>" href="<?php echo url('beneficiaries/'); ?>">
                                <i class="fas fa-users me-2"></i>
                                Beneficiaries
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/support') !== false) ? 'active' : ''; ?>" href="<?php echo url('support/'); ?>">
                                <i class="fas fa-headset me-2"></i>
                                Support
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Flash Messages -->
    <?php if (hasFlashMessage('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars(getFlashMessage('success')); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (hasFlashMessage('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars(getFlashMessage('error')); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (hasFlashMessage('warning')): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars(getFlashMessage('warning')); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <!-- Main Content Container -->
    <div class="page-wrapper">
        <div class="page-body">
            <div class="container-xl">
                
    <!-- Tabler Core -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/js/tabler.min.js"></script>
