<?xml version="1.0" encoding="UTF-8"?>
<files psalm-version="5.24.0@462c80e31c34e58cc4f750c656be3927e80e550e">
  <file src="library/Mockery.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[$demeterMockKey]]></code>
      <code><![CDATA[$newMockName]]></code>
    </ArgumentTypeCoercion>
    <DeprecatedClass>
      <code><![CDATA[MustBe]]></code>
      <code><![CDATA[new MustBe($expected)]]></code>
    </DeprecatedClass>
    <DeprecatedMethod>
      <code><![CDATA[self::builtInTypes()]]></code>
    </DeprecatedMethod>
    <DocblockTypeContradiction>
      <code><![CDATA[self::$_config === null]]></code>
      <code><![CDATA[self::$_generator === null]]></code>
      <code><![CDATA[self::$_loader === null]]></code>
    </DocblockTypeContradiction>
    <InternalMethod>
      <code><![CDATA[Reflector::isReservedWord($type)]]></code>
    </InternalMethod>
    <InvalidReturnStatement>
      <code><![CDATA[$argument]]></code>
      <code><![CDATA[$container->getMocks()[$demeterMockKey] ?? null]]></code>
      <code><![CDATA['...']]></code>
      <code><![CDATA[self::getContainer()->mock(...$args)->shouldIgnoreMissing()]]></code>
    </InvalidReturnStatement>
    <InvalidReturnType>
      <code><![CDATA[LegacyMockInterface&MockInterface&TSpy]]></code>
      <code><![CDATA[TArray]]></code>
      <code><![CDATA[null|(LegacyMockInterface&MockInterface&TMock)]]></code>
    </InvalidReturnType>
    <LessSpecificReturnType>
      <code><![CDATA[array<string, mixed>]]></code>
    </LessSpecificReturnType>
    <MissingClosureParamType>
      <code><![CDATA[$argument]]></code>
      <code><![CDATA[$method]]></code>
      <code><![CDATA[$n]]></code>
      <code><![CDATA[$nesting]]></code>
      <code><![CDATA[$object]]></code>
    </MissingClosureParamType>
    <MissingClosureReturnType>
      <code><![CDATA[static function ($argument) use (&$reference) {]]></code>
      <code><![CDATA[static function ($method) use ($add) {]]></code>
      <code><![CDATA[static function ($n) use ($mock) {]]></code>
      <code><![CDATA[static function ($object, $nesting) {]]></code>
    </MissingClosureReturnType>
    <MissingReturnType>
      <code><![CDATA[registerFileForCleanUp]]></code>
      <code><![CDATA[setGenerator]]></code>
      <code><![CDATA[setLoader]]></code>
    </MissingReturnType>
    <MissingThrowsDocblock>
      <code><![CDATA[getMethod]]></code>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[mockery_teardown]]></code>
    </MissingThrowsDocblock>
    <MixedArgument>
      <code><![CDATA[$expectations]]></code>
      <code><![CDATA[$formatter($object, $nesting)]]></code>
      <code><![CDATA[$nesting]]></code>
      <code><![CDATA[$object]]></code>
      <code><![CDATA[$value]]></code>
    </MixedArgument>
    <MixedArgumentTypeCoercion>
      <code><![CDATA[$args]]></code>
      <code><![CDATA[$formattedArguments]]></code>
      <code><![CDATA[$k]]></code>
    </MixedArgumentTypeCoercion>
    <MixedArrayAssignment>
      <code><![CDATA[$argument[$key]]]></code>
      <code><![CDATA[$argument[$key]]]></code>
    </MixedArrayAssignment>
    <MixedArrayOffset>
      <code><![CDATA[$argument[$key]]]></code>
      <code><![CDATA[$argument[$key]]]></code>
    </MixedArrayOffset>
    <MixedAssignment>
      <code><![CDATA[$arg]]></code>
      <code><![CDATA[$argument]]></code>
      <code><![CDATA[$cleanedProperties[$name]]]></code>
      <code><![CDATA[$expectations]]></code>
      <code><![CDATA[$formattedArguments[]]]></code>
      <code><![CDATA[$key]]></code>
      <code><![CDATA[$reference]]></code>
      <code><![CDATA[$v]]></code>
      <code><![CDATA[$value]]></code>
      <code><![CDATA[$value]]></code>
      <code><![CDATA[$value]]></code>
    </MixedAssignment>
    <MixedInferredReturnType>
      <code><![CDATA[ExpectationInterface]]></code>
    </MixedInferredReturnType>
    <MixedOperand>
      <code><![CDATA[$argument]]></code>
    </MixedOperand>
    <MixedReturnStatement>
      <code><![CDATA[$expectations]]></code>
      <code><![CDATA[$expectations]]></code>
    </MixedReturnStatement>
    <NullableReturnStatement>
      <code><![CDATA[$expectations]]></code>
    </NullableReturnStatement>
    <PossiblyInvalidArgument>
      <code><![CDATA[$args]]></code>
      <code><![CDATA[$args]]></code>
      <code><![CDATA[$name]]></code>
    </PossiblyInvalidArgument>
    <PossiblyInvalidCast>
      <code><![CDATA[$name]]></code>
    </PossiblyInvalidCast>
    <PossiblyNullPropertyAssignmentValue>
      <code><![CDATA[null]]></code>
    </PossiblyNullPropertyAssignmentValue>
    <PossiblyNullReference>
      <code><![CDATA[allows]]></code>
      <code><![CDATA[mockery_getExpectationsFor]]></code>
    </PossiblyNullReference>
    <PossiblyUndefinedIntArrayOffset>
      <code><![CDATA[$args[0]]]></code>
    </PossiblyUndefinedIntArrayOffset>
    <PossiblyUndefinedMethod>
      <code><![CDATA[allows]]></code>
    </PossiblyUndefinedMethod>
    <PossiblyUnusedMethod>
      <code><![CDATA[andAnyOtherArgs]]></code>
      <code><![CDATA[andAnyOthers]]></code>
      <code><![CDATA[any]]></code>
      <code><![CDATA[anyOf]]></code>
      <code><![CDATA[capture]]></code>
      <code><![CDATA[contains]]></code>
      <code><![CDATA[ducktype]]></code>
      <code><![CDATA[fetchMock]]></code>
      <code><![CDATA[globalHelpers]]></code>
      <code><![CDATA[hasKey]]></code>
      <code><![CDATA[hasValue]]></code>
      <code><![CDATA[instanceMock]]></code>
      <code><![CDATA[isBuiltInType]]></code>
      <code><![CDATA[isEqual]]></code>
      <code><![CDATA[isSame]]></code>
      <code><![CDATA[mustBe]]></code>
      <code><![CDATA[not]]></code>
      <code><![CDATA[notAnyOf]]></code>
      <code><![CDATA[on]]></code>
      <code><![CDATA[parseShouldReturnArgs]]></code>
      <code><![CDATA[pattern]]></code>
      <code><![CDATA[resetContainer]]></code>
      <code><![CDATA[setContainer]]></code>
      <code><![CDATA[setGenerator]]></code>
      <code><![CDATA[setLoader]]></code>
      <code><![CDATA[subset]]></code>
      <code><![CDATA[type]]></code>
    </PossiblyUnusedMethod>
    <PossiblyUnusedReturnValue>
      <code><![CDATA[LegacyMockInterface|MockInterface]]></code>
    </PossiblyUnusedReturnValue>
    <RedundantConditionGivenDocblockType>
      <code><![CDATA[$parentMock !== null]]></code>
    </RedundantConditionGivenDocblockType>
    <RiskyTruthyFalsyComparison>
      <code><![CDATA[\strpos($fqn, '\\')]]></code>
    </RiskyTruthyFalsyComparison>
    <UnresolvableInclude>
      <code><![CDATA[require $fileName]]></code>
    </UnresolvableInclude>
  </file>
  <file src="library/Mockery/Adapter/Phpunit/MockeryPHPUnitIntegration.php">
    <InternalMethod>
      <code><![CDATA[addToAssertionCount]]></code>
    </InternalMethod>
    <MissingPropertyType>
      <code><![CDATA[$mockeryOpen]]></code>
    </MissingPropertyType>
    <MissingReturnType>
      <code><![CDATA[addMockeryExpectationsToAssertionCount]]></code>
      <code><![CDATA[checkMockeryExceptions]]></code>
      <code><![CDATA[closeMockery]]></code>
      <code><![CDATA[mockeryAssertPostConditions]]></code>
      <code><![CDATA[purgeMockeryContainer]]></code>
      <code><![CDATA[startMockery]]></code>
    </MissingReturnType>
    <PossiblyUnusedMethod>
      <code><![CDATA[purgeMockeryContainer]]></code>
      <code><![CDATA[startMockery]]></code>
    </PossiblyUnusedMethod>
    <UndefinedAttributeClass>
      <code><![CDATA[After]]></code>
      <code><![CDATA[Before]]></code>
    </UndefinedAttributeClass>
    <UndefinedInterfaceMethod>
      <code><![CDATA[dismissed]]></code>
    </UndefinedInterfaceMethod>
  </file>
  <file src="library/Mockery/Adapter/Phpunit/MockeryTestCase.php">
    <MissingReturnType>
      <code><![CDATA[mockeryTestSetUp]]></code>
      <code><![CDATA[mockeryTestTearDown]]></code>
    </MissingReturnType>
  </file>
  <file src="library/Mockery/Adapter/Phpunit/TestListener.php">
    <DeprecatedInterface>
      <code><![CDATA[TestListener]]></code>
    </DeprecatedInterface>
    <DeprecatedTrait>
      <code><![CDATA[TestListenerDefaultImplementation]]></code>
    </DeprecatedTrait>
    <MissingPropertyType>
      <code><![CDATA[$trait]]></code>
    </MissingPropertyType>
    <MixedMethodCall>
      <code><![CDATA[endTest]]></code>
      <code><![CDATA[startTestSuite]]></code>
    </MixedMethodCall>
    <UnusedClass>
      <code><![CDATA[TestListener]]></code>
    </UnusedClass>
  </file>
  <file src="library/Mockery/Adapter/Phpunit/TestListenerTrait.php">
    <DeprecatedClass>
      <code><![CDATA[Blacklist::addDirectory(dirname((new ReflectionClass(Mockery::class))->getFileName()))]]></code>
      <code><![CDATA[Blacklist::class]]></code>
      <code><![CDATA[new Blacklist()]]></code>
    </DeprecatedClass>
    <InternalClass>
      <code><![CDATA[BaseTestRunner::STATUS_PASSED]]></code>
      <code><![CDATA[new ExpectationFailedException(
            sprintf(
                "Mockery's expectations have not been verified. Make sure that \Mockery::close() is called at the end of the test. Consider using %s\MockeryPHPUnitIntegration or extending %s\MockeryTestCase.",
                __NAMESPACE__,
                __NAMESPACE__
            )
        )]]></code>
    </InternalClass>
    <InternalMethod>
      <code><![CDATA[addFailure]]></code>
      <code><![CDATA[getTestResultObject]]></code>
      <code><![CDATA[new ExpectationFailedException(
            sprintf(
                "Mockery's expectations have not been verified. Make sure that \Mockery::close() is called at the end of the test. Consider using %s\MockeryPHPUnitIntegration or extending %s\MockeryTestCase.",
                __NAMESPACE__,
                __NAMESPACE__
            )
        )]]></code>
    </InternalMethod>
    <MissingReturnType>
      <code><![CDATA[endTest]]></code>
      <code><![CDATA[startTestSuite]]></code>
    </MissingReturnType>
    <MissingThrowsDocblock>
      <code><![CDATA[getBlacklistedDirectories]]></code>
    </MissingThrowsDocblock>
    <PossiblyUnusedMethod>
      <code><![CDATA[endTest]]></code>
      <code><![CDATA[startTestSuite]]></code>
    </PossiblyUnusedMethod>
    <RedundantConditionGivenDocblockType>
      <code><![CDATA[$result !== null]]></code>
    </RedundantConditionGivenDocblockType>
    <UndefinedPropertyAssignment>
      <code><![CDATA[Blacklist::$blacklistedClassNames]]></code>
    </UndefinedPropertyAssignment>
    <UndefinedPropertyFetch>
      <code><![CDATA[Blacklist::$blacklistedClassNames]]></code>
    </UndefinedPropertyFetch>
  </file>
  <file src="library/Mockery/CompositeExpectation.php">
    <InvalidCast>
      <code><![CDATA[$expectation]]></code>
    </InvalidCast>
    <InvalidPropertyAssignmentValue>
      <code><![CDATA[$this->_expectations]]></code>
    </InvalidPropertyAssignmentValue>
    <InvalidReturnStatement>
      <code><![CDATA[$first->getMock()]]></code>
    </InvalidReturnStatement>
    <InvalidReturnType>
      <code><![CDATA[LegacyMockInterface&MockInterface]]></code>
    </InvalidReturnType>
    <LessSpecificReturnStatement>
      <code><![CDATA[$first->getMock()->shouldNotReceive(...$args)]]></code>
      <code><![CDATA[$first->getMock()->shouldReceive(...$args)]]></code>
      <code><![CDATA[$this->andReturn(...$args)]]></code>
    </LessSpecificReturnStatement>
    <MixedArgument>
      <code><![CDATA[$args]]></code>
      <code><![CDATA[$args]]></code>
    </MixedArgument>
    <MoreSpecificReturnType>
      <code><![CDATA[Expectation]]></code>
      <code><![CDATA[Expectation]]></code>
      <code><![CDATA[self]]></code>
    </MoreSpecificReturnType>
    <PossiblyUnusedMethod>
      <code><![CDATA[mock]]></code>
      <code><![CDATA[shouldNotReceive]]></code>
      <code><![CDATA[shouldReceive]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/Configuration.php">
    <MissingPropertyType>
      <code><![CDATA[$_reflectionCacheEnabled]]></code>
    </MissingPropertyType>
    <MixedArgument>
      <code><![CDATA[$type]]></code>
      <code><![CDATA[Hamcrest_Matcher::class]]></code>
    </MixedArgument>
    <MixedAssignment>
      <code><![CDATA[$type]]></code>
    </MixedAssignment>
    <MixedInferredReturnType>
      <code><![CDATA[bool]]></code>
    </MixedInferredReturnType>
    <MixedReturnStatement>
      <code><![CDATA[$this->_reflectionCacheEnabled]]></code>
    </MixedReturnStatement>
    <PossiblyUndefinedVariable>
      <code><![CDATA[$classes]]></code>
    </PossiblyUndefinedVariable>
    <PossiblyUnusedMethod>
      <code><![CDATA[allowMockingMethodsUnnecessarily]]></code>
      <code><![CDATA[allowMockingNonExistentMethods]]></code>
      <code><![CDATA[disableReflectionCache]]></code>
      <code><![CDATA[enableReflectionCache]]></code>
      <code><![CDATA[getDefaultMatcher]]></code>
      <code><![CDATA[getInternalClassMethodParamMap]]></code>
      <code><![CDATA[mockingMethodsUnnecessarilyAllowed]]></code>
      <code><![CDATA[reflectionCacheEnabled]]></code>
      <code><![CDATA[resetInternalClassMethodParamMaps]]></code>
      <code><![CDATA[setConstantsMap]]></code>
      <code><![CDATA[setDefaultMatcher]]></code>
      <code><![CDATA[setInternalClassMethodParamMap]]></code>
      <code><![CDATA[setObjectFormatter]]></code>
    </PossiblyUnusedMethod>
    <PropertyTypeCoercion>
      <code><![CDATA[$this->_internalClassParamMap]]></code>
      <code><![CDATA[$this->_internalClassParamMap]]></code>
    </PropertyTypeCoercion>
    <RedundantCastGivenDocblockType>
      <code><![CDATA[(bool) $flag]]></code>
      <code><![CDATA[(bool) $flag]]></code>
    </RedundantCastGivenDocblockType>
    <UndefinedClass>
      <code><![CDATA[Hamcrest_Matcher]]></code>
    </UndefinedClass>
  </file>
  <file src="library/Mockery/Container.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[$constructorArgs]]></code>
      <code><![CDATA[$interfaces]]></code>
      <code><![CDATA[$mock]]></code>
      <code><![CDATA[$type]]></code>
      <code><![CDATA['stdClass']]></code>
      <code><![CDATA['stdClass']]></code>
    </ArgumentTypeCoercion>
    <DocblockTypeContradiction>
      <code><![CDATA[$match === false]]></code>
      <code><![CDATA[array_keys($arg) !== range(0, count($arg) - 1)]]></code>
    </DocblockTypeContradiction>
    <InvalidArgument>
      <code><![CDATA[$arg]]></code>
    </InvalidArgument>
    <InvalidArrayOffset>
      <code><![CDATA[$mocks[$index]]]></code>
    </InvalidArrayOffset>
    <InvalidCast>
      <code><![CDATA[$arg]]></code>
    </InvalidCast>
    <InvalidReturnStatement>
      <code><![CDATA[$this->rememberMock($mock)]]></code>
    </InvalidReturnStatement>
    <InvalidReturnType>
      <code><![CDATA[LegacyMockInterface&MockInterface&TMock]]></code>
    </InvalidReturnType>
    <LessSpecificReturnStatement>
      <code><![CDATA[$this->_mocks[$reference] ?? null]]></code>
    </LessSpecificReturnStatement>
    <MissingParamType>
      <code><![CDATA[$config]]></code>
    </MissingParamType>
    <MissingReturnType>
      <code><![CDATA[checkForNamedMockClashes]]></code>
      <code><![CDATA[mockery_teardown]]></code>
      <code><![CDATA[mockery_validateOrder]]></code>
      <code><![CDATA[mockery_verify]]></code>
    </MissingReturnType>
    <MissingThrowsDocblock>
      <code><![CDATA[throw new Exception(
                sprintf("The mock named '%s' has been already defined with a different mock configuration", $name)
            );]]></code>
    </MissingThrowsDocblock>
    <MixedArgument>
      <code><![CDATA[$className]]></code>
      <code><![CDATA[$className]]></code>
      <code><![CDATA[$def]]></code>
      <code><![CDATA[$name]]></code>
      <code><![CDATA[$name]]></code>
    </MixedArgument>
    <MixedArgumentTypeCoercion>
      <code><![CDATA[$keys]]></code>
    </MixedArgumentTypeCoercion>
    <MixedArrayAccess>
      <code><![CDATA[$arg[self::BLOCKS]]]></code>
    </MixedArrayAccess>
    <MixedArrayOffset>
      <code><![CDATA[$this->_namedMocks[$name]]]></code>
    </MixedArrayOffset>
    <MixedAssignment>
      <code><![CDATA[$blocks]]></code>
      <code><![CDATA[$className]]></code>
      <code><![CDATA[$def]]></code>
      <code><![CDATA[$exception]]></code>
      <code><![CDATA[$exceptions[]]]></code>
      <code><![CDATA[$hash]]></code>
      <code><![CDATA[$name]]></code>
    </MixedAssignment>
    <MixedInferredReturnType>
      <code><![CDATA[LegacyMockInterface|MockInterface]]></code>
    </MixedInferredReturnType>
    <MixedMethodCall>
      <code><![CDATA[atLeast]]></code>
      <code><![CDATA[byDefault]]></code>
      <code><![CDATA[getClassName]]></code>
      <code><![CDATA[getHash]]></code>
      <code><![CDATA[getName]]></code>
      <code><![CDATA[mockery_init]]></code>
      <code><![CDATA[new $internalMockName()]]></code>
      <code><![CDATA[once]]></code>
      <code><![CDATA[shouldReceive]]></code>
      <code><![CDATA[shouldReceive]]></code>
    </MixedMethodCall>
    <MixedPropertyTypeCoercion>
      <code><![CDATA[$this->_namedMocks]]></code>
    </MixedPropertyTypeCoercion>
    <MixedReturnStatement>
      <code><![CDATA[$mocks[$index]]]></code>
    </MixedReturnStatement>
    <MixedReturnTypeCoercion>
      <code><![CDATA[$exceptions]]></code>
      <code><![CDATA[array<Throwable>]]></code>
    </MixedReturnTypeCoercion>
    <MoreSpecificReturnType>
      <code><![CDATA[null|(LegacyMockInterface&MockInterface&TMock)]]></code>
    </MoreSpecificReturnType>
    <NoValue>
      <code><![CDATA[$arg]]></code>
      <code><![CDATA[$arg]]></code>
      <code><![CDATA[$arg]]></code>
      <code><![CDATA[$constructorArgs]]></code>
      <code><![CDATA[$quickDefinitions]]></code>
    </NoValue>
    <PossiblyUndefinedIntArrayOffset>
      <code><![CDATA[$parts[1]]]></code>
    </PossiblyUndefinedIntArrayOffset>
    <PossiblyUndefinedMethod>
      <code><![CDATA[mockery_thrownExceptions]]></code>
    </PossiblyUndefinedMethod>
    <PossiblyUnusedMethod>
      <code><![CDATA[instanceMock]]></code>
      <code><![CDATA[mockery_allocateOrder]]></code>
      <code><![CDATA[mockery_getCurrentOrder]]></code>
      <code><![CDATA[mockery_getGroups]]></code>
      <code><![CDATA[mockery_setGroup]]></code>
    </PossiblyUnusedMethod>
    <PossiblyUnusedReturnValue>
      <code><![CDATA[int]]></code>
    </PossiblyUnusedReturnValue>
    <PropertyTypeCoercion>
      <code><![CDATA[$this->_mocks]]></code>
      <code><![CDATA[$this->_mocks]]></code>
    </PropertyTypeCoercion>
    <RedundantConditionGivenDocblockType>
      <code><![CDATA[is_object($arg)]]></code>
    </RedundantConditionGivenDocblockType>
    <RiskyTruthyFalsyComparison>
      <code><![CDATA[!strpos($type, ']')]]></code>
      <code><![CDATA[strpos($type, ',')]]></code>
    </RiskyTruthyFalsyComparison>
    <TypeDoesNotContainType>
      <code><![CDATA[$constructorArgs !== null]]></code>
      <code><![CDATA[is_array($arg)]]></code>
    </TypeDoesNotContainType>
  </file>
  <file src="library/Mockery/CountValidator/AtLeast.php">
    <InvalidOperand>
      <code><![CDATA[$n]]></code>
      <code><![CDATA[$this->_limit]]></code>
    </InvalidOperand>
    <InvalidReturnType>
      <code><![CDATA[bool]]></code>
    </InvalidReturnType>
  </file>
  <file src="library/Mockery/CountValidator/AtMost.php">
    <InvalidOperand>
      <code><![CDATA[$n]]></code>
      <code><![CDATA[$this->_limit]]></code>
    </InvalidOperand>
    <InvalidReturnType>
      <code><![CDATA[bool]]></code>
    </InvalidReturnType>
  </file>
  <file src="library/Mockery/CountValidator/CountValidatorAbstract.php">
    <PossiblyNullPropertyAssignmentValue>
      <code><![CDATA[null]]></code>
      <code><![CDATA[null]]></code>
    </PossiblyNullPropertyAssignmentValue>
    <PossiblyUnusedMethod>
      <code><![CDATA[__construct]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/CountValidator/CountValidatorInterface.php">
    <PossiblyUnusedMethod>
      <code><![CDATA[isEligible]]></code>
      <code><![CDATA[validate]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/CountValidator/Exact.php">
    <InvalidOperand>
      <code><![CDATA[$n]]></code>
      <code><![CDATA[$this->_limit]]></code>
    </InvalidOperand>
    <InvalidReturnType>
      <code><![CDATA[bool]]></code>
    </InvalidReturnType>
    <PossiblyNullOperand>
      <code><![CDATA[$this->_expectation->getExceptionMessage()]]></code>
    </PossiblyNullOperand>
    <RiskyTruthyFalsyComparison>
      <code><![CDATA[$because]]></code>
    </RiskyTruthyFalsyComparison>
  </file>
  <file src="library/Mockery/Exception/BadMethodCallException.php">
    <MissingReturnType>
      <code><![CDATA[dismiss]]></code>
    </MissingReturnType>
    <UnusedClass>
      <code><![CDATA[BadMethodCallException]]></code>
    </UnusedClass>
  </file>
  <file src="library/Mockery/Exception/InvalidArgumentException.php">
    <UnusedClass>
      <code><![CDATA[InvalidArgumentException]]></code>
    </UnusedClass>
  </file>
  <file src="library/Mockery/Exception/InvalidCountException.php">
    <MissingThrowsDocblock>
      <code><![CDATA[throw new RuntimeException('Illegal comparative for expected call counts set: ' . $comp);]]></code>
    </MissingThrowsDocblock>
    <PossiblyUnusedMethod>
      <code><![CDATA[getActualCount]]></code>
      <code><![CDATA[getExpectedCount]]></code>
      <code><![CDATA[getExpectedCountComparative]]></code>
      <code><![CDATA[getMethodName]]></code>
      <code><![CDATA[getMockName]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/Exception/InvalidOrderException.php">
    <PossiblyUnusedMethod>
      <code><![CDATA[getActualOrder]]></code>
      <code><![CDATA[getExpectedOrder]]></code>
      <code><![CDATA[getMethodName]]></code>
      <code><![CDATA[getMockName]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/Exception/NoMatchingExpectationException.php">
    <PossiblyUnusedMethod>
      <code><![CDATA[getActualArguments]]></code>
      <code><![CDATA[getMethodName]]></code>
      <code><![CDATA[getMockName]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/Expectation.php">
    <DocblockTypeContradiction>
      <code><![CDATA[! is_int($index)]]></code>
      <code><![CDATA[$group === null]]></code>
      <code><![CDATA[$mock instanceof $mockClass]]></code>
      <code><![CDATA[is_int($limit)]]></code>
    </DocblockTypeContradiction>
    <InvalidArgument>
      <code><![CDATA[$argsOrClosure]]></code>
    </InvalidArgument>
    <InvalidOperand>
      <code><![CDATA[$index]]></code>
    </InvalidOperand>
    <InvalidStringClass>
      <code><![CDATA[new $exception($message, $code, $previous)]]></code>
      <code><![CDATA[new $this->_countValidatorClass($this, $limit)]]></code>
    </InvalidStringClass>
    <LessSpecificReturnType>
      <code><![CDATA[self]]></code>
      <code><![CDATA[self]]></code>
    </LessSpecificReturnType>
    <MissingClosureParamType>
      <code><![CDATA[$args]]></code>
      <code><![CDATA[$args]]></code>
    </MissingClosureParamType>
    <MissingClosureReturnType>
      <code><![CDATA[static function () use ($args) {]]></code>
      <code><![CDATA[static function (...$args) use ($index) {]]></code>
    </MissingClosureReturnType>
    <MissingParamType>
      <code><![CDATA[$code]]></code>
      <code><![CDATA[$exception]]></code>
      <code><![CDATA[$message]]></code>
    </MissingParamType>
    <MissingReturnType>
      <code><![CDATA[andThrows]]></code>
      <code><![CDATA[between]]></code>
      <code><![CDATA[getName]]></code>
    </MissingReturnType>
    <MissingThrowsDocblock>
      <code><![CDATA[evaluate]]></code>
      <code><![CDATA[evaluate]]></code>
      <code><![CDATA[makeExpectationDefault]]></code>
      <code><![CDATA[mockery_validateOrder]]></code>
      <code><![CDATA[mockery_validateOrder]]></code>
      <code><![CDATA[throw new Exception(
                'Mock Objects not created from a loaded/existing class are incapable of passing method calls through to a parent class'
            );]]></code>
      <code><![CDATA[throw new InvalidArgumentException(
                'Invalid argument index supplied. Index must be a non-negative integer.'
            );]]></code>
      <code><![CDATA[throw new InvalidArgumentException(sprintf(
            'Call to %s with an invalid argument (%s), only array and closure are allowed',
            __METHOD__,
            $argsOrClosure
        ));]]></code>
      <code><![CDATA[times]]></code>
      <code><![CDATA[times]]></code>
      <code><![CDATA[times]]></code>
      <code><![CDATA[times]]></code>
      <code><![CDATA[times]]></code>
    </MissingThrowsDocblock>
    <MixedArgument>
      <code><![CDATA[$code]]></code>
      <code><![CDATA[$exception]]></code>
      <code><![CDATA[$groups]]></code>
      <code><![CDATA[$message]]></code>
      <code><![CDATA[$return]]></code>
      <code><![CDATA[$values]]></code>
      <code><![CDATA[func_get_args()]]></code>
      <code><![CDATA[func_get_args()]]></code>
    </MixedArgument>
    <MixedArgumentTypeCoercion>
      <code><![CDATA[$firstCorrespondingKey]]></code>
    </MixedArgumentTypeCoercion>
    <MixedArrayAccess>
      <code><![CDATA[$groups[$group]]]></code>
    </MixedArrayAccess>
    <MixedAssignment>
      <code><![CDATA[$arg]]></code>
      <code><![CDATA[$expectedArg]]></code>
      <code><![CDATA[$groups]]></code>
      <code><![CDATA[$lastExpectedArgument]]></code>
      <code><![CDATA[$newValidators[]]]></code>
      <code><![CDATA[$result]]></code>
      <code><![CDATA[$return]]></code>
      <code><![CDATA[$validator]]></code>
      <code><![CDATA[$validator]]></code>
      <code><![CDATA[$validator]]></code>
      <code><![CDATA[$value]]></code>
      <code><![CDATA[$values]]></code>
    </MixedAssignment>
    <MixedClone>
      <code><![CDATA[clone $validator]]></code>
    </MixedClone>
    <MixedFunctionCall>
      <code><![CDATA[array_shift($this->_closureQueue)(...$args)]]></code>
      <code><![CDATA[current($this->_closureQueue)(...$args)]]></code>
    </MixedFunctionCall>
    <MixedInferredReturnType>
      <code><![CDATA[bool]]></code>
      <code><![CDATA[int]]></code>
    </MixedInferredReturnType>
    <MixedMethodCall>
      <code><![CDATA[isEligible]]></code>
      <code><![CDATA[mockery_allocateOrder]]></code>
      <code><![CDATA[mockery_allocateOrder]]></code>
      <code><![CDATA[mockery_getGroups]]></code>
      <code><![CDATA[mockery_setGroup]]></code>
      <code><![CDATA[validate]]></code>
    </MixedMethodCall>
    <MixedReturnStatement>
      <code><![CDATA[$groups[$group]]]></code>
      <code><![CDATA[$ordering->mockery_allocateOrder()]]></code>
      <code><![CDATA[$result]]></code>
    </MixedReturnStatement>
    <PossiblyFalseArgument>
      <code><![CDATA[$firstCorrespondingKey]]></code>
    </PossiblyFalseArgument>
    <PossiblyInvalidFunctionCall>
      <code><![CDATA[current($this->_closureQueue)(...$args)]]></code>
    </PossiblyInvalidFunctionCall>
    <PossiblyNullArgument>
      <code><![CDATA[$group]]></code>
      <code><![CDATA[$group]]></code>
    </PossiblyNullArgument>
    <PossiblyNullFunctionCall>
      <code><![CDATA[array_shift($this->_closureQueue)(...$args)]]></code>
    </PossiblyNullFunctionCall>
    <PossiblyNullPropertyAssignmentValue>
      <code><![CDATA[null]]></code>
      <code><![CDATA[null]]></code>
      <code><![CDATA[null]]></code>
      <code><![CDATA[null]]></code>
    </PossiblyNullPropertyAssignmentValue>
    <PossiblyUndefinedIntArrayOffset>
      <code><![CDATA[$this->_expectedArgs[0]]]></code>
      <code><![CDATA[$this->_expectedArgs[0]]]></code>
    </PossiblyUndefinedIntArrayOffset>
    <PossiblyUnusedMethod>
      <code><![CDATA[__construct]]></code>
      <code><![CDATA[andReturnArg]]></code>
      <code><![CDATA[andReturnFalse]]></code>
      <code><![CDATA[andReturnNull]]></code>
      <code><![CDATA[andReturnSelf]]></code>
      <code><![CDATA[andReturnTrue]]></code>
      <code><![CDATA[andReturnUndefined]]></code>
      <code><![CDATA[andReturnUsing]]></code>
      <code><![CDATA[andThrowExceptions]]></code>
      <code><![CDATA[andThrows]]></code>
      <code><![CDATA[andYield]]></code>
      <code><![CDATA[because]]></code>
      <code><![CDATA[between]]></code>
      <code><![CDATA[byDefault]]></code>
      <code><![CDATA[globally]]></code>
      <code><![CDATA[isCallCountConstrained]]></code>
      <code><![CDATA[isEligible]]></code>
      <code><![CDATA[once]]></code>
      <code><![CDATA[ordered]]></code>
      <code><![CDATA[passthru]]></code>
      <code><![CDATA[set]]></code>
      <code><![CDATA[twice]]></code>
      <code><![CDATA[with]]></code>
      <code><![CDATA[withSomeOfArgs]]></code>
      <code><![CDATA[zeroOrMoreTimes]]></code>
    </PossiblyUnusedMethod>
    <PossiblyUnusedProperty>
      <code><![CDATA[$_returnValue]]></code>
    </PossiblyUnusedProperty>
    <PossiblyUnusedReturnValue>
      <code><![CDATA[mixed]]></code>
    </PossiblyUnusedReturnValue>
    <RedundantConditionGivenDocblockType>
      <code><![CDATA[$argsOrClosure instanceof Closure]]></code>
    </RedundantConditionGivenDocblockType>
    <TooManyArguments>
      <code><![CDATA[mockery_validateOrder]]></code>
    </TooManyArguments>
    <UndefinedClass>
      <code><![CDATA[Hamcrest_Matcher]]></code>
    </UndefinedClass>
    <UndefinedInterfaceMethod>
      <code><![CDATA[mockery_callSubjectMethod]]></code>
      <code><![CDATA[mockery_isInstance]]></code>
      <code><![CDATA[mockery_returnValueForMethod]]></code>
    </UndefinedInterfaceMethod>
    <UnusedMethod>
      <code><![CDATA[isAndAnyOtherArgumentsMatcher]]></code>
    </UnusedMethod>
  </file>
  <file src="library/Mockery/ExpectationDirector.php">
    <MissingReturnType>
      <code><![CDATA[addExpectation]]></code>
    </MissingReturnType>
    <MissingThrowsDocblock>
      <code><![CDATA[throw $exception;]]></code>
    </MissingThrowsDocblock>
    <MixedMethodCall>
      <code><![CDATA[verifyCall]]></code>
    </MixedMethodCall>
    <PossiblyNullPropertyAssignmentValue>
      <code><![CDATA[null]]></code>
      <code><![CDATA[null]]></code>
      <code><![CDATA[null]]></code>
    </PossiblyNullPropertyAssignmentValue>
    <PossiblyUnusedMethod>
      <code><![CDATA[__construct]]></code>
      <code><![CDATA[addExpectation]]></code>
      <code><![CDATA[call]]></code>
      <code><![CDATA[getExpectationCount]]></code>
      <code><![CDATA[verify]]></code>
    </PossiblyUnusedMethod>
    <PossiblyUnusedProperty>
      <code><![CDATA[$_expectedOrder]]></code>
    </PossiblyUnusedProperty>
    <UndefinedInterfaceMethod>
      <code><![CDATA[isCallCountConstrained]]></code>
      <code><![CDATA[isEligible]]></code>
      <code><![CDATA[matchArgs]]></code>
      <code><![CDATA[matchArgs]]></code>
      <code><![CDATA[verify]]></code>
      <code><![CDATA[verify]]></code>
    </UndefinedInterfaceMethod>
  </file>
  <file src="library/Mockery/ExpectationInterface.php">
    <PossiblyUnusedMethod>
      <code><![CDATA[andReturns]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/ExpectsHigherOrderMessage.php">
    <MixedInferredReturnType>
      <code><![CDATA[Expectation|ExpectationInterface|HigherOrderMessage]]></code>
    </MixedInferredReturnType>
    <MixedReturnStatement>
      <code><![CDATA[$expectation->once()]]></code>
    </MixedReturnStatement>
    <PossiblyUnusedMethod>
      <code><![CDATA[__construct]]></code>
    </PossiblyUnusedMethod>
    <UndefinedInterfaceMethod>
      <code><![CDATA[once]]></code>
    </UndefinedInterfaceMethod>
    <UndefinedMagicMethod>
      <code><![CDATA[once]]></code>
    </UndefinedMagicMethod>
  </file>
  <file src="library/Mockery/Generator/CachingGenerator.php">
    <MixedInferredReturnType>
      <code><![CDATA[string]]></code>
    </MixedInferredReturnType>
    <MixedPropertyTypeCoercion>
      <code><![CDATA[$this->cache]]></code>
    </MixedPropertyTypeCoercion>
    <MixedReturnStatement>
      <code><![CDATA[$this->cache[$hash] = $this->generator->generate($config)]]></code>
    </MixedReturnStatement>
  </file>
  <file src="library/Mockery/Generator/DefinedTargetClass.php">
    <LessSpecificReturnStatement>
      <code><![CDATA[array_unique(
            array_merge(
                ['\AllowDynamicProperties'],
                array_map(
                    static function (ReflectionAttribute $attribute): string {
                        return '\\' . $attribute->getName();
                    },
                    $this->rfc->getAttributes()
                )
            )
        )]]></code>
    </LessSpecificReturnStatement>
    <MixedArgument>
      <code><![CDATA[$this->rfc->getAttributes()]]></code>
    </MixedArgument>
    <MixedOperand>
      <code><![CDATA[$attribute->getName()]]></code>
    </MixedOperand>
    <MoreSpecificImplementedParamType>
      <code><![CDATA[$interface]]></code>
    </MoreSpecificImplementedParamType>
    <MoreSpecificReturnType>
      <code><![CDATA[list<class-string>]]></code>
    </MoreSpecificReturnType>
  </file>
  <file src="library/Mockery/Generator/Generator.php">
    <MissingReturnType>
      <code><![CDATA[generate]]></code>
    </MissingReturnType>
  </file>
  <file src="library/Mockery/Generator/Method.php">
    <PossiblyUnusedMethod>
      <code><![CDATA[__call]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/Generator/MockConfiguration.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[$target]]></code>
      <code><![CDATA[$target]]></code>
      <code><![CDATA[$targetInterface]]></code>
      <code><![CDATA[$targetTrait]]></code>
      <code><![CDATA[$targets]]></code>
      <code><![CDATA[$this->targetClassName]]></code>
      <code><![CDATA['\\Iterator']]></code>
      <code><![CDATA['\\IteratorAggregate']]></code>
      <code><![CDATA['\\IteratorAggregate']]></code>
    </ArgumentTypeCoercion>
    <InvalidArgument>
      <code><![CDATA[$targets]]></code>
    </InvalidArgument>
    <InvalidPropertyAssignmentValue>
      <code><![CDATA[$this->targetTraits]]></code>
      <code><![CDATA[array_unique($this->targetTraits)]]></code>
    </InvalidPropertyAssignmentValue>
    <LessSpecificReturnStatement>
      <code><![CDATA[$this->allMethods = $methods]]></code>
      <code><![CDATA[$this->targetInterfaces]]></code>
      <code><![CDATA[$this->targetInterfaces = array_unique($this->targetInterfaces)]]></code>
      <code><![CDATA[$this->targetTraits]]></code>
      <code><![CDATA[$this->targetTraits]]></code>
      <code><![CDATA[array_filter($methods, static function ($method) use ($whitelist) {
                if ($method->isAbstract()) {
                    return true;
                }

                return in_array(strtolower($method->getName()), $whitelist, true);
            })]]></code>
    </LessSpecificReturnStatement>
    <MissingPropertyType>
      <code><![CDATA[$blackListedMethods]]></code>
      <code><![CDATA[$constantsMap]]></code>
    </MissingPropertyType>
    <MissingReturnType>
      <code><![CDATA[addTarget]]></code>
      <code><![CDATA[addTargetInterfaceName]]></code>
      <code><![CDATA[addTargetTraitName]]></code>
      <code><![CDATA[addTargets]]></code>
      <code><![CDATA[setTargetClassName]]></code>
      <code><![CDATA[setTargetObject]]></code>
    </MissingReturnType>
    <MissingThrowsDocblock>
      <code><![CDATA[throw new Exception(
                    'The class ' . $this->targetClassName . ' is marked final and its methods'
                    . ' cannot be replaced. Classes marked final can be passed in'
                    . ' to \Mockery::mock() as instantiated objects to create a'
                    . ' partial mock, but only if the mock is not subject to type'
                    . ' hinting checks.'
                );]]></code>
    </MissingThrowsDocblock>
    <MixedArgument>
      <code><![CDATA[$alias]]></code>
      <code><![CDATA[$alias]]></code>
      <code><![CDATA[$this->blackListedMethods]]></code>
      <code><![CDATA[$this->constantsMap]]></code>
    </MixedArgument>
    <MixedAssignment>
      <code><![CDATA[$alias]]></code>
    </MixedAssignment>
    <MixedInferredReturnType>
      <code><![CDATA[array<string,scalar|array<scalar>>]]></code>
      <code><![CDATA[array<string>]]></code>
    </MixedInferredReturnType>
    <MixedMethodCall>
      <code><![CDATA[addPart]]></code>
      <code><![CDATA[build]]></code>
    </MixedMethodCall>
    <MixedReturnStatement>
      <code><![CDATA[$this->blackListedMethods]]></code>
      <code><![CDATA[$this->constantsMap]]></code>
    </MixedReturnStatement>
    <MoreSpecificReturnType>
      <code><![CDATA[list<Method>]]></code>
      <code><![CDATA[list<Method>]]></code>
      <code><![CDATA[list<TargetClassInterface>]]></code>
      <code><![CDATA[list<TargetClassInterface>]]></code>
    </MoreSpecificReturnType>
    <PossiblyNullArgument>
      <code><![CDATA[$this->getName()]]></code>
      <code><![CDATA[$this->getName()]]></code>
    </PossiblyNullArgument>
    <PossiblyUndefinedIntArrayOffset>
      <code><![CDATA[$params[1]]]></code>
    </PossiblyUndefinedIntArrayOffset>
    <PossiblyUnusedMethod>
      <code><![CDATA[getParameterOverrides]]></code>
    </PossiblyUnusedMethod>
    <PropertyTypeCoercion>
      <code><![CDATA[$methods]]></code>
    </PropertyTypeCoercion>
  </file>
  <file src="library/Mockery/Generator/MockConfigurationBuilder.php">
    <MissingParamType>
      <code><![CDATA[$whiteListedMethod]]></code>
    </MissingParamType>
    <MissingReturnType>
      <code><![CDATA[setInstanceMock]]></code>
      <code><![CDATA[setMockOriginalDestructor]]></code>
      <code><![CDATA[setName]]></code>
    </MissingReturnType>
    <MixedArgumentTypeCoercion>
      <code><![CDATA[$this->constantsMap]]></code>
      <code><![CDATA[$this->parameterOverrides]]></code>
      <code><![CDATA[$this->targets]]></code>
      <code><![CDATA[$this->whiteListedMethods]]></code>
    </MixedArgumentTypeCoercion>
    <MixedAssignment>
      <code><![CDATA[$method]]></code>
    </MixedAssignment>
    <PossiblyUnusedMethod>
      <code><![CDATA[addWhiteListedMethods]]></code>
      <code><![CDATA[setBlackListedMethods]]></code>
      <code><![CDATA[setWhiteListedMethods]]></code>
    </PossiblyUnusedMethod>
    <PropertyNotSetInConstructor>
      <code><![CDATA[$name]]></code>
    </PropertyNotSetInConstructor>
    <PropertyTypeCoercion>
      <code><![CDATA[array_diff($this->blackListedMethods, $this->php7SemiReservedKeywords)]]></code>
    </PropertyTypeCoercion>
    <RedundantCastGivenDocblockType>
      <code><![CDATA[(bool) $instanceMock]]></code>
      <code><![CDATA[(bool) $mockDestructor]]></code>
    </RedundantCastGivenDocblockType>
  </file>
  <file src="library/Mockery/Generator/MockDefinition.php">
    <InvalidNullableReturnType>
      <code><![CDATA[string]]></code>
    </InvalidNullableReturnType>
    <NullableReturnStatement>
      <code><![CDATA[$this->config->getName()]]></code>
    </NullableReturnStatement>
    <PossiblyUnusedMethod>
      <code><![CDATA[getConfig]]></code>
    </PossiblyUnusedMethod>
    <RiskyTruthyFalsyComparison>
      <code><![CDATA[! $config->getName()]]></code>
    </RiskyTruthyFalsyComparison>
  </file>
  <file src="library/Mockery/Generator/MockNameBuilder.php">
    <MissingReturnType>
      <code><![CDATA[addPart]]></code>
    </MissingReturnType>
  </file>
  <file src="library/Mockery/Generator/Parameter.php">
    <InvalidArgument>
      <code><![CDATA[false]]></code>
    </InvalidArgument>
    <InvalidOperand>
      <code><![CDATA[self::$parameterCounter++]]></code>
    </InvalidOperand>
    <InvalidReturnStatement>
      <code><![CDATA[class_exists($typeHint) ? DefinedTargetClass::factory($typeHint, false) : null]]></code>
    </InvalidReturnStatement>
    <InvalidReturnType>
      <code><![CDATA[null|ReflectionClass]]></code>
    </InvalidReturnType>
    <PossiblyNullArgument>
      <code><![CDATA[$typeHint]]></code>
    </PossiblyNullArgument>
    <PossiblyUnusedMethod>
      <code><![CDATA[__call]]></code>
      <code><![CDATA[getClass]]></code>
      <code><![CDATA[getTypeHintAsString]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/Generator/StringManipulation/Pass/ClassPass.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[$className]]></code>
    </ArgumentTypeCoercion>
  </file>
  <file src="library/Mockery/Generator/StringManipulation/Pass/ConstantsPass.php">
    <MixedAssignment>
      <code><![CDATA[$value]]></code>
    </MixedAssignment>
    <PossiblyInvalidIterator>
      <code><![CDATA[$cm[$name]]]></code>
    </PossiblyInvalidIterator>
    <PossiblyNullArgument>
      <code><![CDATA[$name]]></code>
    </PossiblyNullArgument>
  </file>
  <file src="library/Mockery/Generator/StringManipulation/Pass/InstanceMockPass.php">
    <MissingParamType>
      <code><![CDATA[$class]]></code>
      <code><![CDATA[$code]]></code>
    </MissingParamType>
    <MissingReturnType>
      <code><![CDATA[appendToClass]]></code>
    </MissingReturnType>
    <MixedArgument>
      <code><![CDATA[$class]]></code>
      <code><![CDATA[$class]]></code>
    </MixedArgument>
    <MixedInferredReturnType>
      <code><![CDATA[string]]></code>
    </MixedInferredReturnType>
    <MixedOperand>
      <code><![CDATA[$code]]></code>
    </MixedOperand>
    <MixedReturnStatement>
      <code><![CDATA[$this->appendToClass($code, static::INSTANCE_MOCK_CODE)]]></code>
    </MixedReturnStatement>
    <PossiblyFalseArgument>
      <code><![CDATA[$lastBrace]]></code>
    </PossiblyFalseArgument>
  </file>
  <file src="library/Mockery/Generator/StringManipulation/Pass/InterfacePass.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[$name]]></code>
    </ArgumentTypeCoercion>
    <MixedArgument>
      <code><![CDATA[$i->getName()]]></code>
    </MixedArgument>
    <MixedMethodCall>
      <code><![CDATA[getName]]></code>
    </MixedMethodCall>
    <MixedOperand>
      <code><![CDATA[$code]]></code>
    </MixedOperand>
  </file>
  <file src="library/Mockery/Generator/StringManipulation/Pass/MagicMethodTypeHintsPass.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[$this->getDeclarationRegex($method->getName())]]></code>
      <code><![CDATA[$this->getDeclarationRegex($method->getName())]]></code>
      <code><![CDATA[$this->getDeclarationRegex($method->getName())]]></code>
    </ArgumentTypeCoercion>
    <InvalidArgument>
      <code><![CDATA[$code]]></code>
      <code><![CDATA[$code]]></code>
      <code><![CDATA[$code]]></code>
      <code><![CDATA[$code]]></code>
    </InvalidArgument>
    <InvalidReturnStatement>
      <code><![CDATA[$code]]></code>
    </InvalidReturnStatement>
    <InvalidReturnType>
      <code><![CDATA[string]]></code>
    </InvalidReturnType>
    <LessSpecificReturnType>
      <code><![CDATA[array]]></code>
      <code><![CDATA[string]]></code>
      <code><![CDATA[string]]></code>
    </LessSpecificReturnType>
    <MissingReturnType>
      <code><![CDATA[renderTypeHint]]></code>
    </MissingReturnType>
    <MixedArgument>
      <code><![CDATA[$method]]></code>
    </MixedArgument>
    <MixedAssignment>
      <code><![CDATA[$method]]></code>
      <code><![CDATA[$name]]></code>
    </MixedAssignment>
    <MixedOperand>
      <code><![CDATA[$name]]></code>
      <code><![CDATA[$this->renderTypeHint($parameter)]]></code>
    </MixedOperand>
    <PossiblyUndefinedIntArrayOffset>
      <code><![CDATA[$matches[0]]]></code>
    </PossiblyUndefinedIntArrayOffset>
  </file>
  <file src="library/Mockery/Generator/StringManipulation/Pass/MethodDefinitionPass.php">
    <InvalidCast>
      <code><![CDATA[$param]]></code>
    </InvalidCast>
    <InvalidMethodCall>
      <code><![CDATA[getName]]></code>
      <code><![CDATA[isPassedByReference]]></code>
    </InvalidMethodCall>
    <MissingParamType>
      <code><![CDATA[$class]]></code>
      <code><![CDATA[$code]]></code>
      <code><![CDATA[$config]]></code>
      <code><![CDATA[$config]]></code>
      <code><![CDATA[$method]]></code>
    </MissingParamType>
    <MissingReturnType>
      <code><![CDATA[appendToClass]]></code>
      <code><![CDATA[renderMethodBody]]></code>
      <code><![CDATA[renderParams]]></code>
      <code><![CDATA[renderReturnType]]></code>
      <code><![CDATA[renderTypeHint]]></code>
    </MissingReturnType>
    <MixedArgument>
      <code><![CDATA[$class]]></code>
      <code><![CDATA[$class]]></code>
      <code><![CDATA[$class->getName()]]></code>
      <code><![CDATA[$method->getParameters()]]></code>
      <code><![CDATA[$overrides[$class_name][$method->getName()]]]></code>
      <code><![CDATA[$overrides[strtolower($class->getName())][$method->getName()]]]></code>
    </MixedArgument>
    <MixedArgumentTypeCoercion>
      <code><![CDATA[$param]]></code>
    </MixedArgumentTypeCoercion>
    <MixedArrayAccess>
      <code><![CDATA[$overrides[$class_name]]]></code>
      <code><![CDATA[$overrides[$class_name][$method->getName()]]]></code>
      <code><![CDATA[$overrides[strtolower($class->getName())]]]></code>
      <code><![CDATA[$overrides[strtolower($class->getName())][$method->getName()]]]></code>
    </MixedArrayAccess>
    <MixedArrayOffset>
      <code><![CDATA[$overrides[$class_name][$method->getName()]]]></code>
      <code><![CDATA[$overrides[$class_name][$method->getName()]]]></code>
    </MixedArrayOffset>
    <MixedAssignment>
      <code><![CDATA[$class]]></code>
      <code><![CDATA[$code]]></code>
      <code><![CDATA[$defaultValue]]></code>
      <code><![CDATA[$overrides]]></code>
      <code><![CDATA[$overrides]]></code>
      <code><![CDATA[$paramDef]]></code>
    </MixedAssignment>
    <MixedInferredReturnType>
      <code><![CDATA[string]]></code>
    </MixedInferredReturnType>
    <MixedMethodCall>
      <code><![CDATA[getDeclaringClass]]></code>
      <code><![CDATA[getName]]></code>
      <code><![CDATA[getName]]></code>
      <code><![CDATA[getName]]></code>
      <code><![CDATA[getParameterOverrides]]></code>
      <code><![CDATA[getParameterOverrides]]></code>
      <code><![CDATA[getParameters]]></code>
      <code><![CDATA[getReturnType]]></code>
      <code><![CDATA[isStatic]]></code>
    </MixedMethodCall>
    <MixedOperand>
      <code><![CDATA[$code]]></code>
      <code><![CDATA[$paramDef]]></code>
      <code><![CDATA[$this->renderMethodBody($method, $config)]]></code>
      <code><![CDATA[$this->renderParams($method, $config)]]></code>
      <code><![CDATA[$this->renderReturnType($method)]]></code>
    </MixedOperand>
    <MixedReturnStatement>
      <code><![CDATA[$code]]></code>
    </MixedReturnStatement>
    <PossiblyFalseArgument>
      <code><![CDATA[$lastBrace]]></code>
    </PossiblyFalseArgument>
    <PossiblyUndefinedIntArrayOffset>
      <code><![CDATA[$matches[1]]]></code>
    </PossiblyUndefinedIntArrayOffset>
    <RedundantIdentityWithTrue>
      <code><![CDATA[$param->isDefaultValueAvailable() !== false]]></code>
    </RedundantIdentityWithTrue>
    <RiskyTruthyFalsyComparison>
      <code><![CDATA[$type]]></code>
    </RiskyTruthyFalsyComparison>
    <TypeDoesNotContainType>
      <code><![CDATA[strpos($param, '&') !== false]]></code>
    </TypeDoesNotContainType>
  </file>
  <file src="library/Mockery/Generator/StringManipulation/Pass/RemoveBuiltinMethodsThatAreFinalPass.php">
    <MissingPropertyType>
      <code><![CDATA[$methods]]></code>
    </MissingPropertyType>
    <MixedArgument>
      <code><![CDATA[$this->methods[$method->getName()]]]></code>
    </MixedArgument>
    <MixedArrayAccess>
      <code><![CDATA[$this->methods[$method->getName()]]]></code>
    </MixedArrayAccess>
  </file>
  <file src="library/Mockery/Generator/StringManipulation/Pass/RemoveUnserializeForInternalSerializableClassesPass.php">
    <MissingParamType>
      <code><![CDATA[$class]]></code>
      <code><![CDATA[$code]]></code>
    </MissingParamType>
    <MissingReturnType>
      <code><![CDATA[appendToClass]]></code>
    </MissingReturnType>
    <MixedArgument>
      <code><![CDATA[$class]]></code>
      <code><![CDATA[$class]]></code>
    </MixedArgument>
    <MixedInferredReturnType>
      <code><![CDATA[string]]></code>
    </MixedInferredReturnType>
    <MixedOperand>
      <code><![CDATA[$code]]></code>
    </MixedOperand>
    <MixedReturnStatement>
      <code><![CDATA[$this->appendToClass(
            $code,
            PHP_VERSION_ID < 80100 ? self::DUMMY_METHOD_DEFINITION_LEGACY : self::DUMMY_METHOD_DEFINITION
        )]]></code>
    </MixedReturnStatement>
    <PossiblyFalseArgument>
      <code><![CDATA[$lastBrace]]></code>
    </PossiblyFalseArgument>
  </file>
  <file src="library/Mockery/Generator/StringManipulationGenerator.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[$className]]></code>
    </ArgumentTypeCoercion>
    <MissingThrowsDocblock>
      <code><![CDATA[new MockDefinition($namedConfig, $code)]]></code>
    </MissingThrowsDocblock>
    <PossiblyUnusedMethod>
      <code><![CDATA[addPass]]></code>
    </PossiblyUnusedMethod>
    <RiskyTruthyFalsyComparison>
      <code><![CDATA[$config->getName()]]></code>
    </RiskyTruthyFalsyComparison>
    <UnsafeInstantiation>
      <code><![CDATA[new static([
            new CallTypeHintPass(),
            new MagicMethodTypeHintsPass(),
            new ClassPass(),
            new TraitPass(),
            new ClassNamePass(),
            new InstanceMockPass(),
            new InterfacePass(),
            new AvoidMethodClashPass(),
            new MethodDefinitionPass(),
            new RemoveUnserializeForInternalSerializableClassesPass(),
            new RemoveBuiltinMethodsThatAreFinalPass(),
            new RemoveDestructorPass(),
            new ConstantsPass(),
            new ClassAttributesPass(),
        ])]]></code>
    </UnsafeInstantiation>
  </file>
  <file src="library/Mockery/Generator/TargetClassInterface.php">
    <PossiblyUnusedMethod>
      <code><![CDATA[getShortName]]></code>
      <code><![CDATA[inNamespace]]></code>
      <code><![CDATA[isAbstract]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/Generator/UndefinedTargetClass.php">
    <MoreSpecificImplementedParamType>
      <code><![CDATA[$interface]]></code>
    </MoreSpecificImplementedParamType>
  </file>
  <file src="library/Mockery/HigherOrderMessage.php">
    <MissingParamType>
      <code><![CDATA[$method]]></code>
    </MissingParamType>
    <MixedAssignment>
      <code><![CDATA[$expectation]]></code>
      <code><![CDATA[$this->method]]></code>
    </MixedAssignment>
    <MixedInferredReturnType>
      <code><![CDATA[Expectation|ExpectationInterface|HigherOrderMessage]]></code>
    </MixedInferredReturnType>
    <MixedMethodCall>
      <code><![CDATA[withArgs]]></code>
    </MixedMethodCall>
    <MixedReturnStatement>
      <code><![CDATA[$expectation->withArgs($args)]]></code>
      <code><![CDATA[$this->mock->{$this->method}($method, $args)]]></code>
    </MixedReturnStatement>
  </file>
  <file src="library/Mockery/Instantiator.php">
    <LessSpecificReturnType>
      <code><![CDATA[Closure]]></code>
    </LessSpecificReturnType>
    <MissingClosureReturnType>
      <code><![CDATA[static function () use ($serializedString) {]]></code>
    </MissingClosureReturnType>
    <MissingThrowsDocblock>
      <code><![CDATA[attemptInstantiationViaUnSerialization]]></code>
      <code><![CDATA[getReflectionClass]]></code>
    </MissingThrowsDocblock>
    <MixedArgument>
      <code><![CDATA[static function ($code, $message, $file, $line) use ($reflectionClass, &$error): void {
            $msg = sprintf(
                'Could not produce an instance of "%s" via un-serialization, since an error was triggered in file "%s" at line "%d"',
                $reflectionClass->getName(),
                $file,
                $line
            );

            $error = new UnexpectedValueException($msg, 0, new Exception($message, $code));
        }]]></code>
    </MixedArgument>
    <MixedInferredReturnType>
      <code><![CDATA[TClass]]></code>
    </MixedInferredReturnType>
    <MixedReturnStatement>
      <code><![CDATA[$this->buildFactory($className)()]]></code>
    </MixedReturnStatement>
    <UndefinedVariable>
      <code><![CDATA[$error]]></code>
      <code><![CDATA[$error]]></code>
    </UndefinedVariable>
    <UnusedMethod>
      <code><![CDATA[hasInternalAncestors]]></code>
    </UnusedMethod>
  </file>
  <file src="library/Mockery/LegacyMockInterface.php">
    <PossiblyUnusedMethod>
      <code><![CDATA[byDefault]]></code>
      <code><![CDATA[makePartial]]></code>
      <code><![CDATA[mockery_allocateOrder]]></code>
      <code><![CDATA[mockery_findExpectation]]></code>
      <code><![CDATA[mockery_getCurrentOrder]]></code>
      <code><![CDATA[mockery_getGroups]]></code>
      <code><![CDATA[mockery_getMockableProperties]]></code>
      <code><![CDATA[mockery_init]]></code>
      <code><![CDATA[mockery_setCurrentOrder]]></code>
      <code><![CDATA[mockery_setExpectationsFor]]></code>
      <code><![CDATA[mockery_setGroup]]></code>
      <code><![CDATA[shouldAllowMockingMethod]]></code>
      <code><![CDATA[shouldAllowMockingProtectedMethods]]></code>
      <code><![CDATA[shouldDeferMissing]]></code>
      <code><![CDATA[shouldHaveBeenCalled]]></code>
      <code><![CDATA[shouldHaveReceived]]></code>
      <code><![CDATA[shouldNotHaveBeenCalled]]></code>
      <code><![CDATA[shouldNotHaveReceived]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/Loader/RequireLoader.php">
    <RiskyTruthyFalsyComparison>
      <code><![CDATA[glob($this->path . DIRECTORY_SEPARATOR . 'Mockery_*.php')]]></code>
    </RiskyTruthyFalsyComparison>
    <UnusedClass>
      <code><![CDATA[RequireLoader]]></code>
    </UnusedClass>
  </file>
  <file src="library/Mockery/Matcher/AndAnyOtherArgs.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
  </file>
  <file src="library/Mockery/Matcher/Any.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
  </file>
  <file src="library/Mockery/Matcher/AnyArgs.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
  </file>
  <file src="library/Mockery/Matcher/AnyOf.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <InvalidArgument>
      <code><![CDATA[$this->_expected]]></code>
    </InvalidArgument>
  </file>
  <file src="library/Mockery/Matcher/Closure.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <InvalidFunctionCall>
      <code><![CDATA[($this->_expected)($actual)]]></code>
    </InvalidFunctionCall>
  </file>
  <file src="library/Mockery/Matcher/Contains.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <MixedArgumentTypeCoercion>
      <code><![CDATA[$actual]]></code>
    </MixedArgumentTypeCoercion>
    <UndefinedClass>
      <code><![CDATA[$this->_expected]]></code>
      <code><![CDATA[$this->_expected]]></code>
    </UndefinedClass>
  </file>
  <file src="library/Mockery/Matcher/Ducktype.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <InvalidArgument>
      <code><![CDATA[$this->_expected]]></code>
    </InvalidArgument>
    <UndefinedClass>
      <code><![CDATA[$this->_expected]]></code>
    </UndefinedClass>
  </file>
  <file src="library/Mockery/Matcher/HasKey.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <InvalidArgument>
      <code><![CDATA[$this->_expected]]></code>
      <code><![CDATA[$this->_expected]]></code>
    </InvalidArgument>
  </file>
  <file src="library/Mockery/Matcher/HasValue.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <InvalidCast>
      <code><![CDATA[$this->_expected]]></code>
    </InvalidCast>
  </file>
  <file src="library/Mockery/Matcher/IsEqual.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
  </file>
  <file src="library/Mockery/Matcher/IsSame.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
  </file>
  <file src="library/Mockery/Matcher/MatcherAbstract.php">
    <MixedPropertyTypeCoercion>
      <code><![CDATA[$expected]]></code>
    </MixedPropertyTypeCoercion>
    <PossiblyNullPropertyAssignmentValue>
      <code><![CDATA[null]]></code>
    </PossiblyNullPropertyAssignmentValue>
    <UndefinedDocblockClass>
      <code><![CDATA[TExpected]]></code>
      <code><![CDATA[protected $_expected = null;]]></code>
    </UndefinedDocblockClass>
  </file>
  <file src="library/Mockery/Matcher/MultiArgumentClosure.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <InvalidFunctionCall>
      <code><![CDATA[($this->_expected)(...$actual)]]></code>
    </InvalidFunctionCall>
  </file>
  <file src="library/Mockery/Matcher/MustBe.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
  </file>
  <file src="library/Mockery/Matcher/NoArgs.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <MixedArgumentTypeCoercion>
      <code><![CDATA[$actual]]></code>
    </MixedArgumentTypeCoercion>
  </file>
  <file src="library/Mockery/Matcher/Not.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
  </file>
  <file src="library/Mockery/Matcher/NotAnyOf.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <UndefinedClass>
      <code><![CDATA[$this->_expected]]></code>
    </UndefinedClass>
  </file>
  <file src="library/Mockery/Matcher/Pattern.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <InvalidArgument>
      <code><![CDATA[$this->_expected]]></code>
    </InvalidArgument>
    <InvalidCast>
      <code><![CDATA[$this->_expected]]></code>
    </InvalidCast>
  </file>
  <file src="library/Mockery/Matcher/Subset.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <InvalidOperand>
      <code><![CDATA[$k]]></code>
    </InvalidOperand>
    <MissingPropertyType>
      <code><![CDATA[$strict]]></code>
    </MissingPropertyType>
    <MixedAssignment>
      <code><![CDATA[$v]]></code>
    </MixedAssignment>
    <PossiblyUnusedMethod>
      <code><![CDATA[loose]]></code>
      <code><![CDATA[strict]]></code>
    </PossiblyUnusedMethod>
    <UnsafeInstantiation>
      <code><![CDATA[new static($expected, false)]]></code>
      <code><![CDATA[new static($expected, true)]]></code>
    </UnsafeInstantiation>
  </file>
  <file src="library/Mockery/Matcher/Type.php">
    <DeprecatedClass>
      <code><![CDATA[MatcherAbstract]]></code>
    </DeprecatedClass>
    <DocblockTypeContradiction>
      <code><![CDATA[$this->_expected === 'real']]></code>
      <code><![CDATA[$this->_expected === 'real']]></code>
    </DocblockTypeContradiction>
    <InvalidArgument>
      <code><![CDATA[$this->_expected]]></code>
      <code><![CDATA[$this->_expected]]></code>
    </InvalidArgument>
    <InvalidCast>
      <code><![CDATA[$this->_expected]]></code>
    </InvalidCast>
    <MixedInferredReturnType>
      <code><![CDATA[bool]]></code>
    </MixedInferredReturnType>
    <MixedReturnStatement>
      <code><![CDATA[$function($actual)]]></code>
    </MixedReturnStatement>
    <NoValue>
      <code><![CDATA[$this->_expected]]></code>
    </NoValue>
    <PossiblyInvalidCast>
      <code><![CDATA[$this->_expected]]></code>
    </PossiblyInvalidCast>
    <RedundantConditionGivenDocblockType>
      <code><![CDATA['is_' . strtolower($this->_expected)]]></code>
      <code><![CDATA[is_string($this->_expected)]]></code>
    </RedundantConditionGivenDocblockType>
    <TypeDoesNotContainType>
      <code><![CDATA[function_exists($function)]]></code>
    </TypeDoesNotContainType>
  </file>
  <file src="library/Mockery/MethodCall.php">
    <PossiblyUnusedMethod>
      <code><![CDATA[__construct]]></code>
      <code><![CDATA[getArgs]]></code>
      <code><![CDATA[getMethod]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/MockInterface.php">
    <PossiblyUnusedMethod>
      <code><![CDATA[expects]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/ReceivedMethodCalls.php">
    <MissingPropertyType>
      <code><![CDATA[$methodCalls]]></code>
    </MissingPropertyType>
    <MissingReturnType>
      <code><![CDATA[push]]></code>
      <code><![CDATA[verify]]></code>
    </MissingReturnType>
    <MixedArgument>
      <code><![CDATA[$methodCall->getArgs()]]></code>
      <code><![CDATA[$methodCall->getArgs()]]></code>
    </MixedArgument>
    <MixedArrayAssignment>
      <code><![CDATA[$this->methodCalls[]]]></code>
    </MixedArrayAssignment>
    <MixedAssignment>
      <code><![CDATA[$methodCall]]></code>
    </MixedAssignment>
    <MixedMethodCall>
      <code><![CDATA[getArgs]]></code>
      <code><![CDATA[getArgs]]></code>
      <code><![CDATA[getMethod]]></code>
    </MixedMethodCall>
    <PossiblyUnusedMethod>
      <code><![CDATA[push]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/Mockery/Reflector.php">
    <LessSpecificReturnType>
      <code><![CDATA[string]]></code>
    </LessSpecificReturnType>
    <MissingThrowsDocblock>
      <code><![CDATA[throw new InvalidArgumentException('Unknown ReflectionType: ' . get_debug_type($type));]]></code>
    </MissingThrowsDocblock>
    <MixedArgument>
      <code><![CDATA[$innterType]]></code>
      <code><![CDATA[$type->getTypes()]]></code>
      <code><![CDATA[$type->getTypes()]]></code>
      <code><![CDATA[$typeHint]]></code>
    </MixedArgument>
    <MixedAssignment>
      <code><![CDATA[$innterType]]></code>
      <code><![CDATA[$type]]></code>
      <code><![CDATA[$type]]></code>
      <code><![CDATA[$typeHint]]></code>
    </MixedAssignment>
    <MixedReturnTypeCoercion>
      <code><![CDATA[[
                [
                    'typeHint' => $typeHint,
                    'isPrimitive' => in_array($typeHint, self::BUILTIN_TYPES, true),
                ],
            ]]]></code>
      <code><![CDATA[list<array{typeHint:string,isPrimitive:bool}>]]></code>
    </MixedReturnTypeCoercion>
    <PossiblyNullArgument>
      <code><![CDATA[$declaringClass]]></code>
    </PossiblyNullArgument>
    <PossiblyUnusedMethod>
      <code><![CDATA[getSimplestReturnType]]></code>
    </PossiblyUnusedMethod>
    <RedundantCondition>
      <code><![CDATA[! $type instanceof ReflectionType && method_exists($method, 'getTentativeReturnType')]]></code>
      <code><![CDATA[! $type instanceof ReflectionType && method_exists($method, 'getTentativeReturnType')]]></code>
    </RedundantCondition>
    <UndefinedMethod>
      <code><![CDATA[getName]]></code>
    </UndefinedMethod>
  </file>
  <file src="library/Mockery/Undefined.php">
    <PossiblyUnusedMethod>
      <code><![CDATA[__call]]></code>
    </PossiblyUnusedMethod>
    <PossiblyUnusedParam>
      <code><![CDATA[$args]]></code>
      <code><![CDATA[$method]]></code>
    </PossiblyUnusedParam>
  </file>
  <file src="library/Mockery/VerificationDirector.php">
    <MissingReturnType>
      <code><![CDATA[verify]]></code>
    </MissingReturnType>
    <PossiblyUnusedMethod>
      <code><![CDATA[atLeast]]></code>
      <code><![CDATA[atMost]]></code>
      <code><![CDATA[between]]></code>
      <code><![CDATA[once]]></code>
      <code><![CDATA[times]]></code>
      <code><![CDATA[twice]]></code>
      <code><![CDATA[with]]></code>
      <code><![CDATA[withAnyArgs]]></code>
      <code><![CDATA[withArgs]]></code>
      <code><![CDATA[withNoArgs]]></code>
    </PossiblyUnusedMethod>
  </file>
  <file src="library/helpers.php">
    <LessSpecificReturnType>
      <code><![CDATA[LegacyMockInterface&MockInterface&TMock]]></code>
      <code><![CDATA[LegacyMockInterface&MockInterface&TSpy]]></code>
    </LessSpecificReturnType>
  </file>
</files>
