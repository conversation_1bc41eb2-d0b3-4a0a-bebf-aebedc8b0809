<?php

declare(strict_types=1);

namespace Tests\Unit\PHP80;

use <PERSON><PERSON><PERSON>;
use <PERSON><PERSON>y\Adapter\Phpunit\MockeryTestCase;
use <PERSON><PERSON>y\LegacyMockInterface;
use Mo<PERSON>y\MockInterface;

/**
 * @coversDefaultClass \Mockery
 */
final class MockWithClosureAsLastArgumentTest extends MockeryTestCase
{
    public function testIfClosureIsPassedAsLastArgumentToMockItIsCalledWithMockObject(): void
    {
        $mock = Mockery::mock(
            TestInterface::class,
            static function (LegacyMockInterface|MockInterface $mock): void {
                $mock->expects('blm')->andReturn('#BlackLivesMatter');
            }
        );

        self::assertInstanceOf(TestInterface::class, $mock);

        self::assertSame('#BlackLivesMatter', $mock->blm());
    }
}

interface TestInterface
{
}
