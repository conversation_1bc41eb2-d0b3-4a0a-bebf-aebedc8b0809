<?php
require_once '../config/config.php';

// Require admin authentication
requireAdmin();

$page_title = 'Admin Dashboard';

// Define page actions
$page_actions = [
    [
        'url' => 'users.php',
        'label' => 'Manage Users',
        'icon' => 'fas fa-users'
    ]
];

// Get comprehensive system statistics
try {
    $db = getDB();

    // User Statistics (excluding deleted accounts)
    $total_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND deleted_at IS NULL");
    $total_users = $total_users_result->fetch_assoc()['count'];

    $active_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND status = 'active' AND deleted_at IS NULL");
    $active_users = $active_users_result->fetch_assoc()['count'];

    $suspended_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND status = 'suspended' AND deleted_at IS NULL");
    $suspended_users = $suspended_users_result->fetch_assoc()['count'];

    $pending_users_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND status = 'pending' AND deleted_at IS NULL");
    $pending_users = $pending_users_result->fetch_assoc()['count'];

    // Transaction Statistics (using account_transactions table)
    $today_transactions_result = $db->query("SELECT COUNT(*) as count, SUM(amount) as volume FROM account_transactions WHERE DATE(created_at) = CURDATE()");
    $today_stats = $today_transactions_result->fetch_assoc();
    $today_transactions = $today_stats['count'] ?? 0;
    $today_volume = $today_stats['volume'] ?? 0;

    $week_transactions_result = $db->query("SELECT COUNT(*) as count, SUM(amount) as volume FROM account_transactions WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $week_stats = $week_transactions_result->fetch_assoc();
    $week_transactions = $week_stats['count'] ?? 0;
    $week_volume = $week_stats['volume'] ?? 0;

    $pending_transfers_result = $db->query("SELECT COUNT(*) as count FROM account_transactions WHERE status = 'pending'");
    $pending_transfers = $pending_transfers_result->fetch_assoc()['count'];

    $failed_transfers_result = $db->query("SELECT COUNT(*) as count FROM account_transactions WHERE status = 'failed' AND DATE(created_at) = CURDATE()");
    $failed_transfers = $failed_transfers_result->fetch_assoc()['count'];

    // KYC and Verification Statistics (excluding deleted accounts)
    $pending_kyc_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE kyc_status = 'pending' AND is_admin = 0 AND deleted_at IS NULL");
    $pending_kyc = $pending_kyc_result->fetch_assoc()['count'];

    $approved_kyc_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE kyc_status = 'verified' AND is_admin = 0 AND deleted_at IS NULL");
    $approved_kyc = $approved_kyc_result->fetch_assoc()['count'];

    $rejected_kyc_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE kyc_status = 'rejected' AND is_admin = 0 AND deleted_at IS NULL");
    $rejected_kyc = $rejected_kyc_result->fetch_assoc()['count'];

    // System Health Indicators
    $total_balance_result = $db->query("SELECT SUM(balance) as total FROM accounts WHERE is_admin = 0 AND deleted_at IS NULL");
    $total_system_balance = $total_balance_result->fetch_assoc()['total'] ?? 0;

    $new_registrations_today_result = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND deleted_at IS NULL AND DATE(created_at) = CURDATE()");
    $new_registrations_today = $new_registrations_today_result->fetch_assoc()['count'];

    $login_attempts_today_result = $db->query("SELECT COUNT(*) as count FROM login_attempts WHERE DATE(attempted_at) = CURDATE()");
    $login_attempts_today = $login_attempts_today_result->fetch_assoc()['count'];

    // Recent Activities
    $recent_users_result = $db->query("
        SELECT id, username, first_name, last_name, email, created_at, status, kyc_status
        FROM accounts
        WHERE is_admin = 0 AND deleted_at IS NULL AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY created_at DESC
        LIMIT 5
    ");

    $recent_transactions_result = $db->query("
        SELECT at.*,
               a.username, a.first_name, a.last_name, a.account_number,
               ra.username as recipient_username, ra.first_name as recipient_first_name, ra.last_name as recipient_last_name
        FROM account_transactions at
        LEFT JOIN accounts a ON at.account_id = a.id
        LEFT JOIN accounts ra ON at.recipient_account_id = ra.id
        ORDER BY at.created_at DESC
        LIMIT 8
    ");

    // Alerts and Pending Actions
    $high_value_transfers_result = $db->query("SELECT COUNT(*) as count FROM account_transactions WHERE amount > 10000 AND status = 'pending'");
    $high_value_transfers = $high_value_transfers_result->fetch_assoc()['count'];

    // Check for suspicious activities (failed login attempts)
    $suspicious_activities_result = $db->query("SELECT COUNT(*) as count FROM login_attempts WHERE success = 0 AND DATE(attempted_at) = CURDATE()");
    $suspicious_activities = $suspicious_activities_result->fetch_assoc()['count'];

} catch (Exception $e) {
    error_log("Admin dashboard error: " . $e->getMessage());
    // Initialize all variables to prevent undefined variable errors
    $total_users = $active_users = $suspended_users = $pending_users = 0;
    $today_transactions = $today_volume = $week_transactions = $week_volume = 0;
    $pending_transfers = $failed_transfers = $pending_kyc = $approved_kyc = $rejected_kyc = 0;
    $total_system_balance = $new_registrations_today = $login_attempts_today = 0;
    $high_value_transfers = $suspicious_activities = 0;
    $recent_users_result = $recent_transactions_result = null;
}

include 'includes/admin-header.php';
?>

<!-- System Health Alerts -->
<?php if ($pending_kyc > 0 || $pending_transfers > 0 || $high_value_transfers > 0 || $suspicious_activities > 0): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white;">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Pending Actions Required
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if ($pending_kyc > 0): ?>
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="alert alert-warning mb-2 d-flex align-items-center justify-content-between">
                            <div class="flex-grow-1">
                                <strong><?php echo $pending_kyc; ?></strong> KYC pending
                            </div>
                            <a href="users.php?filter=kyc_pending" class="btn btn-sm btn-outline-warning">Review</a>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($pending_transfers > 0): ?>
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="alert alert-info mb-2 d-flex align-items-center justify-content-between">
                            <div class="flex-grow-1">
                                <strong><?php echo $pending_transfers; ?></strong> transfers pending
                            </div>
                            <a href="transfers.php?status=pending" class="btn btn-sm btn-outline-info">Review</a>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($high_value_transfers > 0): ?>
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="alert alert-danger mb-2 d-flex align-items-center justify-content-between">
                            <div class="flex-grow-1">
                                <strong><?php echo $high_value_transfers; ?></strong> high-value
                            </div>
                            <a href="transfers.php?filter=high_value" class="btn btn-sm btn-outline-danger">Review</a>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($suspicious_activities > 0): ?>
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="alert alert-dark mb-2 d-flex align-items-center justify-content-between">
                            <div class="flex-grow-1">
                                <strong><?php echo $suspicious_activities; ?></strong> suspicious
                            </div>
                            <a href="security-logs.php" class="btn btn-sm btn-outline-dark">Review</a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- System Overview Statistics -->
<div class="row mb-4">
    <!-- User Statistics -->
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%); color: white;">
                <h6 class="card-title mb-0">
                    <i class="fas fa-users"></i> User Management
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 mb-1 text-primary"><?php echo number_format($total_users); ?></div>
                        <div class="small text-muted">Total Users</div>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-1 text-success"><?php echo number_format($active_users); ?></div>
                        <div class="small text-muted">Active</div>
                    </div>
                </div>
                <hr class="my-2">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h6 mb-1 text-warning"><?php echo number_format($suspended_users); ?></div>
                        <div class="small text-muted">Suspended</div>
                    </div>
                    <div class="col-6">
                        <div class="h6 mb-1 text-info"><?php echo number_format($pending_users); ?></div>
                        <div class="small text-muted">Pending</div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="users.php" class="btn btn-outline-primary btn-sm w-100">Manage Users</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction Statistics -->
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <h6 class="card-title mb-0">
                    <i class="fas fa-exchange-alt"></i> Transactions
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 mb-1 text-primary"><?php echo number_format($today_transactions); ?></div>
                        <div class="small text-muted">Today</div>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-1 text-success"><?php echo number_format($week_transactions); ?></div>
                        <div class="small text-muted">This Week</div>
                    </div>
                </div>
                <hr class="my-2">
                <div class="text-center">
                    <div class="h6 mb-1"><?php echo formatCurrency($week_volume); ?></div>
                    <div class="small text-muted">Week's Volume</div>
                </div>
                <div class="mt-3">
                    <a href="transactions.php" class="btn btn-outline-success btn-sm w-100">View Transactions</a>
                </div>
            </div>
        </div>
    </div>

    <!-- KYC Verification Status -->
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white;">
                <h6 class="card-title mb-0">
                    <i class="fas fa-id-card"></i> KYC Status
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 mb-1 text-warning"><?php echo number_format($pending_kyc); ?></div>
                        <div class="small text-muted">Pending</div>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-1 text-success"><?php echo number_format($approved_kyc); ?></div>
                        <div class="small text-muted">Approved</div>
                    </div>
                </div>
                <hr class="my-2">
                <div class="text-center">
                    <div class="h6 mb-1 text-danger"><?php echo number_format($rejected_kyc); ?></div>
                    <div class="small text-muted">Rejected</div>
                </div>
                <div class="mt-3">
                    <a href="users.php?filter=kyc_pending" class="btn btn-outline-warning btn-sm w-100">Review KYC</a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white;">
                <h6 class="card-title mb-0">
                    <i class="fas fa-heartbeat"></i> System Health
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-2">
                    <div class="h4 mb-1 text-primary"><?php echo formatCurrency($total_system_balance); ?></div>
                    <div class="small text-muted">Total Balance</div>
                </div>
                <hr class="my-2">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h6 mb-1 text-info"><?php echo number_format($new_registrations_today); ?></div>
                        <div class="small text-muted">New Today</div>
                    </div>
                    <div class="col-6">
                        <div class="h6 mb-1 text-secondary"><?php echo number_format($login_attempts_today); ?></div>
                        <div class="small text-muted">Logins Today</div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="reports.php" class="btn btn-outline-secondary btn-sm w-100">View Reports</a>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Quick Actions & Recent Activities -->
<div class="row mb-4">
    <!-- Quick Actions -->
    <div class="col-lg-4 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white;">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="add-user.php" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus me-2"></i>Add New User
                    </a>
                    <a href="credit-debit.php" class="btn btn-outline-success">
                        <i class="fas fa-coins me-2"></i>Credit/Debit Account
                    </a>
                    <a href="transfers.php?status=pending" class="btn btn-outline-warning">
                        <i class="fas fa-clock me-2"></i>Review Pending Transfers
                    </a>
                    <a href="users.php?filter=kyc_pending" class="btn btn-outline-info">
                        <i class="fas fa-id-card me-2"></i>Review KYC Applications
                    </a>
                    <a href="user-status-management.php" class="btn btn-outline-secondary">
                        <i class="fas fa-user-cog me-2"></i>Manage User Status
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent User Registrations -->
    <div class="col-lg-8 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus"></i> Recent User Registrations
                </h5>
                <div class="card-actions">
                    <a href="users.php" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-eye me-1"></i>View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if ($recent_users_result && $recent_users_result->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table mb-0">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>KYC</th>
                                <th>Registered</th>
                                <th class="w-1"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($user = $recent_users_result->fetch_assoc()): ?>
                            <tr>
                                <td>
                                    <div class="d-flex py-1 align-items-center">
                                        <span class="avatar avatar-sm me-2" style="background: #4361ee; color: white;">
                                            <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                        </span>
                                        <div class="flex-fill">
                                            <div class="font-weight-medium"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                            <div class="text-muted small">@<?php echo htmlspecialchars($user['username']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-muted">
                                    <?php echo htmlspecialchars($user['email']); ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : ($user['status'] === 'suspended' ? 'danger' : 'warning'); ?>">
                                        <?php echo ucfirst($user['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $user['kyc_status'] === 'approved' ? 'success' : ($user['kyc_status'] === 'rejected' ? 'danger' : 'warning'); ?>">
                                        <?php echo ucfirst($user['kyc_status'] ?? 'pending'); ?>
                                    </span>
                                </td>
                                <td class="text-muted">
                                    <?php echo formatDate($user['created_at'], 'M d, Y'); ?>
                                </td>
                                <td>
                                    <div class="btn-list flex-nowrap">
                                        <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty py-4">
                    <div class="empty-img">
                        <i class="fas fa-users" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No recent registrations</p>
                    <p class="empty-subtitle text-muted">
                        New user registrations will appear here.
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #6f42c1 0%, #6610f2 100%); color: white;">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exchange-alt"></i> Recent Transactions
                </h5>
                <div class="card-actions">
                    <a href="transactions.php" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-eye me-1"></i>View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if ($recent_transactions_result && $recent_transactions_result->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table mb-0">
                        <thead>
                            <tr>
                                <th>Reference</th>
                                <th>Account</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th class="w-1"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($transaction = $recent_transactions_result->fetch_assoc()): ?>
                            <tr>
                                <td>
                                    <span class="font-monospace text-muted">#<?php echo htmlspecialchars($transaction['reference_number']); ?></span>
                                </td>
                                <td>
                                    <?php if ($transaction['username']): ?>
                                        <div class="d-flex align-items-center">
                                            <span class="avatar avatar-xs me-2" style="background: #4361ee; color: white;">
                                                <?php echo strtoupper(substr($transaction['first_name'], 0, 1) . substr($transaction['last_name'], 0, 1)); ?>
                                            </span>
                                            <span><?php echo htmlspecialchars($transaction['username']); ?></span>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">System</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($transaction['recipient_username']): ?>
                                        <div class="d-flex align-items-center">
                                            <span class="avatar avatar-xs me-2" style="background: #28a745; color: white;">
                                                <?php echo strtoupper(substr($transaction['recipient_first_name'], 0, 1) . substr($transaction['recipient_last_name'], 0, 1)); ?>
                                            </span>
                                            <span><?php echo htmlspecialchars($transaction['recipient_username']); ?></span>
                                        </div>
                                    <?php elseif ($transaction['transaction_type'] === 'transfer_out'): ?>
                                        <span class="text-muted">External Transfer</span>
                                    <?php else: ?>
                                        <span class="text-muted">
                                            <?php
                                            echo $transaction['transaction_type'] === 'credit' ? 'Account Credit' :
                                                ($transaction['transaction_type'] === 'debit' ? 'Account Debit' :
                                                ($transaction['transaction_type'] === 'deposit' ? 'Deposit' :
                                                ($transaction['transaction_type'] === 'withdrawal' ? 'Withdrawal' : 'Internal')));
                                            ?>
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="font-weight-bold text-<?php echo in_array($transaction['transaction_type'], ['credit', 'deposit', 'transfer_in']) ? 'success' : 'danger'; ?>">
                                        <?php echo in_array($transaction['transaction_type'], ['credit', 'deposit', 'transfer_in']) ? '+' : '-'; ?>
                                        <?php echo formatCurrency($transaction['amount']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php
                                        echo $transaction['status'] === 'completed' ? 'success' :
                                            ($transaction['status'] === 'failed' ? 'danger' :
                                            ($transaction['status'] === 'pending' ? 'warning' :
                                            ($transaction['status'] === 'processing' ? 'info' : 'secondary')));
                                    ?>">
                                        <?php echo ucfirst($transaction['status']); ?>
                                    </span>
                                </td>
                                <td class="text-muted">
                                    <?php echo formatDate($transaction['created_at'], 'M d, Y H:i'); ?>
                                </td>
                                <td>
                                    <a href="transaction-details.php?id=<?php echo $transaction['id']; ?>&type=account" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty py-4">
                    <div class="empty-img">
                        <i class="fas fa-exchange-alt" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No recent transactions</p>
                    <p class="empty-subtitle text-muted">
                        Recent transactions will appear here.
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- System Status Footer -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
            <div class="card-body text-center py-3">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-muted small">System Status</div>
                        <div class="text-success font-weight-bold">
                            <i class="fas fa-circle me-1"></i>Online
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-muted small">Last Updated</div>
                        <div class="font-weight-bold"><?php echo date('M d, Y H:i'); ?></div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-muted small">Failed Transfers Today</div>
                        <div class="text-<?php echo $failed_transfers > 0 ? 'danger' : 'success'; ?> font-weight-bold">
                            <?php echo number_format($failed_transfers); ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-muted small">Admin Session</div>
                        <div class="text-info font-weight-bold">
                            <?php echo htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
