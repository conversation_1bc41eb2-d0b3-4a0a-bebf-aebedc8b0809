<?php
/**
 * Session Performance and Stress Test Suite
 * Tests session performance under load and various stress conditions
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/SessionManager.php';

class SessionPerformanceTest {
    
    private $testResults = [];
    private $performanceMetrics = [];
    
    public function __construct() {
        echo "<h1>Session Performance and Stress Test Suite</h1>\n";
        echo "<style>
            .test-pass { color: green; font-weight: bold; }
            .test-fail { color: red; font-weight: bold; }
            .test-info { color: blue; }
            .test-section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
            .test-result { margin: 5px 0; padding: 5px; }
            .performance-metric { background: #f0f8ff; padding: 5px; margin: 2px 0; }
            .benchmark { background: #fff8dc; padding: 10px; margin: 10px 0; }
        </style>\n";
    }
    
    private function logTest($description, $passed, $details = '') {
        $status = $passed ? 'PASS' : 'FAIL';
        $class = $passed ? 'test-pass' : 'test-fail';
        
        echo "<div class='test-result'>";
        echo "<span class='$class'>[$status]</span> $description";
        if ($details) {
            echo " <span class='test-info'>($details)</span>";
        }
        echo "</div>\n";
        
        $this->testResults[] = [
            'description' => $description,
            'passed' => $passed,
            'details' => $details
        ];
    }
    
    private function logPerformance($operation, $duration, $iterations = 1) {
        $avgTime = $duration / $iterations;
        echo "<div class='performance-metric'>";
        echo "<strong>$operation:</strong> ";
        echo "Total: " . number_format($duration * 1000, 2) . "ms, ";
        echo "Average: " . number_format($avgTime * 1000, 2) . "ms";
        if ($iterations > 1) {
            echo " ($iterations iterations)";
        }
        echo "</div>\n";
        
        $this->performanceMetrics[] = [
            'operation' => $operation,
            'total_time' => $duration,
            'average_time' => $avgTime,
            'iterations' => $iterations
        ];
    }
    
    /**
     * Test session initialization performance
     */
    public function testSessionInitializationPerformance() {
        echo "<div class='test-section'><h2>Session Initialization Performance</h2>";
        
        $iterations = 100;
        $totalTime = 0;
        
        for ($i = 0; $i < $iterations; $i++) {
            session_destroy();
            
            $startTime = microtime(true);
            SessionManager::getInstance();
            $endTime = microtime(true);
            
            $totalTime += ($endTime - $startTime);
        }
        
        $this->logPerformance("Session Initialization", $totalTime, $iterations);
        
        $avgTime = $totalTime / $iterations;
        $this->logTest("Session initialization under 10ms average", 
            $avgTime < 0.01,
            number_format($avgTime * 1000, 2) . "ms average");
        
        echo "</div>";
    }
    
    /**
     * Test session data operations performance
     */
    public function testSessionDataPerformance() {
        echo "<div class='test-section'><h2>Session Data Operations Performance</h2>";
        
        SessionManager::getInstance();
        
        // Test setting data performance
        $iterations = 1000;
        $startTime = microtime(true);
        
        for ($i = 0; $i < $iterations; $i++) {
            SessionManager::set("test_key_$i", "test_value_$i");
        }
        
        $endTime = microtime(true);
        $setTime = $endTime - $startTime;
        $this->logPerformance("Setting $iterations session variables", $setTime, $iterations);
        
        // Test getting data performance
        $startTime = microtime(true);
        
        for ($i = 0; $i < $iterations; $i++) {
            SessionManager::get("test_key_$i");
        }
        
        $endTime = microtime(true);
        $getTime = $endTime - $startTime;
        $this->logPerformance("Getting $iterations session variables", $getTime, $iterations);
        
        // Test performance benchmarks
        $this->logTest("Set operations under 1ms average", 
            ($setTime / $iterations) < 0.001,
            number_format(($setTime / $iterations) * 1000, 3) . "ms average");
        
        $this->logTest("Get operations under 0.5ms average", 
            ($getTime / $iterations) < 0.0005,
            number_format(($getTime / $iterations) * 1000, 3) . "ms average");
        
        echo "</div>";
    }
    
    /**
     * Test session validation performance
     */
    public function testSessionValidationPerformance() {
        echo "<div class='test-section'><h2>Session Validation Performance</h2>";
        
        SessionManager::login(1, 'user');
        
        $iterations = 500;
        
        // Test isLoggedIn performance
        $startTime = microtime(true);
        for ($i = 0; $i < $iterations; $i++) {
            SessionManager::isLoggedIn();
        }
        $endTime = microtime(true);
        $this->logPerformance("isLoggedIn() calls", $endTime - $startTime, $iterations);
        
        // Test isAdmin performance
        $startTime = microtime(true);
        for ($i = 0; $i < $iterations; $i++) {
            SessionManager::isAdmin();
        }
        $endTime = microtime(true);
        $this->logPerformance("isAdmin() calls", $endTime - $startTime, $iterations);
        
        // Test session info retrieval
        $startTime = microtime(true);
        for ($i = 0; $i < 100; $i++) {
            SessionManager::getSessionInfo();
        }
        $endTime = microtime(true);
        $this->logPerformance("getSessionInfo() calls", $endTime - $startTime, 100);
        
        echo "</div>";
    }
    
    /**
     * Test memory usage during session operations
     */
    public function testSessionMemoryUsage() {
        echo "<div class='test-section'><h2>Session Memory Usage Tests</h2>";
        
        $initialMemory = memory_get_usage();
        
        // Create session with large amount of data
        SessionManager::getInstance();
        
        $largeData = str_repeat('A', 10000); // 10KB string
        for ($i = 0; $i < 100; $i++) {
            SessionManager::set("large_data_$i", $largeData);
        }
        
        $afterDataMemory = memory_get_usage();
        $memoryIncrease = $afterDataMemory - $initialMemory;
        
        echo "<div class='performance-metric'>";
        echo "<strong>Memory Usage:</strong> ";
        echo "Initial: " . number_format($initialMemory / 1024, 2) . "KB, ";
        echo "After 1MB session data: " . number_format($afterDataMemory / 1024, 2) . "KB, ";
        echo "Increase: " . number_format($memoryIncrease / 1024, 2) . "KB";
        echo "</div>\n";
        
        $this->logTest("Memory increase reasonable for data size", 
            $memoryIncrease < 2000000, // Less than 2MB increase for 1MB data
            number_format($memoryIncrease / 1024, 2) . "KB increase");
        
        // Test memory cleanup on logout
        SessionManager::logout();
        $afterLogoutMemory = memory_get_usage();
        
        echo "<div class='performance-metric'>";
        echo "<strong>Memory after logout:</strong> " . number_format($afterLogoutMemory / 1024, 2) . "KB";
        echo "</div>\n";
        
        echo "</div>";
    }
    
    /**
     * Test concurrent session simulation
     */
    public function testConcurrentSessionSimulation() {
        echo "<div class='test-section'><h2>Concurrent Session Simulation</h2>";
        
        $sessionCount = 50;
        $operationsPerSession = 20;
        
        $startTime = microtime(true);
        
        for ($session = 0; $session < $sessionCount; $session++) {
            // Simulate new session
            session_destroy();
            SessionManager::getInstance();
            SessionManager::login($session + 1, 'user');
            
            // Perform operations
            for ($op = 0; $op < $operationsPerSession; $op++) {
                SessionManager::set("data_$op", "value_$op");
                SessionManager::get("data_$op");
                SessionManager::extendSession();
            }
            
            SessionManager::logout();
        }
        
        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        
        $this->logPerformance("$sessionCount concurrent sessions simulation", $totalTime);
        
        $avgSessionTime = $totalTime / $sessionCount;
        $this->logTest("Average session handling under 100ms", 
            $avgSessionTime < 0.1,
            number_format($avgSessionTime * 1000, 2) . "ms per session");
        
        echo "</div>";
    }
    
    /**
     * Test session stress conditions
     */
    public function testSessionStressConditions() {
        echo "<div class='test-section'><h2>Session Stress Tests</h2>";
        
        // Test rapid login/logout cycles
        $cycles = 100;
        $startTime = microtime(true);
        
        for ($i = 0; $i < $cycles; $i++) {
            SessionManager::getInstance();
            SessionManager::login($i + 1, 'user');
            SessionManager::logout();
        }
        
        $endTime = microtime(true);
        $this->logPerformance("$cycles rapid login/logout cycles", $endTime - $startTime, $cycles);
        
        // Test session with maximum data
        SessionManager::getInstance();
        SessionManager::login(1, 'user');
        
        $maxDataSize = 1000; // 1000 variables
        $startTime = microtime(true);
        
        for ($i = 0; $i < $maxDataSize; $i++) {
            SessionManager::set("stress_test_$i", "data_value_$i");
        }
        
        $endTime = microtime(true);
        $this->logPerformance("Setting $maxDataSize session variables", $endTime - $startTime);
        
        // Test session info retrieval with large session
        $startTime = microtime(true);
        $sessionInfo = SessionManager::getSessionInfo();
        $endTime = microtime(true);
        
        $this->logPerformance("Session info with large session", $endTime - $startTime);
        
        $this->logTest("Session info retrieval successful with large data", 
            is_array($sessionInfo) && isset($sessionInfo['user_id']));
        
        echo "</div>";
    }
    
    /**
     * Test session timeout performance
     */
    public function testSessionTimeoutPerformance() {
        echo "<div class='test-section'><h2>Session Timeout Performance</h2>";
        
        SessionManager::setTimeout(5); // 5 second timeout
        SessionManager::getInstance();
        SessionManager::login(1, 'user');
        
        // Test timeout checking performance
        $iterations = 1000;
        $startTime = microtime(true);
        
        for ($i = 0; $i < $iterations; $i++) {
            $sessionInfo = SessionManager::getSessionInfo();
            $timeRemaining = $sessionInfo['time_remaining'];
        }
        
        $endTime = microtime(true);
        $this->logPerformance("Timeout calculations", $endTime - $startTime, $iterations);
        
        // Test session extension performance
        $startTime = microtime(true);
        
        for ($i = 0; $i < $iterations; $i++) {
            SessionManager::extendSession();
        }
        
        $endTime = microtime(true);
        $this->logPerformance("Session extensions", $endTime - $startTime, $iterations);
        
        echo "</div>";
    }
    
    /**
     * Run all performance tests
     */
    public function runAllTests() {
        echo "<h2>Starting Session Performance Tests...</h2>";
        
        $overallStart = microtime(true);
        
        $this->testSessionInitializationPerformance();
        $this->testSessionDataPerformance();
        $this->testSessionValidationPerformance();
        $this->testSessionMemoryUsage();
        $this->testConcurrentSessionSimulation();
        $this->testSessionStressConditions();
        $this->testSessionTimeoutPerformance();
        
        $overallEnd = microtime(true);
        
        $this->displaySummary($overallEnd - $overallStart);
        $this->displayBenchmarks();
    }
    
    /**
     * Display test summary
     */
    private function displaySummary($totalTestTime) {
        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, function($test) {
            return $test['passed'];
        }));
        $failedTests = $totalTests - $passedTests;
        
        echo "<div class='test-section'>";
        echo "<h2>Performance Test Summary</h2>";
        echo "<p><strong>Total Tests:</strong> $totalTests</p>";
        echo "<p class='test-pass'><strong>Passed:</strong> $passedTests</p>";
        echo "<p class='test-fail'><strong>Failed:</strong> $failedTests</p>";
        echo "<p><strong>Total Test Time:</strong> " . number_format($totalTestTime, 2) . " seconds</p>";
        echo "<p><strong>Performance Score:</strong> " . round(($passedTests / $totalTests) * 100, 2) . "%</p>";
        echo "</div>";
    }
    
    /**
     * Display performance benchmarks
     */
    private function displayBenchmarks() {
        echo "<div class='benchmark'>";
        echo "<h3>Performance Benchmarks Summary</h3>";
        
        foreach ($this->performanceMetrics as $metric) {
            $avgMs = $metric['average_time'] * 1000;
            $status = $this->getBenchmarkStatus($metric['operation'], $avgMs);
            
            echo "<div>";
            echo "<strong>{$metric['operation']}:</strong> ";
            echo number_format($avgMs, 3) . "ms average ";
            echo "<span class='$status'>[" . strtoupper(str_replace('test-', '', $status)) . "]</span>";
            echo "</div>";
        }
        
        echo "</div>";
    }
    
    private function getBenchmarkStatus($operation, $avgMs) {
        $benchmarks = [
            'Session Initialization' => 10,
            'Setting' => 1,
            'Getting' => 0.5,
            'isLoggedIn()' => 0.1,
            'isAdmin()' => 0.1,
            'getSessionInfo()' => 1,
            'Session extensions' => 0.5,
            'Timeout calculations' => 0.5
        ];
        
        foreach ($benchmarks as $op => $threshold) {
            if (strpos($operation, $op) !== false) {
                return $avgMs <= $threshold ? 'test-pass' : 'test-fail';
            }
        }
        
        return 'test-info';
    }
}

// Only run if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'session_performance_test.php') {
    $performanceTest = new SessionPerformanceTest();
    $performanceTest->runAllTests();
}
?>
