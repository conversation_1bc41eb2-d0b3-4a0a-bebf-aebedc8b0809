<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Transaction History';
$site_name = getBankName();

// Pagination settings
$records_per_page = 20;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_type = $_GET['type'] ?? '';
$filter_status = $_GET['status'] ?? '';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$filter_amount_min = floatval($_GET['amount_min'] ?? 0);
$filter_amount_max = floatval($_GET['amount_max'] ?? 0);
$search_query = $_GET['search'] ?? '';

try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Build WHERE clause for filters
    $where_conditions = ["(t.sender_id = ? OR t.recipient_id = ?)"];
    $params = [$user_id, $user_id];
    
    if (!empty($filter_type)) {
        if ($filter_type === 'sent') {
            $where_conditions[] = "t.sender_id = ?";
            $params[] = $user_id;
        } elseif ($filter_type === 'received') {
            $where_conditions[] = "t.recipient_id = ?";
            $params[] = $user_id;
        }
    }
    
    if (!empty($filter_status)) {
        $where_conditions[] = "t.status = ?";
        $params[] = $filter_status;
    }
    
    if (!empty($filter_date_from)) {
        $where_conditions[] = "DATE(t.created_at) >= ?";
        $params[] = $filter_date_from;
    }
    
    if (!empty($filter_date_to)) {
        $where_conditions[] = "DATE(t.created_at) <= ?";
        $params[] = $filter_date_to;
    }
    
    if ($filter_amount_min > 0) {
        $where_conditions[] = "t.amount >= ?";
        $params[] = $filter_amount_min;
    }
    
    if ($filter_amount_max > 0) {
        $where_conditions[] = "t.amount <= ?";
        $params[] = $filter_amount_max;
    }
    
    if (!empty($search_query)) {
        $where_conditions[] = "(t.recipient_name LIKE ? OR t.description LIKE ? OR t.transaction_id LIKE ?)";
        $search_param = '%' . $search_query . '%';
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get total count for pagination
    $count_sql = "SELECT COUNT(*) as total FROM transfers t WHERE $where_clause";
    $count_result = $db->query($count_sql, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get transactions with pagination
    $transactions_sql = "SELECT t.*,
                               CASE
                                   WHEN t.sender_id = ? THEN 'sent'
                                   ELSE 'received'
                               END as direction,
                               CASE
                                   WHEN t.sender_id = ? THEN t.recipient_name
                                   ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                               END as other_party
                        FROM transfers t
                        WHERE $where_clause
                        ORDER BY t.created_at DESC
                        LIMIT ? OFFSET ?";
    
    $transaction_params = array_merge([$user_id, $user_id], $params, [$records_per_page, $offset]);
    $transactions_result = $db->query($transactions_sql, $transaction_params);
    
    // Get transaction statistics
    $stats_sql = "SELECT 
                    COUNT(*) as total_transactions,
                    SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                    SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count
                  FROM transfers 
                  WHERE (sender_id = ? OR recipient_id = ?)";
    
    $stats_result = $db->query($stats_sql, [$user_id, $user_id, $user_id, $user_id]);
    $stats = $stats_result->fetch_assoc();

} catch (Exception $e) {
    error_log("Transaction history error: " . $e->getMessage());
    $transactions_result = null;
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total_transactions' => 0, 'total_sent' => 0, 'total_received' => 0, 'completed_count' => 0, 'pending_count' => 0];
}

include '../includes/dashboard/header.php';
include '../includes/dashboard/sidebar.php';
?>

<div class="main-content">
    <div class="banking-container">
        <div class="banking-dashboard">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1>Transaction History</h1>
                    <p>View and manage all your banking transactions</p>
                </div>
                <div class="page-actions">
                    <a href="export.php<?php echo !empty($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] : ''; ?>" class="btn-outline">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                        Export CSV
                    </a>
                    <a href="../transfers/" class="btn-primary">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                        </svg>
                        New Transfer
                    </a>
                </div>
            </div>

            <!-- Transaction Statistics -->
            <div class="stats-overview">
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_transactions']); ?></h3>
                        <p>Total Transactions</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon sent">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo formatCurrency($stats['total_sent'], $_SESSION['currency'] ?? 'USD'); ?></h3>
                        <p>Total Sent</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon received">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo formatCurrency($stats['total_received'], $_SESSION['currency'] ?? 'USD'); ?></h3>
                        <p>Total Received</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['completed_count']); ?></h3>
                        <p>Completed</p>
                    </div>
                </div>
            </div>

            <!-- Advanced Filters -->
            <div class="filters-section">
                <div class="filters-header">
                    <h3>Filter Transactions</h3>
                    <button type="button" class="toggle-filters" onclick="toggleFilters()">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd"/>
                        </svg>
                        Filters
                    </button>
                </div>

                <div class="filters-content" id="filtersContent">
                    <form method="GET" class="filters-form">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="search">Search</label>
                                <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search_query); ?>" placeholder="Search by name, description, or ID">
                            </div>
                            <div class="filter-group">
                                <label for="type">Transaction Type</label>
                                <select id="type" name="type">
                                    <option value="">All Types</option>
                                    <option value="sent" <?php echo $filter_type === 'sent' ? 'selected' : ''; ?>>Sent</option>
                                    <option value="received" <?php echo $filter_type === 'received' ? 'selected' : ''; ?>>Received</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="status">Status</label>
                                <select id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="completed" <?php echo $filter_status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                    <option value="cancelled" <?php echo $filter_status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                        </div>

                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="date_from">From Date</label>
                                <input type="date" id="date_from" name="date_from" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                            </div>
                            <div class="filter-group">
                                <label for="date_to">To Date</label>
                                <input type="date" id="date_to" name="date_to" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                            </div>
                            <div class="filter-group">
                                <label for="amount_min">Min Amount</label>
                                <input type="number" id="amount_min" name="amount_min" value="<?php echo $filter_amount_min > 0 ? $filter_amount_min : ''; ?>" placeholder="0.00" step="0.01">
                            </div>
                            <div class="filter-group">
                                <label for="amount_max">Max Amount</label>
                                <input type="number" id="amount_max" name="amount_max" value="<?php echo $filter_amount_max > 0 ? $filter_amount_max : ''; ?>" placeholder="0.00" step="0.01">
                            </div>
                        </div>

                        <div class="filter-actions">
                            <button type="submit" class="btn-primary">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                                </svg>
                                Apply Filters
                            </button>
                            <a href="index.php" class="btn-outline">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                                Clear All
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Transaction List -->
            <div class="transactions-container">
                <?php if ($transactions_result && $transactions_result->num_rows > 0): ?>
                    <div class="transactions-header">
                        <div class="results-info">
                            <span>Showing <?php echo $offset + 1; ?>-<?php echo min($offset + $records_per_page, $total_records); ?> of <?php echo number_format($total_records); ?> transactions</span>
                        </div>
                        <div class="view-options">
                            <button type="button" class="view-toggle active" data-view="list">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                </svg>
                                List
                            </button>
                            <button type="button" class="view-toggle" data-view="grid">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" clip-rule="evenodd"/>
                                </svg>
                                Grid
                            </button>
                        </div>
                    </div>

                    <div class="transactions-list" id="transactionsList">
                        <?php while ($transaction = $transactions_result->fetch_assoc()): ?>
                            <div class="transaction-row" onclick="showTransactionDetails('<?php echo $transaction['transaction_id']; ?>')">
                                <div class="transaction-info">
                                    <div class="transaction-avatar">
                                        <div class="avatar-icon <?php echo $transaction['direction']; ?>">
                                            <?php if ($transaction['direction'] === 'sent'): ?>
                                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                                </svg>
                                            <?php else: ?>
                                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"/>
                                                </svg>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="transaction-details">
                                        <div class="transaction-primary">
                                            <h4><?php echo htmlspecialchars($transaction['other_party'] ?? 'Unknown'); ?></h4>
                                            <span class="transaction-type-badge <?php echo $transaction['direction']; ?>">
                                                <?php echo ucfirst($transaction['direction']); ?>
                                            </span>
                                        </div>
                                        <div class="transaction-secondary">
                                            <span class="transaction-description"><?php echo htmlspecialchars($transaction['description'] ?? 'No description'); ?></span>
                                            <span class="transaction-id">ID: <?php echo htmlspecialchars($transaction['transaction_id']); ?></span>
                                        </div>
                                        <div class="transaction-meta">
                                            <span class="transaction-date"><?php echo date('M j, Y g:i A', strtotime($transaction['created_at'])); ?></span>
                                            <?php if ($transaction['transfer_type'] !== 'local'): ?>
                                                <span class="transfer-type"><?php echo ucfirst($transaction['transfer_type']); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="transaction-amount-info">
                                    <div class="amount <?php echo $transaction['direction'] === 'sent' ? 'negative' : 'positive'; ?>">
                                        <?php echo $transaction['direction'] === 'sent' ? '-' : '+'; ?><?php echo formatCurrency($transaction['amount'], $transaction['currency'] ?? 'USD'); ?>
                                    </div>
                                    <?php if ($transaction['fee'] > 0): ?>
                                        <div class="transaction-fee">Fee: <?php echo formatCurrency($transaction['fee'], $transaction['currency'] ?? 'USD'); ?></div>
                                    <?php endif; ?>
                                    <div class="transaction-status">
                                        <span class="status-badge status-<?php echo $transaction['status']; ?>">
                                            <?php echo ucfirst($transaction['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="transaction-actions">
                                    <button type="button" class="action-btn" onclick="event.stopPropagation(); showTransactionDetails('<?php echo $transaction['transaction_id']; ?>')">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                        </svg>
                                    </button>
                                    <?php if ($transaction['direction'] === 'sent' && $transaction['status'] === 'completed'): ?>
                                        <button type="button" class="action-btn" onclick="event.stopPropagation(); repeatTransaction('<?php echo $transaction['transaction_id']; ?>')">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                                            </svg>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="pagination-container">
                            <div class="pagination">
                                <?php if ($current_page > 1): ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page - 1])); ?>" class="pagination-btn">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        Previous
                                    </a>
                                <?php endif; ?>

                                <div class="pagination-info">
                                    Page <?php echo $current_page; ?> of <?php echo $total_pages; ?>
                                </div>

                                <?php if ($current_page < $total_pages): ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $current_page + 1])); ?>" class="pagination-btn">
                                        Next
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                <?php else: ?>
                    <div class="empty-transactions">
                        <div class="empty-icon">
                            <svg width="64" height="64" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <h4>No transactions found</h4>
                        <p>No transactions match your current filters. Try adjusting your search criteria or clear all filters.</p>
                        <div class="empty-actions">
                            <a href="index.php" class="btn-outline">Clear Filters</a>
                            <a href="../transfers/" class="btn-primary">Make a Transfer</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Details Modal -->
<div id="transactionModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Transaction Details</h3>
            <button type="button" class="modal-close" onclick="closeTransactionModal()">
                <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
            </button>
        </div>
        <div class="modal-body" id="transactionDetails">
            <!-- Transaction details will be loaded here -->
        </div>
    </div>
</div>

<script>
function toggleFilters() {
    const content = document.getElementById('filtersContent');
    const button = document.querySelector('.toggle-filters');

    if (content.style.display === 'none' || content.style.display === '') {
        content.style.display = 'block';
        button.classList.add('active');
    } else {
        content.style.display = 'none';
        button.classList.remove('active');
    }
}

function showTransactionDetails(transactionId) {
    // Show modal
    document.getElementById('transactionModal').style.display = 'flex';

    // Load transaction details via AJAX
    fetch(`details.php?id=${transactionId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('transactionDetails').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('transactionDetails').innerHTML = '<p>Error loading transaction details.</p>';
        });
}

function closeTransactionModal() {
    document.getElementById('transactionModal').style.display = 'none';
}

function repeatTransaction(transactionId) {
    if (confirm('Do you want to repeat this transaction?')) {
        window.location.href = `../transfers/?repeat=${transactionId}`;
    }
}

// View toggle functionality
document.querySelectorAll('.view-toggle').forEach(button => {
    button.addEventListener('click', function() {
        document.querySelectorAll('.view-toggle').forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');

        const view = this.dataset.view;
        const list = document.getElementById('transactionsList');

        if (view === 'grid') {
            list.classList.add('grid-view');
        } else {
            list.classList.remove('grid-view');
        }
    });
});

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('transactionModal');
    if (event.target === modal) {
        closeTransactionModal();
    }
}
</script>

<?php include '../includes/dashboard/footer.php'; ?>
