<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'URL Testing';

// List of admin URLs to test
$admin_urls = [
    'Dashboard' => 'admin/dashboard.php',
    'Dashboard (folder)' => 'admin/dashboard/',
    'Users' => 'admin/users.php',
    'Add User' => 'admin/add-user.php',
    'Edit User' => 'admin/edit-user.php?id=2',
    'View User' => 'admin/view-user.php?id=2',
    'Activate User' => 'admin/activate-user.php?id=2',
    'Suspend User' => 'admin/suspend-user.php?id=2',
    'Accounts' => 'admin/accounts/',
    'Virtual Cards' => 'admin/virtual-cards/',
    'Settings' => 'admin/settings/',
    'Logout' => 'auth/logout.php'
];

include '../includes/admin_header.php';
?>

<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        URL Testing
                    </h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <div class="row row-cards">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Admin URL Testing</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-vcenter">
                                    <thead>
                                        <tr>
                                            <th>Page</th>
                                            <th>URL</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($admin_urls as $name => $url): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($name); ?></td>
                                            <td><code><?php echo htmlspecialchars($url); ?></code></td>
                                            <td>
                                                <a href="<?php echo url($url); ?>" class="btn btn-sm btn-primary" target="_blank">
                                                    Test URL
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
