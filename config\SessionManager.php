<?php
/**
 * Session Manager Class
 * Provides secure session management for the banking system
 */

require_once 'ErrorHandler.php';

class SessionManager {
    
    private static $instance = null;
    private $sessionTimeout = 1800; // 30 minutes
    private $regenerateInterval = 300; // 5 minutes
    
    private function __construct() {
        $this->configureSession();
        $this->startSecureSession();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Configure session settings
     */
    private function configureSession() {
        // Set secure session configuration
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        
        // Set session timeout
        ini_set('session.gc_maxlifetime', $this->sessionTimeout);
        
        // Use custom session name
        session_name('BANKING_SESSION');
    }
    
    /**
     * Start secure session
     */
    private function startSecureSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
            
            // Initialize session security
            $this->initializeSessionSecurity();
            
            // Check session validity
            $this->validateSession();
        }
    }
    
    /**
     * Initialize session security measures
     */
    private function initializeSessionSecurity() {
        if (!isset($_SESSION['initiated'])) {
            session_regenerate_id(true);
            $_SESSION['initiated'] = true;
            $_SESSION['created_at'] = time();
            $_SESSION['last_activity'] = time();
            $_SESSION['last_regeneration'] = time();
            $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            
            ErrorHandler::logError('New session initiated', [
                'session_id' => session_id(),
                'ip_address' => $_SESSION['ip_address']
            ], 'INFO');
        }
    }
    
    /**
     * Validate current session
     */
    private function validateSession() {
        // Check if session has expired
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity']) > $this->sessionTimeout) {
            $this->destroySession('Session timeout');
            return false;
        }
        
        // Check IP address consistency (optional - can be disabled for mobile users)
        if (isset($_SESSION['ip_address']) && 
            $_SESSION['ip_address'] !== ($_SERVER['REMOTE_ADDR'] ?? 'unknown')) {
            ErrorHandler::logError('Session IP mismatch detected', [
                'session_ip' => $_SESSION['ip_address'],
                'current_ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_id' => $_SESSION['user_id'] ?? 'unknown'
            ], 'WARNING');
            
            // Uncomment to enforce IP checking
            // $this->destroySession('IP address mismatch');
            // return false;
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        // Regenerate session ID periodically
        if (isset($_SESSION['last_regeneration']) && 
            (time() - $_SESSION['last_regeneration']) > $this->regenerateInterval) {
            $this->regenerateSession();
        }
        
        return true;
    }
    
    /**
     * Regenerate session ID
     */
    private function regenerateSession() {
        $oldSessionId = session_id();
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
        
        ErrorHandler::logError('Session ID regenerated', [
            'old_session_id' => $oldSessionId,
            'new_session_id' => session_id(),
            'user_id' => $_SESSION['user_id'] ?? 'unknown'
        ], 'INFO');
    }
    
    /**
     * Set session data
     */
    public static function set($key, $value) {
        $instance = self::getInstance();
        $_SESSION[$key] = $value;
        
        ErrorHandler::logError('Session data set', [
            'key' => $key,
            'user_id' => $_SESSION['user_id'] ?? 'unknown'
        ], 'DEBUG');
    }
    
    /**
     * Get session data
     */
    public static function get($key, $default = null) {
        $instance = self::getInstance();
        return $_SESSION[$key] ?? $default;
    }
    
    /**
     * Check if session key exists
     */
    public static function has($key) {
        $instance = self::getInstance();
        return isset($_SESSION[$key]);
    }
    
    /**
     * Remove session data
     */
    public static function remove($key) {
        $instance = self::getInstance();
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
            
            ErrorHandler::logError('Session data removed', [
                'key' => $key,
                'user_id' => $_SESSION['user_id'] ?? 'unknown'
            ], 'DEBUG');
        }
    }
    
    /**
     * Login user
     */
    public static function login($userId, $userType = 'user', $additionalData = []) {
        $instance = self::getInstance();
        
        // Regenerate session ID on login
        session_regenerate_id(true);
        
        // Set user session data
        $_SESSION['user_id'] = $userId;
        $_SESSION['user_type'] = $userType;
        $_SESSION['logged_in'] = true;
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        $_SESSION['last_regeneration'] = time();
        
        // Set additional data
        foreach ($additionalData as $key => $value) {
            $_SESSION[$key] = $value;
        }
        
        ErrorHandler::logError('User logged in', [
            'user_id' => $userId,
            'user_type' => $userType,
            'session_id' => session_id(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ], 'INFO');
        
        return true;
    }
    
    /**
     * Logout user
     */
    public static function logout($reason = 'User logout') {
        $instance = self::getInstance();
        
        ErrorHandler::logError('User logged out', [
            'user_id' => $_SESSION['user_id'] ?? 'unknown',
            'reason' => $reason,
            'session_duration' => isset($_SESSION['login_time']) ? (time() - $_SESSION['login_time']) : 'unknown'
        ], 'INFO');
        
        $instance->destroySession($reason);
    }
    
    /**
     * Check if user is logged in
     */
    public static function isLoggedIn() {
        $instance = self::getInstance();
        return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
    }
    
    /**
     * Check if user is admin
     */
    public static function isAdmin() {
        $instance = self::getInstance();
        return self::isLoggedIn() && 
               isset($_SESSION['user_type']) && 
               $_SESSION['user_type'] === 'admin';
    }
    
    /**
     * Get current user ID
     */
    public static function getUserId() {
        $instance = self::getInstance();
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * Get current user type
     */
    public static function getUserType() {
        $instance = self::getInstance();
        return $_SESSION['user_type'] ?? null;
    }
    
    /**
     * Destroy session
     */
    private function destroySession($reason = 'Session destroyed') {
        ErrorHandler::logError('Session destroyed', [
            'reason' => $reason,
            'user_id' => $_SESSION['user_id'] ?? 'unknown',
            'session_id' => session_id()
        ], 'INFO');
        
        // Clear session data
        $_SESSION = [];
        
        // Delete session cookie
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params['path'], $params['domain'],
                $params['secure'], $params['httponly']
            );
        }
        
        // Destroy session
        session_destroy();
    }
    
    /**
     * Get session info
     */
    public static function getSessionInfo() {
        $instance = self::getInstance();
        
        return [
            'session_id' => session_id(),
            'user_id' => $_SESSION['user_id'] ?? null,
            'user_type' => $_SESSION['user_type'] ?? null,
            'logged_in' => self::isLoggedIn(),
            'created_at' => $_SESSION['created_at'] ?? null,
            'login_time' => $_SESSION['login_time'] ?? null,
            'last_activity' => $_SESSION['last_activity'] ?? null,
            'ip_address' => $_SESSION['ip_address'] ?? null,
            'time_remaining' => isset($_SESSION['last_activity']) ? 
                ($instance->sessionTimeout - (time() - $_SESSION['last_activity'])) : null
        ];
    }
    
    /**
     * Extend session timeout
     */
    public static function extendSession() {
        $instance = self::getInstance();
        $_SESSION['last_activity'] = time();
        
        ErrorHandler::logError('Session extended', [
            'user_id' => $_SESSION['user_id'] ?? 'unknown'
        ], 'DEBUG');
    }
    
    /**
     * Set session timeout
     */
    public static function setTimeout($seconds) {
        $instance = self::getInstance();
        $instance->sessionTimeout = $seconds;
        ini_set('session.gc_maxlifetime', $seconds);
    }
    
    /**
     * Clean up expired sessions (call from cron job)
     */
    public static function cleanupExpiredSessions() {
        session_gc();
        
        ErrorHandler::logError('Session cleanup performed', [], 'INFO');
    }
}

// Initialize session manager
SessionManager::getInstance();
?>
