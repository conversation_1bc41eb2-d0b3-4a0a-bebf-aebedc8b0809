<?php
/**
 * Master Session Test Runner
 * Executes all session-related tests and provides comprehensive reporting
 */

require_once __DIR__ . '/../../config/config.php';

class SessionTestRunner {
    
    private $testSuites = [];
    private $overallResults = [];
    private $startTime;
    
    public function __construct() {
        $this->startTime = microtime(true);
        
        echo "<!DOCTYPE html><html><head><title>Session Test Suite Results</title>";
        echo "<style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; }
            .test-suite { margin: 20px 0; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
            .suite-header { background: #f8f9fa; padding: 15px; font-weight: bold; border-bottom: 1px solid #ddd; }
            .suite-content { padding: 15px; }
            .test-pass { color: #28a745; font-weight: bold; }
            .test-fail { color: #dc3545; font-weight: bold; }
            .test-warning { color: #ffc107; font-weight: bold; }
            .test-info { color: #17a2b8; }
            .summary { background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
            .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745 0%, #20c997 100%); transition: width 0.3s ease; }
            .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
            .metric-card { background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
            .metric-label { color: #6c757d; font-size: 14px; }
            .test-navigation { margin: 20px 0; }
            .nav-button { display: inline-block; padding: 8px 16px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
            .nav-button:hover { background: #0056b3; }
            iframe { width: 100%; height: 600px; border: 1px solid #ddd; border-radius: 8px; }
        </style></head><body>";
        
        echo "<div class='container'>";
        echo "<div class='header'>";
        echo "<h1>🔐 Comprehensive Session Test Suite</h1>";
        echo "<p>Complete testing of user session management, security, performance, and integration</p>";
        echo "</div>";
        
        $this->registerTestSuites();
    }
    
    /**
     * Register all test suites
     */
    private function registerTestSuites() {
        $this->testSuites = [
            'comprehensive' => [
                'name' => 'Comprehensive Session Tests',
                'description' => 'Core session functionality, login/logout, data management',
                'file' => 'comprehensive_session_test.php',
                'priority' => 1
            ],
            'security' => [
                'name' => 'Session Security Tests',
                'description' => 'Security measures, CSRF protection, hijacking prevention',
                'file' => 'session_security_test.php',
                'priority' => 2
            ],
            'performance' => [
                'name' => 'Session Performance Tests',
                'description' => 'Performance benchmarks, stress testing, memory usage',
                'file' => 'session_performance_test.php',
                'priority' => 3
            ],
            'integration' => [
                'name' => 'Session Integration Tests',
                'description' => 'Database integration, real-world scenarios, user flows',
                'file' => 'session_integration_test.php',
                'priority' => 4
            ]
        ];
    }
    
    /**
     * Run all test suites
     */
    public function runAllTests() {
        echo "<div class='test-navigation'>";
        echo "<h2>📋 Test Suite Navigation</h2>";
        foreach ($this->testSuites as $key => $suite) {
            echo "<a href='#{$key}' class='nav-button'>{$suite['name']}</a>";
        }
        echo "</div>";
        
        echo "<div class='summary'>";
        echo "<h2>🚀 Test Execution Progress</h2>";
        echo "<div id='overall-progress'>";
        echo "<div class='progress-bar'><div class='progress-fill' style='width: 0%'></div></div>";
        echo "<p id='progress-text'>Initializing tests...</p>";
        echo "</div>";
        echo "</div>";
        
        $totalSuites = count($this->testSuites);
        $completedSuites = 0;
        
        // Sort by priority
        uasort($this->testSuites, function($a, $b) {
            return $a['priority'] - $b['priority'];
        });
        
        foreach ($this->testSuites as $key => $suite) {
            $this->runTestSuite($key, $suite);
            $completedSuites++;
            
            $progress = ($completedSuites / $totalSuites) * 100;
            echo "<script>
                document.querySelector('.progress-fill').style.width = '{$progress}%';
                document.getElementById('progress-text').textContent = 'Completed {$completedSuites}/{$totalSuites} test suites ({$progress}%)';
            </script>";
            
            // Flush output for real-time updates
            if (ob_get_level()) ob_flush();
            flush();
        }
        
        $this->displayOverallSummary();
        $this->displayRecommendations();
        
        echo "</div></body></html>";
    }
    
    /**
     * Run individual test suite
     */
    private function runTestSuite($key, $suite) {
        echo "<div class='test-suite' id='{$key}'>";
        echo "<div class='suite-header'>";
        echo "<h3>🧪 {$suite['name']}</h3>";
        echo "<p>{$suite['description']}</p>";
        echo "</div>";
        
        echo "<div class='suite-content'>";
        
        $testFile = __DIR__ . '/' . $suite['file'];
        
        if (!file_exists($testFile)) {
            echo "<div class='test-fail'>❌ Test file not found: {$suite['file']}</div>";
            $this->overallResults[$key] = ['status' => 'error', 'message' => 'File not found'];
            echo "</div></div>";
            return;
        }
        
        echo "<p><strong>📁 Test File:</strong> {$suite['file']}</p>";
        echo "<p><strong>⏱️ Started:</strong> " . date('Y-m-d H:i:s') . "</p>";
        
        $startTime = microtime(true);
        
        // Capture output from test file
        ob_start();
        
        try {
            // Include and run the test
            include $testFile;
            
            $output = ob_get_contents();
            $endTime = microtime(true);
            $duration = $endTime - $startTime;
            
            ob_end_clean();
            
            // Parse results from output
            $results = $this->parseTestResults($output);
            $this->overallResults[$key] = $results;
            $this->overallResults[$key]['duration'] = $duration;
            
            echo "<div class='metrics'>";
            echo "<div class='metric-card'>";
            echo "<div class='metric-value'>{$results['total']}</div>";
            echo "<div class='metric-label'>Total Tests</div>";
            echo "</div>";
            
            echo "<div class='metric-card'>";
            echo "<div class='metric-value test-pass'>{$results['passed']}</div>";
            echo "<div class='metric-label'>Passed</div>";
            echo "</div>";
            
            echo "<div class='metric-card'>";
            echo "<div class='metric-value test-fail'>{$results['failed']}</div>";
            echo "<div class='metric-label'>Failed</div>";
            echo "</div>";
            
            echo "<div class='metric-card'>";
            echo "<div class='metric-value'>" . number_format($duration, 2) . "s</div>";
            echo "<div class='metric-label'>Duration</div>";
            echo "</div>";
            echo "</div>";
            
            $successRate = $results['total'] > 0 ? ($results['passed'] / $results['total']) * 100 : 0;
            echo "<div class='progress-bar'>";
            echo "<div class='progress-fill' style='width: {$successRate}%'></div>";
            echo "</div>";
            echo "<p><strong>Success Rate:</strong> " . number_format($successRate, 1) . "%</p>";
            
            // Display detailed output in iframe for better formatting
            $outputFile = "temp_output_{$key}.html";
            file_put_contents(__DIR__ . '/' . $outputFile, $output);
            
            echo "<h4>📊 Detailed Test Results:</h4>";
            echo "<iframe src='{$outputFile}' frameborder='0'></iframe>";
            
            // Schedule cleanup of temp file
            register_shutdown_function(function() use ($outputFile) {
                $file = __DIR__ . '/' . $outputFile;
                if (file_exists($file)) {
                    unlink($file);
                }
            });
            
        } catch (Exception $e) {
            ob_end_clean();
            echo "<div class='test-fail'>❌ Error running test suite: " . $e->getMessage() . "</div>";
            $this->overallResults[$key] = [
                'status' => 'error', 
                'message' => $e->getMessage(),
                'total' => 0,
                'passed' => 0,
                'failed' => 1,
                'duration' => microtime(true) - $startTime
            ];
        }
        
        echo "<p><strong>✅ Completed:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "</div></div>";
    }
    
    /**
     * Parse test results from output
     */
    private function parseTestResults($output) {
        $results = [
            'total' => 0,
            'passed' => 0,
            'failed' => 0,
            'warnings' => 0,
            'status' => 'completed'
        ];
        
        // Count test results
        $results['passed'] = substr_count($output, '[PASS]');
        $results['failed'] = substr_count($output, '[FAIL]');
        $results['warnings'] = substr_count($output, '[WARNING]');
        $results['total'] = $results['passed'] + $results['failed'] + $results['warnings'];
        
        return $results;
    }
    
    /**
     * Display overall summary
     */
    private function displayOverallSummary() {
        $totalTests = 0;
        $totalPassed = 0;
        $totalFailed = 0;
        $totalWarnings = 0;
        $totalDuration = microtime(true) - $this->startTime;
        
        foreach ($this->overallResults as $result) {
            $totalTests += $result['total'] ?? 0;
            $totalPassed += $result['passed'] ?? 0;
            $totalFailed += $result['failed'] ?? 0;
            $totalWarnings += $result['warnings'] ?? 0;
        }
        
        $overallSuccessRate = $totalTests > 0 ? ($totalPassed / $totalTests) * 100 : 0;
        
        echo "<div class='summary'>";
        echo "<h2>📈 Overall Test Summary</h2>";
        
        echo "<div class='metrics'>";
        echo "<div class='metric-card'>";
        echo "<div class='metric-value'>{$totalTests}</div>";
        echo "<div class='metric-label'>Total Tests Executed</div>";
        echo "</div>";
        
        echo "<div class='metric-card'>";
        echo "<div class='metric-value test-pass'>{$totalPassed}</div>";
        echo "<div class='metric-label'>Tests Passed</div>";
        echo "</div>";
        
        echo "<div class='metric-card'>";
        echo "<div class='metric-value test-fail'>{$totalFailed}</div>";
        echo "<div class='metric-label'>Tests Failed</div>";
        echo "</div>";
        
        echo "<div class='metric-card'>";
        echo "<div class='metric-value test-warning'>{$totalWarnings}</div>";
        echo "<div class='metric-label'>Warnings</div>";
        echo "</div>";
        
        echo "<div class='metric-card'>";
        echo "<div class='metric-value'>" . number_format($overallSuccessRate, 1) . "%</div>";
        echo "<div class='metric-label'>Success Rate</div>";
        echo "</div>";
        
        echo "<div class='metric-card'>";
        echo "<div class='metric-value'>" . number_format($totalDuration, 2) . "s</div>";
        echo "<div class='metric-label'>Total Duration</div>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='progress-bar'>";
        echo "<div class='progress-fill' style='width: {$overallSuccessRate}%'></div>";
        echo "</div>";
        
        $status = $totalFailed === 0 ? '✅ All Tests Passed' : "⚠️ {$totalFailed} Tests Failed";
        echo "<h3>{$status}</h3>";
        
        echo "</div>";
    }
    
    /**
     * Display recommendations based on test results
     */
    private function displayRecommendations() {
        echo "<div class='summary'>";
        echo "<h2>💡 Recommendations</h2>";
        
        $totalFailed = array_sum(array_column($this->overallResults, 'failed'));
        
        if ($totalFailed === 0) {
            echo "<div class='test-pass'>";
            echo "<h3>🎉 Excellent! All session tests passed.</h3>";
            echo "<ul>";
            echo "<li>✅ Session management is working correctly</li>";
            echo "<li>✅ Security measures are properly implemented</li>";
            echo "<li>✅ Performance is within acceptable limits</li>";
            echo "<li>✅ Integration with the banking system is successful</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div class='test-fail'>";
            echo "<h3>⚠️ Issues detected that need attention:</h3>";
            echo "<ul>";
            echo "<li>Review failed tests and address underlying issues</li>";
            echo "<li>Check session configuration and security settings</li>";
            echo "<li>Verify database connectivity and data integrity</li>";
            echo "<li>Consider performance optimizations if needed</li>";
            echo "</ul>";
            echo "</div>";
        }
        
        echo "<h3>🔧 General Recommendations:</h3>";
        echo "<ul>";
        echo "<li>Run these tests regularly during development</li>";
        echo "<li>Monitor session performance in production</li>";
        echo "<li>Keep session security measures up to date</li>";
        echo "<li>Review and update timeout settings based on usage patterns</li>";
        echo "<li>Implement session monitoring and alerting</li>";
        echo "</ul>";
        
        echo "</div>";
    }
}

// Run all session tests
$testRunner = new SessionTestRunner();
$testRunner->runAllTests();
?>
