[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://github.com/myclabs/DeepCopy.git
	fetch = +refs/heads/*:refs/remotes/origin/*
	pushurl = **************:myclabs/DeepCopy.git
[branch "1.x"]
	remote = origin
	merge = refs/heads/1.x
[remote "composer"]
	url = https://github.com/myclabs/DeepCopy.git
	fetch = +refs/heads/*:refs/remotes/composer/*
