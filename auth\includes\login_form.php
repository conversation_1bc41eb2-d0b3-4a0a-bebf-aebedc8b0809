<?php
/**
 * User Login Form Component
 * Contains the login form with validation and error handling
 */
?>

<div class="container-fluid">
    <div class="row">
        <!-- Left Panel - Login Form -->
        <div class="col-md-5 left-panel">
            <a href="<?php echo url(''); ?>" class="back-link">
                <i class="fas fa-arrow-left"></i> Back to Main Site
            </a>

            <!-- Logo -->
            <div class="logo">
                <?php if (!empty($site_settings['site_logo']) && file_exists($site_settings['site_logo'])): ?>
                    <img src="<?php echo htmlspecialchars($site_settings['site_logo']); ?>"
                         alt="<?php echo htmlspecialchars($site_settings['site_name']); ?>"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <h2 class="logo-fallback" style="display: none;"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                <?php else: ?>
                    <h2 class="logo-fallback"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                <?php endif; ?>
            </div>

            <!-- Welcome Text -->
            <div class="welcome-text">
                <h1>Welcome Back</h1>
                <p>Please sign in to access your banking account securely</p>
            </div>

            <!-- Login Form -->
            <div class="login-form">
                <!-- Error Messages -->
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Timeout Message -->
                <?php if (isset($_GET['timeout'])): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-clock"></i>
                        Your session has expired for security reasons. Please log in again.
                    </div>
                <?php endif; ?>

                <!-- Success Messages -->
                <?php if (hasFlashMessage('success')): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars(getFlashMessage('success')); ?>
                    </div>
                <?php endif; ?>

                <!-- Info Messages -->
                <?php if (hasFlashMessage('info')): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <?php echo htmlspecialchars(getFlashMessage('info')); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="" id="loginForm">
                    <div class="form-group">
                        <label for="username" class="form-label">Username, Email, or Account Number</label>
                        <div class="input-group">
                            <i class="input-icon fas fa-user"></i>
                            <input type="text"
                                   class="form-control"
                                   id="username"
                                   name="username"
                                   placeholder="Enter your login credentials"
                                   value="<?php echo htmlspecialchars($username ?? ''); ?>"
                                   required
                                   autofocus
                                   autocomplete="username">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <i class="input-icon fas fa-lock"></i>
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   placeholder="Enter your password"
                                   required
                                   autocomplete="current-password">
                        </div>
                        <div class="forgot-password">
                            <a href="<?php echo url('auth/forgot-password.php'); ?>">Forgot your password?</a>
                        </div>
                    </div>

                    <button type="submit" class="btn-login" id="loginBtn">
                        <i class="fas fa-sign-in-alt"></i> Sign In to Account
                    </button>
                </form>

                <!-- Registration Link -->
                <div class="signup-link">
                    Don't have an account? <a href="<?php echo url('register.php'); ?>">Create Account</a>
                </div>

                <!-- Security Notice -->
                <div class="security-notice">
                    <h6><i class="fas fa-shield-alt"></i> Secure Banking</h6>
                    <p>
                        Your connection is encrypted and secure. We use industry-standard security measures 
                        to protect your personal and financial information.
                    </p>
                </div>
            </div>
        </div>

        <!-- Right Panel - Enhanced Banking Visual -->
        <div class="col-md-7 right-panel">
            <div class="right-panel-content">
                <div class="feature-illustration">
                    <div class="dashboard-svg-container">
                        <!-- Custom SVG Dashboard Illustration -->
                        <svg class="dashboard-svg" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <!-- Gradients for modern look -->
                                <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95"/>
                                    <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:0.9"/>
                                </linearGradient>
                                <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8"/>
                                    <stop offset="100%" style="stop-color:#10b981;stop-opacity:0.2"/>
                                </linearGradient>
                                <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1"/>
                                    <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1"/>
                                </linearGradient>
                                <!-- Drop shadow filter -->
                                <filter id="cardShadow" x="-20%" y="-20%" width="140%" height="140%">
                                    <feDropShadow dx="0" dy="4" stdDeviation="8" flood-opacity="0.15"/>
                                </filter>
                            </defs>

                            <!-- Account Balance Card -->
                            <g class="dashboard-card" filter="url(#cardShadow)">
                                <rect x="20" y="20" width="160" height="90" rx="12" fill="url(#cardGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                                <rect x="30" y="30" width="30" height="4" rx="2" fill="url(#accentGradient)"/>
                                <text x="30" y="50" font-family="Inter, sans-serif" font-size="10" fill="#64748b" font-weight="500">Account Balance</text>
                                <text x="30" y="70" font-family="Inter, sans-serif" font-size="16" fill="#1e293b" font-weight="700">$24,580.50</text>
                                <circle cx="150" cy="45" r="12" fill="rgba(16, 185, 129, 0.1)"/>
                                <path d="M145 45 L148 48 L155 41" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round"/>
                                <text x="30" y="90" font-family="Inter, sans-serif" font-size="8" fill="#10b981" font-weight="500">+2.5% this month</text>
                            </g>

                            <!-- Transaction Chart Card -->
                            <g class="dashboard-card" filter="url(#cardShadow)">
                                <rect x="200" y="40" width="180" height="120" rx="12" fill="url(#cardGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                                <rect x="210" y="50" width="30" height="4" rx="2" fill="url(#accentGradient)"/>
                                <text x="210" y="70" font-family="Inter, sans-serif" font-size="10" fill="#64748b" font-weight="500">Monthly Spending</text>

                                <!-- Chart Area -->
                                <rect x="210" y="80" width="160" height="60" rx="4" fill="rgba(248, 250, 252, 0.5)"/>

                                <!-- Chart Lines -->
                                <path class="chart-line" d="M220 130 L240 120 L260 125 L280 110 L300 115 L320 105 L340 100 L360 95"
                                      stroke="url(#accentGradient)" stroke-width="2.5" fill="none" stroke-linecap="round"/>
                                <path class="chart-line" d="M220 135 L240 130 L260 128 L280 125 L300 120 L320 118 L340 115 L360 110"
                                      stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" opacity="0.7"/>

                                <!-- Chart Fill Area -->
                                <path d="M220 130 L240 120 L260 125 L280 110 L300 115 L320 105 L340 100 L360 95 L360 140 L220 140 Z"
                                      fill="url(#chartGradient)" opacity="0.3"/>

                                <!-- Data Points -->
                                <circle class="pulse-dot" cx="240" cy="120" r="3" fill="#4f46e5"/>
                                <circle class="pulse-dot" cx="280" cy="110" r="3" fill="#4f46e5"/>
                                <circle class="pulse-dot" cx="320" cy="105" r="3" fill="#4f46e5"/>
                                <circle class="pulse-dot" cx="360" cy="95" r="3" fill="#4f46e5"/>
                            </g>

                            <!-- Quick Actions Card -->
                            <g class="dashboard-card" filter="url(#cardShadow)">
                                <rect x="20" y="130" width="160" height="100" rx="12" fill="url(#cardGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                                <rect x="30" y="140" width="30" height="4" rx="2" fill="url(#accentGradient)"/>
                                <text x="30" y="160" font-family="Inter, sans-serif" font-size="10" fill="#64748b" font-weight="500">Quick Actions</text>

                                <!-- Action Icons -->
                                <g transform="translate(35, 175)">
                                    <circle cx="0" cy="0" r="15" fill="rgba(79, 70, 229, 0.1)"/>
                                    <path d="M-6 -2 L6 -2 M0 -8 L0 6 M-4 2 L4 2" stroke="#4f46e5" stroke-width="1.5" stroke-linecap="round"/>
                                </g>
                                <text x="30" y="205" font-family="Inter, sans-serif" font-size="7" fill="#64748b">Transfer</text>

                                <g transform="translate(80, 175)">
                                    <circle cx="0" cy="0" r="15" fill="rgba(16, 185, 129, 0.1)"/>
                                    <rect x="-6" y="-6" width="12" height="8" rx="2" fill="none" stroke="#10b981" stroke-width="1.5"/>
                                    <path d="M-3 -2 L3 -2 M-3 1 L3 1" stroke="#10b981" stroke-width="1"/>
                                </g>
                                <text x="72" y="205" font-family="Inter, sans-serif" font-size="7" fill="#64748b">Pay Bills</text>

                                <g transform="translate(125, 175)">
                                    <circle cx="0" cy="0" r="15" fill="rgba(245, 158, 11, 0.1)"/>
                                    <circle cx="0" cy="0" r="6" fill="none" stroke="#f59e0b" stroke-width="1.5"/>
                                    <path d="M0 -3 L0 0 L3 3" stroke="#f59e0b" stroke-width="1.5" stroke-linecap="round"/>
                                </g>
                                <text x="115" y="205" font-family="Inter, sans-serif" font-size="7" fill="#64748b">History</text>
                            </g>

                            <!-- Floating Elements -->
                            <g opacity="0.6">
                                <circle cx="350" cy="25" r="2" fill="rgba(255,255,255,0.8)">
                                    <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="50" cy="250" r="1.5" fill="rgba(255,255,255,0.6)">
                                    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="4s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="380" cy="180" r="1" fill="rgba(255,255,255,0.7)">
                                    <animate attributeName="opacity" values="0.4;0.7;0.4" dur="2.5s" repeatCount="indefinite"/>
                                </circle>
                            </g>
                        </svg>
                    </div>
                </div>

                <div class="bottom-text">
                    <h3>Secure Online Banking</h3>
                    <p>
                        Access your accounts, transfer funds, pay bills, and manage your finances
                        with confidence using our secure banking platform.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Enhanced UX -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');

    // Form submission handling
    loginForm.addEventListener('submit', function(e) {
        // Add loading state
        loginBtn.classList.add('loading');
        loginBtn.disabled = true;
        
        // Basic client-side validation
        if (!usernameInput.value.trim() || !passwordInput.value.trim()) {
            e.preventDefault();
            loginBtn.classList.remove('loading');
            loginBtn.disabled = false;
            
            // Show error
            showAlert('Please fill in all required fields.', 'danger');
            return;
        }
    });

    // Input validation feedback
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.style.borderColor = '#dc2626';
            } else {
                this.style.borderColor = '#d1d5db';
            }
        });

        input.addEventListener('input', function() {
            if (this.style.borderColor === 'rgb(220, 38, 38)') {
                this.style.borderColor = '#d1d5db';
            }
        });
    });

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (!alert.classList.contains('alert-danger')) {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            }, 5000);
        }
    });

    function showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        
        const form = document.querySelector('.login-form');
        form.insertBefore(alertDiv, form.firstChild);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            alertDiv.style.opacity = '0';
            setTimeout(() => alertDiv.remove(), 300);
        }, 5000);
    }
});
</script>
