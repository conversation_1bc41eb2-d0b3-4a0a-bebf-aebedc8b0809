<?php
/**
 * User Login Form Component
 * Contains the login form with validation and error handling
 */
?>

<div class="container-fluid">
    <div class="row">
        <!-- Left Panel - Login Form -->
        <div class="col-md-5 left-panel">
            <a href="<?php echo url(''); ?>" class="back-link">
                <i class="fas fa-arrow-left"></i> Back to Main Site
            </a>

            <!-- Logo -->
            <div class="logo">
                <?php if (!empty($site_settings['site_logo']) && file_exists($site_settings['site_logo'])): ?>
                    <img src="<?php echo htmlspecialchars($site_settings['site_logo']); ?>"
                         alt="<?php echo htmlspecialchars($site_settings['site_name']); ?>"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <h2 class="logo-fallback" style="display: none;"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                <?php else: ?>
                    <h2 class="logo-fallback"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                <?php endif; ?>
            </div>

            <!-- Welcome Text -->
            <div class="welcome-text">
                <h1>Welcome Back</h1>
                <p>Please sign in to access your banking account securely</p>
            </div>

            <!-- Login Form -->
            <div class="login-form">
                <!-- Error Messages -->
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Timeout Message -->
                <?php if (isset($_GET['timeout'])): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-clock"></i>
                        Your session has expired for security reasons. Please log in again.
                    </div>
                <?php endif; ?>

                <!-- Success Messages -->
                <?php if (hasFlashMessage('success')): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars(getFlashMessage('success')); ?>
                    </div>
                <?php endif; ?>

                <!-- Info Messages -->
                <?php if (hasFlashMessage('info')): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <?php echo htmlspecialchars(getFlashMessage('info')); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="" id="loginForm">
                    <div class="form-group">
                        <label for="username" class="form-label">Username, Email, or Account Number</label>
                        <div class="input-group">
                            <i class="input-icon fas fa-user"></i>
                            <input type="text"
                                   class="form-control"
                                   id="username"
                                   name="username"
                                   placeholder="Enter your login credentials"
                                   value="<?php echo htmlspecialchars($username ?? ''); ?>"
                                   required
                                   autofocus
                                   autocomplete="username">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <i class="input-icon fas fa-lock"></i>
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   placeholder="Enter your password"
                                   required
                                   autocomplete="current-password">
                        </div>
                        <div class="forgot-password">
                            <a href="<?php echo url('auth/forgot-password.php'); ?>">Forgot your password?</a>
                        </div>
                    </div>

                    <button type="submit" class="btn-login" id="loginBtn">
                        <i class="fas fa-sign-in-alt"></i> Sign In to Account
                    </button>
                </form>

                <!-- Registration Link -->
                <div class="signup-link">
                    Don't have an account? <a href="<?php echo url('register.php'); ?>">Create Account</a>
                </div>

                <!-- Security Notice -->
                <div class="security-notice">
                    <h6><i class="fas fa-shield-alt"></i> Secure Banking</h6>
                    <p>
                        Your connection is encrypted and secure. We use industry-standard security measures 
                        to protect your personal and financial information.
                    </p>
                </div>
            </div>
        </div>

        <!-- Right Panel - Banking Visual -->
        <div class="col-md-7 right-panel">
            <div class="right-panel-content">
                <div class="feature-illustration">
                    <i class="banking-icon fas fa-university"></i>
                </div>
                
                <div class="bottom-text">
                    <h3>Secure Online Banking</h3>
                    <p>
                        Access your accounts, transfer funds, pay bills, and manage your finances 
                        with confidence using our secure banking platform.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Enhanced UX -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');

    // Form submission handling
    loginForm.addEventListener('submit', function(e) {
        // Add loading state
        loginBtn.classList.add('loading');
        loginBtn.disabled = true;
        
        // Basic client-side validation
        if (!usernameInput.value.trim() || !passwordInput.value.trim()) {
            e.preventDefault();
            loginBtn.classList.remove('loading');
            loginBtn.disabled = false;
            
            // Show error
            showAlert('Please fill in all required fields.', 'danger');
            return;
        }
    });

    // Input validation feedback
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.style.borderColor = '#dc2626';
            } else {
                this.style.borderColor = '#d1d5db';
            }
        });

        input.addEventListener('input', function() {
            if (this.style.borderColor === 'rgb(220, 38, 38)') {
                this.style.borderColor = '#d1d5db';
            }
        });
    });

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (!alert.classList.contains('alert-danger')) {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            }, 5000);
        }
    });

    function showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        
        const form = document.querySelector('.login-form');
        form.insertBefore(alertDiv, form.firstChild);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            alertDiv.style.opacity = '0';
            setTimeout(() => alertDiv.remove(), 300);
        }, 5000);
    }
});
</script>
