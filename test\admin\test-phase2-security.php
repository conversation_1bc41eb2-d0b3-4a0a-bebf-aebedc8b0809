<?php
/**
 * Phase 2 Security Components Test
 * Tests <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Input<PERSON><PERSON><PERSON><PERSON>, SessionManager, and AuditLogger
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>\n";
echo "<html lang='en'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "<title>Phase 2 Security Components Test</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }\n";
echo ".test-container { max-width: 1200px; margin: 0 auto; }\n";
echo ".test-section { margin: 20px 0; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }\n";
echo ".success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0; }\n";
echo ".error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }\n";
echo ".warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin: 10px 0; }\n";
echo ".info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin: 10px 0; }\n";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }\n";
echo ".component-test { border: 1px solid #e9ecef; margin: 10px 0; padding: 15px; border-radius: 4px; }\n";
echo ".component-test h4 { margin: 0 0 10px 0; color: #495057; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<div class='test-container'>\n";
echo "<h1>🔒 Phase 2 Security Components Test Suite</h1>\n";
echo "<p><strong>Test Date:</strong> " . date('Y-m-d H:i:s') . "</p>\n";

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

function runTest($test_name, $test_function) {
    global $test_results, $total_tests, $passed_tests;
    
    $total_tests++;
    echo "<div class='component-test'>\n";
    echo "<h4>🔍 Testing: {$test_name}</h4>\n";
    
    try {
        $result = $test_function();
        if ($result['success']) {
            echo "<div class='success'>✅ PASSED: {$result['message']}</div>\n";
            $passed_tests++;
            $test_results[$test_name] = 'PASSED';
        } else {
            echo "<div class='error'>❌ FAILED: {$result['message']}</div>\n";
            $test_results[$test_name] = 'FAILED';
        }
        
        if (isset($result['details'])) {
            echo "<pre>{$result['details']}</pre>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ ERROR: {$e->getMessage()}</div>\n";
        $test_results[$test_name] = 'ERROR';
    }
    
    echo "</div>\n";
}

// Test 1: ErrorHandler Class
runTest("ErrorHandler Class", function() {
    $file = '../../config/ErrorHandler.php';
    
    if (!file_exists($file)) {
        return ['success' => false, 'message' => 'ErrorHandler.php file does not exist'];
    }
    
    // Include and test
    require_once $file;
    
    if (!class_exists('ErrorHandler')) {
        return ['success' => false, 'message' => 'ErrorHandler class not found'];
    }
    
    // Test logging
    ErrorHandler::logError('Test error message', ['test' => true], 'INFO');
    
    // Check if log file was created
    $logFile = '../../logs/error.log';
    if (!file_exists($logFile)) {
        return ['success' => false, 'message' => 'Error log file was not created'];
    }
    
    $logContent = file_get_contents($logFile);
    if (strpos($logContent, 'Test error message') === false) {
        return ['success' => false, 'message' => 'Test message not found in log'];
    }
    
    return [
        'success' => true, 
        'message' => 'ErrorHandler class working correctly',
        'details' => 'Log file size: ' . filesize($logFile) . ' bytes'
    ];
});

// Test 2: InputValidator Class
runTest("InputValidator Class", function() {
    $file = '../../config/InputValidator.php';
    
    if (!file_exists($file)) {
        return ['success' => false, 'message' => 'InputValidator.php file does not exist'];
    }
    
    require_once $file;
    
    if (!class_exists('InputValidator')) {
        return ['success' => false, 'message' => 'InputValidator class not found'];
    }
    
    // Test validation
    $testData = [
        'email' => '<EMAIL>',
        'amount' => '100.50',
        'name' => 'John Doe'
    ];
    
    $rules = [
        'email' => 'required|email',
        'amount' => 'required|numeric|min_value:0',
        'name' => 'required|alpha|max_length:50'
    ];
    
    $result = InputValidator::validate($testData, $rules);
    
    if ($result === false) {
        return ['success' => false, 'message' => 'Valid data failed validation'];
    }
    
    // Test invalid data
    $invalidData = [
        'email' => 'invalid-email',
        'amount' => '-50',
        'name' => ''
    ];
    
    $invalidResult = InputValidator::validate($invalidData, $rules);
    
    if ($invalidResult !== false) {
        return ['success' => false, 'message' => 'Invalid data passed validation'];
    }
    
    return [
        'success' => true, 
        'message' => 'InputValidator class working correctly',
        'details' => 'Validation errors: ' . json_encode(InputValidator::getErrors())
    ];
});

// Test 3: SessionManager Class
runTest("SessionManager Class", function() {
    $file = '../../config/SessionManager.php';
    
    if (!file_exists($file)) {
        return ['success' => false, 'message' => 'SessionManager.php file does not exist'];
    }
    
    require_once $file;
    
    if (!class_exists('SessionManager')) {
        return ['success' => false, 'message' => 'SessionManager class not found'];
    }
    
    // Test session operations
    SessionManager::set('test_key', 'test_value');
    
    if (SessionManager::get('test_key') !== 'test_value') {
        return ['success' => false, 'message' => 'Session set/get not working'];
    }
    
    if (!SessionManager::has('test_key')) {
        return ['success' => false, 'message' => 'Session has() not working'];
    }
    
    SessionManager::remove('test_key');
    
    if (SessionManager::has('test_key')) {
        return ['success' => false, 'message' => 'Session remove() not working'];
    }
    
    // Test login/logout
    SessionManager::login(123, 'admin', ['test_data' => 'value']);
    
    if (!SessionManager::isLoggedIn()) {
        return ['success' => false, 'message' => 'Login not working'];
    }
    
    if (!SessionManager::isAdmin()) {
        return ['success' => false, 'message' => 'Admin check not working'];
    }
    
    $sessionInfo = SessionManager::getSessionInfo();
    
    return [
        'success' => true, 
        'message' => 'SessionManager class working correctly',
        'details' => 'Session info: ' . json_encode($sessionInfo, JSON_PRETTY_PRINT)
    ];
});

// Test 4: AuditLogger Class
runTest("AuditLogger Class", function() {
    $file = '../../config/AuditLogger.php';

    if (!file_exists($file)) {
        return ['success' => false, 'message' => 'AuditLogger.php file does not exist'];
    }

    // Suppress errors for database connection issues during testing
    $old_error_reporting = error_reporting(E_ERROR);

    try {
        require_once $file;

        if (!class_exists('AuditLogger')) {
            return ['success' => false, 'message' => 'AuditLogger class not found'];
        }

        // Test audit logging (file-based logging should work even if DB fails)
        AuditLogger::log(AuditLogger::ACTION_ADMIN, 'Test admin action', [
            'resource_type' => 'user',
            'resource_id' => '123',
            'additional_data' => ['test' => true]
        ]);

        // Test specific log methods
        AuditLogger::logLogin(123, 'admin', true);
        AuditLogger::logAdminAction('test', 'Test admin action description');
        AuditLogger::logSecurityEvent('test_event', 'Test security event');

        // Check if audit log file was created
        $auditFile = '../../logs/audit.log';
        if (!file_exists($auditFile)) {
            return ['success' => false, 'message' => 'Audit log file was not created'];
        }

        $auditContent = file_get_contents($auditFile);
        if (strpos($auditContent, 'Test admin action') === false) {
            return ['success' => false, 'message' => 'Test audit message not found in log'];
        }

        return [
            'success' => true,
            'message' => 'AuditLogger class working correctly (file logging confirmed)',
            'details' => 'Audit log file size: ' . filesize($auditFile) . ' bytes'
        ];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'AuditLogger test failed: ' . $e->getMessage()];
    } finally {
        // Restore error reporting
        error_reporting($old_error_reporting);
    }
});

// Test 5: Integration Test
runTest("Integration Test", function() {
    // Test all components working together
    
    // 1. Start session
    SessionManager::login(999, 'admin', ['name' => 'Test Admin']);
    
    // 2. Validate some input
    $data = ['email' => '<EMAIL>', 'amount' => '250.00'];
    $rules = ['email' => 'required|email', 'amount' => 'required|numeric'];
    $validated = InputValidator::validate($data, $rules);
    
    if ($validated === false) {
        return ['success' => false, 'message' => 'Integration validation failed'];
    }
    
    // 3. Log an audit event
    AuditLogger::logFinancialAction('credit', 'Test credit transaction', 999, 250.00, 'USD', [
        'transaction_id' => 'TEST123'
    ]);
    
    // 4. Log an error
    ErrorHandler::logError('Integration test completed', [
        'user_id' => SessionManager::getUserId(),
        'validated_data' => $validated
    ], 'INFO');
    
    // 5. Check session info
    $sessionInfo = SessionManager::getSessionInfo();
    
    return [
        'success' => true, 
        'message' => 'All components integrated successfully',
        'details' => 'Session user: ' . $sessionInfo['user_id'] . ', Type: ' . $sessionInfo['user_type']
    ];
});

// Test 6: File Structure Test
runTest("File Structure", function() {
    $required_files = [
        '../../config/ErrorHandler.php',
        '../../config/InputValidator.php',
        '../../config/SessionManager.php',
        '../../config/AuditLogger.php'
    ];
    
    $required_dirs = [
        '../../logs'
    ];
    
    $missing_files = [];
    $missing_dirs = [];
    
    foreach ($required_files as $file) {
        if (!file_exists($file)) {
            $missing_files[] = $file;
        }
    }
    
    foreach ($required_dirs as $dir) {
        if (!is_dir($dir)) {
            $missing_dirs[] = $dir;
        }
    }
    
    if (!empty($missing_files) || !empty($missing_dirs)) {
        return [
            'success' => false, 
            'message' => 'Missing files/directories',
            'details' => 'Missing files: ' . implode(', ', $missing_files) . 
                        '\nMissing dirs: ' . implode(', ', $missing_dirs)
        ];
    }
    
    return [
        'success' => true, 
        'message' => 'All required files and directories exist'
    ];
});

// Display summary
echo "<div class='test-section info'>\n";
echo "<h2>📊 Test Summary</h2>\n";
echo "<p><strong>Total Tests:</strong> {$total_tests}</p>\n";
echo "<p><strong>Passed:</strong> {$passed_tests}</p>\n";
echo "<p><strong>Failed:</strong> " . ($total_tests - $passed_tests) . "</p>\n";
echo "<p><strong>Success Rate:</strong> " . round(($passed_tests / $total_tests) * 100, 2) . "%</p>\n";

if ($passed_tests === $total_tests) {
    echo "<div class='success'><strong>🎉 ALL TESTS PASSED!</strong> Phase 2 security components are working correctly.</div>\n";
} else {
    echo "<div class='warning'><strong>⚠️ SOME TESTS FAILED</strong> Please review the failed tests above.</div>\n";
}

echo "</div>\n";

// Display detailed results
echo "<div class='test-section'>\n";
echo "<h3>📋 Detailed Results</h3>\n";
echo "<pre>\n";
foreach ($test_results as $test => $result) {
    $icon = $result === 'PASSED' ? '✅' : ($result === 'FAILED' ? '❌' : '⚠️');
    echo "{$icon} {$test}: {$result}\n";
}
echo "</pre>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h3>🔧 Next Steps</h3>\n";
echo "<div class='info'>\n";
echo "<h4>Phase 2 Implementation Status:</h4>\n";
echo "<ul>\n";
echo "<li>✅ <strong>Task 2.1.1</strong>: ErrorHandler class created and tested</li>\n";
echo "<li>✅ <strong>Task 2.2.3</strong>: InputValidator class created and tested</li>\n";
echo "<li>✅ <strong>Task 2.3.1</strong>: SessionManager class created and tested</li>\n";
echo "<li>✅ <strong>Task 2.3.4</strong>: AuditLogger class created and tested</li>\n";
echo "<li>⏳ <strong>Next</strong>: Replace error_log() calls throughout codebase</li>\n";
echo "<li>⏳ <strong>Next</strong>: Audit and fix SQL injection vulnerabilities</li>\n";
echo "<li>⏳ <strong>Next</strong>: Implement CSRF protection in forms</li>\n";
echo "</ul>\n";
echo "</div>\n";
echo "</div>\n";

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";
?>
