-- Create super admin settings table for centralized configuration
-- This table stores critical system settings that only super admins can modify

CREATE TABLE IF NOT EXISTS `super_admin_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL UNIQUE,
  `setting_value` text NOT NULL,
  `setting_description` varchar(255) DEFAULT NULL,
  `setting_type` enum('text','email','phone','url','password','number','boolean') DEFAULT 'text',
  `is_encrypted` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON>EY `idx_setting_key` (`setting_key`),
  <PERSON>EY `fk_created_by` (`created_by`),
  <PERSON><PERSON>Y `fk_updated_by` (`updated_by`),
  FOREIGN KEY (`created_by`) REFERENCES `accounts` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`updated_by`) REFERENCES `accounts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default super admin settings
INSERT INTO `super_admin_settings` (`setting_key`, `setting_value`, `setting_description`, `setting_type`) VALUES
('site_name', 'Online Banking System', 'The name of the banking platform displayed in emails and throughout the system', 'text'),
('site_url', 'http://localhost/online_banking', 'The base URL of the banking platform', 'url'),
('support_email', '<EMAIL>', 'Primary support email address for customer inquiries', 'email'),
('support_phone', '1-800-BANKING', 'Primary support phone number for customer service', 'phone'),
('security_email', '<EMAIL>', 'Security team email for fraud and security issues', 'email'),
('security_phone', '1-800-SECURITY', 'Security team phone number for urgent security matters', 'phone'),
('noreply_email', '<EMAIL>', 'No-reply email address for automated system emails', 'email'),
('admin_email', '<EMAIL>', 'Administrator email for system notifications', 'email'),
('company_address', '123 Banking Street, Financial District, NY 10001', 'Physical address of the banking institution', 'text'),
('company_phone', '1-800-MAIN-BANK', 'Main company phone number', 'phone'),
('smtp_host', 'smtp.hostinger.com', 'SMTP server hostname for email delivery', 'text'),
('smtp_port', '465', 'SMTP server port (usually 587 for TLS or 465 for SSL)', 'number'),
('smtp_username', '<EMAIL>', 'SMTP authentication username', 'email'),
('smtp_password', 'Money2025@Demo#', 'SMTP authentication password', 'password'),
('smtp_encryption', 'ssl', 'SMTP encryption method (tls, ssl, or none)', 'text'),
('smtp_from_email', '<EMAIL>', 'Default from email address for outgoing emails', 'email'),
('smtp_from_name', 'Online Banking System', 'Default from name for outgoing emails', 'text'),
('email_footer_text', 'Your trusted financial partner since 2024', 'Footer text displayed in all emails', 'text'),
('privacy_policy_url', 'http://localhost/online_banking/privacy-policy.php', 'URL to privacy policy page', 'url'),
('terms_of_service_url', 'http://localhost/online_banking/terms-of-service.php', 'URL to terms of service page', 'url'),
('help_center_url', 'http://localhost/online_banking/help-center.php', 'URL to help center or FAQ page', 'url'),
('social_media_facebook', '', 'Facebook page URL (optional)', 'url'),
('social_media_twitter', '', 'Twitter profile URL (optional)', 'url'),
('social_media_linkedin', '', 'LinkedIn company page URL (optional)', 'url'),
('maintenance_mode', '0', 'Enable/disable maintenance mode (1 = enabled, 0 = disabled)', 'boolean'),
('maintenance_message', 'We are currently performing scheduled maintenance. Please check back soon.', 'Message displayed during maintenance mode', 'text'),
('max_login_attempts', '5', 'Maximum number of failed login attempts before account lockout', 'number'),
('session_timeout', '30', 'Session timeout in minutes for inactive users', 'number'),
('password_min_length', '8', 'Minimum password length requirement', 'number'),
('otp_expiry_minutes', '10', 'OTP code expiry time in minutes', 'number'),
('transaction_limit_daily', '10000.00', 'Default daily transaction limit for new accounts', 'number'),
('currency_default', 'USD', 'Default currency for new accounts', 'text'),
('timezone_default', 'America/New_York', 'Default timezone for the system', 'text');

-- Create audit log for super admin settings changes
CREATE TABLE IF NOT EXISTS `super_admin_settings_audit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `old_value` text,
  `new_value` text NOT NULL,
  `changed_by` int(11) NOT NULL,
  `change_reason` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_setting_key` (`setting_key`),
  KEY `idx_changed_by` (`changed_by`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`changed_by`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add super admin role if it doesn't exist
INSERT IGNORE INTO `user_roles` (`role_name`, `role_description`, `permissions`) VALUES
('super_admin', 'Super Administrator', 'ALL_PERMISSIONS,SYSTEM_SETTINGS,SMTP_CONFIG,USER_MANAGEMENT,FINANCIAL_OVERSIGHT');

-- Update accounts table to support super admin role (if column doesn't exist)
ALTER TABLE `accounts` 
ADD COLUMN IF NOT EXISTS `is_super_admin` tinyint(1) DEFAULT 0 AFTER `is_admin`,
ADD COLUMN IF NOT EXISTS `role` enum('user','admin','super_admin') DEFAULT 'user' AFTER `is_super_admin`;

-- Set existing admin users as super admins (you can modify this as needed)
UPDATE `accounts` SET `is_super_admin` = 1, `role` = 'super_admin' WHERE `is_admin` = 1;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS `idx_role` ON `accounts` (`role`);
CREATE INDEX IF NOT EXISTS `idx_is_super_admin` ON `accounts` (`is_super_admin`);
