<?php
require_once '../config/config.php';
requireAdmin();

if (empty($_GET['id'])) {
    redirect('users.php');
}

$user_id = intval($_GET['id']);

try {
    $db = getDB();
    
    // Get user details
    $sql = "SELECT * FROM accounts WHERE id = ? AND status = 'pending'";
    $result = $db->query($sql, [$user_id]);
    
    if ($result && $result->num_rows === 1) {
        $user = $result->fetch_assoc();
        
        // Update user status to active
        $update_sql = "UPDATE accounts SET status = 'active' WHERE id = ?";
        $db->query($update_sql, [$user_id]);
        
        // Prepare user data for welcome email
        $user_data = [
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'username' => $user['username'],
            'email' => $user['email'],
            'account_number' => $user['account_number'],
            'account_type' => $user['account_type'],
            'currency' => $user['currency'],
            'balance' => $user['balance'],
            'status' => 'active'
        ];
        
        // Send welcome email
        $emailSent = sendWelcomeEmail($user['email'], $user_data);
        
        // Log activity
        logActivity($_SESSION['user_id'], 'Approved user account', 'accounts', $user_id, null, [
            'username' => $user['username'],
            'email' => $user['email'],
            'account_number' => $user['account_number']
        ]);
        
        // Set success message
        $_SESSION['success'] = "User account approved successfully! " . 
            ($emailSent ? "Welcome email sent." : "But welcome email could not be sent.");
    } else {
        $_SESSION['error'] = 'User not found or not pending approval.';
    }
} catch (Exception $e) {
    error_log("Approve user error: " . $e->getMessage());
    $_SESSION['error'] = 'An error occurred. Please try again.';
}

redirect('users.php');
