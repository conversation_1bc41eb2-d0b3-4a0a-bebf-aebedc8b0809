<?php
require_once '../config/config.php';
require_once '../config/super_admin_settings.php';

// Require super admin access
requireSuperAdmin();

$page_title = 'SMTP Configuration';
$success = '';
$errors = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $smtp_settings = [
            'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password',
            'smtp_encryption', 'smtp_from_email', 'smtp_from_name'
        ];
        
        $updated_count = 0;
        foreach ($smtp_settings as $key) {
            if (isset($_POST[$key])) {
                $value = $_POST[$key];
                
                // Validate setting
                $validation_errors = validateSuperAdminSetting($key, $value);
                if (!empty($validation_errors)) {
                    $errors = array_merge($errors, $validation_errors);
                    continue;
                }
                
                updateSuperAdminSetting($key, $value, $_POST['change_reason'] ?? 'SMTP configuration update');
                $updated_count++;
            }
        }
        
        if (empty($errors)) {
            $success = "Successfully updated SMTP configuration ($updated_count settings).";
        }
        
    } catch (Exception $e) {
        $errors[] = "Error updating SMTP settings: " . $e->getMessage();
    }
}

// Test email functionality
if (isset($_POST['test_email'])) {
    try {
        require_once '../config/email.php';
        require_once '../config/email_templates.php';
        
        $test_email = $_POST['test_email_address'] ?? '<EMAIL>';
        
        // Create test user data
        $test_user_data = [
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => $test_email
        ];
        
        // Generate test OTP email
        $otp_code = generateOTP();
        $test_html = generateOTPEmailTemplate($test_user_data, $otp_code, 10);
        $subject = "SMTP Test - Verification Code";
        
        $result = sendEmailSMTP($test_email, $subject, $test_html, true);
        
        if ($result) {
            $success = "Test email sent successfully to $test_email! OTP Code: $otp_code";
        } else {
            $errors[] = "Failed to send test email. Please check your SMTP configuration.";
        }
        
    } catch (Exception $e) {
        $errors[] = "Test email error: " . $e->getMessage();
    }
}

// Get current SMTP settings
$current_settings = getSMTPConfig();

include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-envelope-open"></i> SMTP Configuration
                    </h3>
                    <p class="mb-0 mt-2">
                        <i class="fas fa-shield-alt"></i> 
                        <strong>Super Admin Only:</strong> Configure email delivery settings for the entire banking system.
                    </p>
                </div>
                
                <div class="card-body">
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> 
                            <strong>Errors:</strong>
                            <ul class="mb-0 mt-2">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <!-- SMTP Configuration Form -->
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-server"></i> SMTP Server Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="smtp_host">SMTP Host</label>
                                            <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                                   value="<?php echo htmlspecialchars($current_settings['host'] ?? ''); ?>" required>
                                            <small class="form-text text-muted">e.g., smtp.gmail.com, smtp.hostinger.com</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="smtp_port">SMTP Port</label>
                                            <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                                   value="<?php echo htmlspecialchars($current_settings['port'] ?? ''); ?>" required min="1" max="65535">
                                            <small class="form-text text-muted">587 (TLS) or 465 (SSL)</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="smtp_username">SMTP Username</label>
                                            <input type="email" class="form-control" id="smtp_username" name="smtp_username" 
                                                   value="<?php echo htmlspecialchars($current_settings['username'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="smtp_password">SMTP Password</label>
                                            <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                                   value="<?php echo htmlspecialchars($current_settings['password'] ?? ''); ?>" required>
                                            <small class="form-text text-muted">Password will be encrypted when stored</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="smtp_encryption">Encryption Method</label>
                                    <select class="form-control" id="smtp_encryption" name="smtp_encryption" required>
                                        <option value="tls" <?php echo ($current_settings['encryption'] ?? '') === 'tls' ? 'selected' : ''; ?>>TLS (Port 587)</option>
                                        <option value="ssl" <?php echo ($current_settings['encryption'] ?? '') === 'ssl' ? 'selected' : ''; ?>>SSL (Port 465)</option>
                                        <option value="none" <?php echo ($current_settings['encryption'] ?? '') === 'none' ? 'selected' : ''; ?>>None (Not Recommended)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- From Address Settings -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-paper-plane"></i> From Address Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="smtp_from_email">From Email Address</label>
                                            <input type="email" class="form-control" id="smtp_from_email" name="smtp_from_email" 
                                                   value="<?php echo htmlspecialchars($current_settings['from_email'] ?? ''); ?>" required>
                                            <small class="form-text text-muted">Email address that appears as sender</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="smtp_from_name">From Name</label>
                                            <input type="text" class="form-control" id="smtp_from_name" name="smtp_from_name" 
                                                   value="<?php echo htmlspecialchars($current_settings['from_name'] ?? ''); ?>" required>
                                            <small class="form-text text-muted">Name that appears as sender</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Change Reason -->
                        <div class="card mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0"><i class="fas fa-edit"></i> Change Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="change_reason">Reason for Changes (Optional)</label>
                                    <input type="text" class="form-control" id="change_reason" name="change_reason" 
                                           placeholder="e.g., Updated SMTP provider, Security enhancement, etc.">
                                    <small class="form-text text-muted">This will be logged for audit purposes</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> Update SMTP Settings
                            </button>
                        </div>
                    </form>
                    
                    <!-- Test Email Section -->
                    <hr class="my-5">
                    
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-paper-plane"></i> Test Email Configuration</h5>
                        </div>
                        <div class="card-body">
                            <p>Send a test email to verify your SMTP configuration is working correctly.</p>
                            
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="test_email_address">Test Email Address</label>
                                            <input type="email" class="form-control" id="test_email_address" name="test_email_address" 
                                                   value="<EMAIL>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <button type="submit" name="test_email" class="btn btn-success btn-block">
                                                <i class="fas fa-paper-plane"></i> Send Test Email
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="super-admin-settings.php" class="btn btn-secondary btn-lg">
                            <i class="fas fa-cogs"></i> Back to Super Admin Settings
                        </a>
                        <a href="dashboard.php" class="btn btn-outline-secondary btn-lg ml-3">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Auto-update port based on encryption selection
document.getElementById('smtp_encryption').addEventListener('change', function() {
    const portField = document.getElementById('smtp_port');
    if (this.value === 'tls') {
        portField.value = '587';
    } else if (this.value === 'ssl') {
        portField.value = '465';
    }
});
</script>

<?php include '../includes/admin_footer.php'; ?>
