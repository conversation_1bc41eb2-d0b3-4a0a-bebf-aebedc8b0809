<?php
/**
 * Site Settings Controller
 * Manage site logo, favicon, and other visual settings
 */

$page_title = 'Site Settings';
$page_subtitle = 'Manage site appearance and branding';

// Include header
include 'includes/header.php';

// Handle form submission
if ($_POST && isset($_POST['csrf_token']) && verifyCSRFToken($_POST['csrf_token'])) {
    try {
        require_once '../config/database.php';
        $db = getDB();

        $site_name = trim($_POST['site_name']);
        $site_url = trim($_POST['site_url']);
        $site_logo = $_FILES['site_logo'] ?? null;
        $favicon = $_FILES['favicon'] ?? null;
        $logo_path = '';
        $favicon_path = '';

        // Handle logo upload
        if ($site_logo && $site_logo['error'] === UPLOAD_ERR_OK) {
            $logo_dir = '../assets/img/';
            if (!is_dir($logo_dir)) {
                mkdir($logo_dir, 0777, true);
            }
            
            $logo_ext = pathinfo($site_logo['name'], PATHINFO_EXTENSION);
            $logo_name = 'logo_' . time() . '.' . $logo_ext;
            $logo_path = $logo_dir . $logo_name;
            
            if (move_uploaded_file($site_logo['tmp_name'], $logo_path)) {
                $logo_path = 'assets/img/' . $logo_name;
            } else {
                throw new Exception("Failed to upload site logo");
            }
        }

        // Handle favicon upload
        if ($favicon && $favicon['error'] === UPLOAD_ERR_OK) {
            $favicon_dir = '../assets/img/';
            if (!is_dir($favicon_dir)) {
                mkdir($favicon_dir, 0777, true);
            }
            
            $favicon_ext = pathinfo($favicon['name'], PATHINFO_EXTENSION);
            $favicon_name = 'favicon_' . time() . '.' . $favicon_ext;
            $favicon_path = $favicon_dir . $favicon_name;
            
            if (move_uploaded_file($favicon['tmp_name'], $favicon_path)) {
                $favicon_path = 'assets/img/' . $favicon_name;
            } else {
                throw new Exception("Failed to upload favicon");
            }
        }

        // Update settings in database
        $settings = [
            'site_name' => $site_name,
            'site_url' => $site_url
        ];

        if ($logo_path) {
            $settings['site_logo'] = $logo_path;
        }

        if ($favicon_path) {
            $settings['site_favicon'] = $favicon_path;
        }

        foreach ($settings as $key => $value) {
            $db->query("INSERT INTO super_admin_settings (setting_key, setting_value) 
                        VALUES (?, ?)
                        ON DUPLICATE KEY UPDATE setting_value = ?", 
                        [$key, $value, $value]);
        }

        $success_message = "Site settings updated successfully!";
        logSuperAdminAction('site_settings_updated', 'Updated site settings', $settings);

    } catch (Exception $e) {
        $error_message = "Failed to update settings: " . $e->getMessage();
        error_log($error_message);
    }
}

// Get current settings
$current_settings = [];
try {
    require_once '../config/database.php';
    $db = getDB();
    
    $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings 
                          WHERE setting_key IN ('site_name', 'site_url', 'site_logo', 'site_favicon')");
    
    while ($row = $result->fetch_assoc()) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    error_log("Failed to load site settings: " . $e->getMessage());
}

// Set defaults
$site_name = $current_settings['site_name'] ?? 'Online Banking System';
$site_url = $current_settings['site_url'] ?? 'http://localhost/online_banking';
$site_logo_path = $current_settings['site_logo'] ?? 'assets/img/logo.png';
$favicon_path = $current_settings['site_favicon'] ?? 'assets/img/favicon.ico';

// Log page access
logSuperAdminAction('site_settings_access', 'Super admin accessed site settings');
?>

<!-- Success/Error Messages -->
<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade极端的 show" role="alert">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="极端的btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-0 py-3">
        <h5 class="card-title mb-0">
            <i class="fas fa-cog text-primary"></i> Site Appearance Settings
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_name" class="form-label">Site Name</label>
                        <input type="text" class="form-control" id="site_name" name="site_name" 
                               value="<?php echo htmlspecialchars($site_name); ?>" required>
                        <div class="form-text">The name displayed in the browser tab and throughout the site</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="site_url" class="form-label">Site URL</label>
                        <input type="url" class="form-control" id="site_url" name="site_url" 
                               value="<?php echo htmlspecialchars($site_url); ?>" required>
                        <div class="form-text">Base URL of your website</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="text-center mb-3">
                        <?php if ($site_logo_path && file_exists('../' . $site_logo_path)): ?>
                            <img src="<?php echo htmlspecialchars($site_url . '/' . $site_logo_path); ?>" 
                                 alt="Site Logo" class="img-fluid mb-2" style="max-height: 100px;">
                        <?php else: ?>
                            <div class="bg-light border rounded p-5 text-center text-muted mb-2">
                                <i class="fas fa-image fa-2x mb-2"></i>
                                <p class="mb-0">No logo uploaded</p>
                            </div>
                        <?php endif; ?>
                        
                        <label for="site_logo" class="form-label">Site Logo</label>
                        <input type="file" class="form-control" id="site_logo" name="site_logo" accept="image/*">
                        <div class="form-text">Recommended size: 200x50 pixels, PNG format</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="text-center">
                        <?php if ($favicon_path && file_exists('../' . $favicon_path)): ?>
                            <img src="<?php echo htmlspecialchars($site_url . '/' . $favicon_path); ?>" 
                                 alt="Favicon" class="img-fluid mb-2" style="max-height: 32px;">
                        <?php else: ?>
                            <div class="bg-light border rounded p-4 text-center text-muted mb-2">
                                <i class="fas fa-icons fa-2x mb-2"></i>
                                <p class="mb-0">No favicon uploaded</p>
                            </div>
                        <?php endif; ?>
                        
                        <label for="favicon" class="form-label">Favicon</label>
                        <input type="file" class="form-control" id="favicon" name="favicon" accept="image/*">
                        <div class="form-text">Recommended size: 32x32 pixels, ICO or PNG format</div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
