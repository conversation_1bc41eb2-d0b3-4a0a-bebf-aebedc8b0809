<?php
require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        redirect('admin/');
    } else {
        redirect('dashboard/');
    }
}

$error = '';
$username = '';

// Get site settings for logo, name and favicon
function getSiteSettings() {
    try {
        require_once 'config/database.php';
        $db = getDB();

        $settings = [];
        $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key IN ('site_name', 'site_logo', 'site_favicon')");

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }

        return [
            'site_name' => $settings['site_name'] ?? 'SecureBank Online Banking',
            'site_logo' => $settings['site_logo'] ?? '',
            'site_favicon' => $settings['site_favicon'] ?? ''
        ];
    } catch (Exception $e) {
        error_log("Error getting site settings: " . $e->getMessage());
        return [
            'site_name' => 'SecureBank Online Banking',
            'site_logo' => '',
            'site_favicon' => ''
        ];
    }
}

$site_settings = getSiteSettings();

// Function to determine login method based on input format
function determineLoginMethod($identifier) {
    // Check if it's an email
    if (filter_var($identifier, FILTER_VALIDATE_EMAIL)) {
        return 'email';
    }

    // Check if it's an account number (typically 10-12 digits)
    if (preg_match('/^\d{10,12}$/', $identifier)) {
        return 'account_number';
    }

    // Default to username
    return 'username';
}

// Function to validate login identifier format
function validateLoginIdentifier($identifier, $method) {
    switch ($method) {
        case 'email':
            return filter_var($identifier, FILTER_VALIDATE_EMAIL) !== false;
        case 'account_number':
            return preg_match('/^\d{10,12}$/', $identifier) === 1;
        case 'username':
            return preg_match('/^[a-zA-Z0-9_]{3,30}$/', $identifier) === 1;
        default:
            return false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $login_identifier = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

    // Check for too many failed attempts using dynamic security settings
    $security_settings = getSecuritySettings();
    $max_attempts = $security_settings['login_attempts_limit'] ?? 5;
    $failed_attempts = getFailedLoginAttempts($login_identifier, $ip_address);

    if ($failed_attempts >= $max_attempts) {
        $lockout_duration = $security_settings['lockout_duration'] ?? 30;
        $error = "Too many failed login attempts. Please try again after {$lockout_duration} minutes.";
    } else {
        if (empty($login_identifier) || empty($password)) {
            $error = 'Please enter both login credentials and password.';
        } else {
            // Validate login identifier format
            $login_method = determineLoginMethod($login_identifier);
            if (!validateLoginIdentifier($login_identifier, $login_method)) {
                switch ($login_method) {
                    case 'email':
                        $error = 'Please enter a valid email address.';
                        break;
                    case 'account_number':
                        $error = 'Please enter a valid account number (10-12 digits).';
                        break;
                    case 'username':
                        $error = 'Please enter a valid username (3-30 characters, letters, numbers, and underscores only).';
                        break;
                    default:
                        $error = 'Please enter valid login credentials.';
                        break;
                }
            } else {
                try {
                    $db = getDB();

                    // Determine login method and build appropriate SQL query
                    $login_method = determineLoginMethod($login_identifier);
                    $sql = "SELECT id, username, password, first_name, last_name, email, account_number,
                                  balance, status, is_admin, kyc_status
                            FROM accounts
                            WHERE ";

                    switch ($login_method) {
                        case 'email':
                            $sql .= "email = ?";
                            break;
                        case 'account_number':
                            $sql .= "account_number = ?";
                            break;
                        case 'username':
                        default:
                            $sql .= "username = ?";
                            break;
                    }

                    $result = $db->query($sql, [$login_identifier]);
                
                if ($result && $result->num_rows === 1) {
                    $user = $result->fetch_assoc();
                    
                    if (verifyPassword($password, $user['password'])) {
                        // Check account status
                        if ($user['status'] === 'pending') {
                            $error = 'Your account is pending admin approval. Please wait for activation.';
                        } elseif (!isValidEmail($user['email'])) {
                            $error = 'Your account email address is invalid. Please contact support to update your email address before logging in.';
                        } else {
                            // Check if OTP is enabled for this user
                            $otp_check_sql = "SELECT otp_enabled FROM user_security_settings WHERE user_id = ?";
                            $otp_result = $db->query($otp_check_sql, [$user['id']]);
                            $otp_enabled = true; // Default to enabled for security

                            if ($otp_result && $otp_result->num_rows === 1) {
                                $otp_settings = $otp_result->fetch_assoc();
                                $otp_enabled = ($otp_settings['otp_enabled'] == 1);
                            }

                            if ($otp_enabled) {
                                // Generate and send OTP
                                $otp = generateOTP();
                                $user_name = $user['first_name'] . ' ' . $user['last_name'];

                                // Store OTP first
                                if (storeOTP($user['id'], $otp)) {
                                    // Try to send email
                                    $emailSent = sendOTPEmail($user['email'], $otp, $user_name);

                                    if ($emailSent) {
                                        // Set OTP session variables
                                        $_SESSION['otp_user_id'] = $user['id'];
                                        $_SESSION['otp_pending'] = true;
                                        $_SESSION['otp_email'] = $user['email'];

                                        // Record successful login attempt
                                        recordLoginAttempt($username, true);

                                        // Log activity
                                        logActivity($user['id'], 'User credentials verified, OTP sent to email');

                                        // Redirect to OTP verification
                                        redirect('auth/verify-otp.php');
                                    } else {
                                        // Email failed but OTP is stored - still allow verification
                                        $_SESSION['otp_user_id'] = $user['id'];
                                        $_SESSION['otp_pending'] = true;
                                        $_SESSION['otp_email'] = $user['email'];
                                        $_SESSION['email_failed'] = true;

                                        // Record login attempt
                                        recordLoginAttempt($username, true);

                                        // Log activity
                                        logActivity($user['id'], 'User credentials verified, OTP generated but email failed');

                                        // Redirect to OTP verification with email warning
                                        redirect('auth/verify-otp.php?email_failed=1');
                                    }
                                } else {
                                    $error = 'Failed to generate verification code. Please try again.';
                                }
                            } else {
                                // OTP is disabled, log user in directly
                                $_SESSION['user_id'] = $user['id'];
                                $_SESSION['username'] = $user['username'];
                                $_SESSION['user_logged_in'] = true;
                                $_SESSION['login_time'] = time();

                                // Record successful login
                                recordLoginAttempt($login_identifier, true);

                                // Log activity
                                logActivity($user['id'], 'User logged in successfully (OTP disabled)');

                                // Redirect to user dashboard
                                redirect('dashboard/');
                            }
                        }
                    } else {
                        $error = 'Invalid login credentials.';
                        recordLoginAttempt($login_identifier, false);
                    }
                } else {
                    $error = 'Invalid login credentials.';
                    recordLoginAttempt($login_identifier, false);
                }
                } catch (Exception $e) {
                    error_log("Login error: " . $e->getMessage());
                    $error = 'An error occurred. Please try again.';
                }
            }
        }
    }
}

$page_title = 'User Login';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . htmlspecialchars($site_settings['site_name']); ?></title>

    <!-- Favicon -->
    <?php if (!empty($site_settings['site_favicon']) && file_exists($site_settings['site_favicon'])): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($site_settings['site_favicon']); ?>">
    <?php else: ?>
        <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico">
    <?php endif; ?>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            min-height: 100vh;
            padding: 0;
        }

        .row {
            min-height: 100vh;
            margin: 0;
        }

        /* Left Panel - User Login Form */
        .left-panel {
            background: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding: 60px 80px;
            position: relative;
        }

        .back-link {
            position: absolute;
            top: 30px;
            left: 30px;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: color 0.2s ease;
        }

        .back-link:hover {
            color: #4f46e5;
        }

        .logo {
            margin-bottom: 40px;
            text-align: left;
            width: 100%;
        }

        .logo img {
            max-width: 200px;
            max-height: 80px;
            width: auto;
            height: auto;
        }

        .logo-fallback {
            font-size: 24px;
            font-weight: 700;
            color: #4f46e5;
            margin: 0;
        }

        .welcome-text {
            text-align: left;
            margin-bottom: 40px;
            width: 100%;
        }

        .welcome-text h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 10px;
        }

        .welcome-text p {
            color: #6b7280;
            font-size: 16px;
            margin: 0;
        }

        .login-form {
            width: 100%;
            max-width: 400px;
            text-align: left;
        }

        .form-group {
            margin-bottom: 20px;
            width: 100%;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-control {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: white;
            width: 100%;
            box-sizing: border-box;
        }

        .form-control:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            outline: none;
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 16px;
        }

        .input-group .form-control {
            padding-left: 40px;
        }

        .forgot-password {
            text-align: right;
            margin-top: 8px;
        }

        .forgot-password a {
            color: #4f46e5;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .btn-login {
            background: #4f46e5;
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 16px;
            width: 100%;
            transition: all 0.2s ease;
            cursor: pointer;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-login:hover {
            background: #4338ca;
            transform: translateY(-1px);
        }

        .signup-link {
            text-align: center;
            margin-top: 20px;
            color: #6b7280;
            font-size: 14px;
        }

        .signup-link a {
            color: #4f46e5;
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        .alert {
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 12px 16px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .alert-danger {
            background: #fef2f2;
            color: #dc2626;
            border-color: #fecaca;
        }

        .alert-warning {
            background: #fffbeb;
            color: #d97706;
            border-color: #fed7aa;
        }

        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border-color: #bbf7d0;
        }

        /* Right Panel - User Banking Visual */
        .right-panel {
            background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 50%, #2563eb 100%);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .right-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.2;
            z-index: 2;
        }

        .right-panel-content {
            display: flex;
            flex-direction: column;
            height: 100%;
            z-index: 3;
            position: relative;
            padding: 40px;
        }

        .feature-illustration {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
        }

        .bottom-text {
            flex: 0 0 auto;
            text-align: center;
            color: white;
        }

        .bottom-text h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            opacity: 0.95;
        }

        .bottom-text p {
            font-size: 14px;
            opacity: 0.8;
            margin: 0;
            line-height: 1.5;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .left-panel {
                padding: 40px 30px;
            }

            .right-panel {
                display: none;
            }

            .welcome-text h1 {
                font-size: 24px;
            }

            .logo img {
                max-width: 150px;
                max-height: 60px;
            }
        }

        @media (max-width: 576px) {
            .left-panel {
                padding: 30px 20px;
            }

            .welcome-text h1 {
                font-size: 22px;
            }
        }

        .header-logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .header-logo img {
            max-height: 40px;
            width: auto;
        }

        .header-logo h2 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0;
        }

        .header-nav {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: #6b7280;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .nav-link:hover {
            color: #4f46e5;
        }

        .header-cta {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-outline-primary {
            border: 1px solid #4f46e5;
            color: #4f46e5;
            background: transparent;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-outline-primary:hover {
            background: #4f46e5;
            color: white;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-1px);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding-top: 80px;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-text {
            color: white;
        }

        .hero-text h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
        }

        .hero-text p {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .hero-features {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .hero-feature {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            opacity: 0.9;
        }

        .hero-feature i {
            color: #10b981;
            font-size: 1.1rem;
        }

        .hero-cta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-hero-primary {
            background: white;
            color: #4f46e5;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-hero-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: #4f46e5;
        }

        .btn-hero-secondary {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-hero-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
        }

        /* Login Form Section */
        .login-section {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 480px;
            width: 100%;
        }

        .logo {
            margin-bottom: 40px;
            text-align: left;
            width: 100%;
        }

        .logo img {
            max-width: 200px;
            height: auto;
            max-height: 80px;
            object-fit: contain;
            background: transparent;
            mix-blend-mode: multiply;
        }

        .logo-fallback {
            font-size: 24px;
            font-weight: 700;
            color: #4361ee;
            margin: 0;
        }

        .welcome-text {
            text-align: left;
            margin-bottom: 40px;
            width: 100%;
        }

        .welcome-text h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 10px;
        }

        .welcome-text p {
            color: #6b7280;
            font-size: 16px;
            margin: 0;
        }

        .login-form {
            width: 100%;
            max-width: 400px;
            text-align: left;
        }

        .form-group {
            margin-bottom: 20px;
            width: 100%;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }

        .form-control {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 12px 16px 12px 45px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: white;
            width: 100%;
            box-sizing: border-box;
        }

        .form-control:focus {
            border-color: #4361ee;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
            outline: none;
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            z-index: 2;
        }

        .btn-login {
            background: #4361ee;
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 16px;
            width: 100%;
            transition: all 0.2s ease;
            cursor: pointer;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-login:hover {
            background: #3651d4;
            transform: translateY(-1px);
        }

        .alert {
            border-radius: 8px;
            border: none;
            padding: 12px 16px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background: #fef2f2;
            color: #dc2626;
            border-left: 4px solid #dc2626;
        }

        .right-panel {
            background: linear-gradient(135deg, #4361ee 0%, #3651d4 50%, #2d46c7 100%);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .right-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('demo-images/Image_fx (8).jpg'), url('demo-images/use_this.jpg');
            background-size: cover, cover;
            background-position: center, center;
            background-repeat: no-repeat, no-repeat;
            background-blend-mode: overlay;
            opacity: 0.08;
            z-index: 1;
        }

        .right-panel::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.2;
            z-index: 2;
        }

        .right-panel-content {
            display: flex;
            flex-direction: column;
            height: 100%;
            z-index: 3;
            position: relative;
            padding: 40px;
        }

        .feature-illustration {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
        }

        .bottom-text {
            flex: 0 0 auto;
            text-align: center;
            color: white;
        }

        .bottom-text h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            opacity: 0.95;
        }

        .bottom-text p {
            font-size: 14px;
            opacity: 0.8;
            margin: 0;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .left-panel {
                padding: 40px 30px;
            }

            .right-panel {
                display: none;
            }

            .welcome-text h1 {
                font-size: 24px;
            }

            .logo img {
                max-width: 150px;
                max-height: 60px;
            }
        }

        @media (max-width: 576px) {
            .left-panel {
                padding: 30px 20px;
            }

            .welcome-text h1 {
                font-size: 22px;
            }
        }

        .forgot-password {
            text-align: right;
            margin-top: 8px;
        }

        .forgot-password a {
            color: #4361ee;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .signup-link {
            text-align: center;
            margin-top: 30px;
            color: #6b7280;
        }

        .signup-link a {
            color: #4361ee;
            text-decoration: none;
            font-weight: 600;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }
        /* Features Section */
        .features-section {
            padding: 5rem 0;
            background: #f8fafc;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .section-header p {
            font-size: 1.25rem;
            color: #6b7280;
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: #6b7280;
            line-height: 1.6;
        }

        /* Security Section */
        .security-section {
            padding: 5rem 0;
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: white;
        }

        .security-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .security-text h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
        }

        .security-text p {
            font-size: 1.125rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .security-features {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .security-feature {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .security-feature i {
            color: #10b981;
            font-size: 1.25rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .security-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .header-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-5 left-panel">
                <div class="login-header">
                    <h2>Welcome Back</h2>
                    <p>Sign in to access your banking dashboard</p>
                </div>

                <div class="login-tabs">
                    <div class="login-tab active">Personal Banking</div>
                    <div class="login-tab">Business Banking</div>
                </div>

                <!-- Error Messages -->
                <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <?php if (isset($_GET['timeout'])): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-clock me-2"></i>
                    Your session has expired. Please log in again.
                </div>
                <?php endif; ?>

                <!-- Login Form -->
                <form method="post" action="" autocomplete="off" novalidate>
                    <div class="form-group">
                        <label class="form-label" for="username">Username, Email, or Account Number</label>
                        <div class="input-group">
                            <i class="fas fa-user input-icon"></i>
                            <input type="text"
                                   class="form-control"
                                   id="username"
                                   name="username"
                                   placeholder="Enter username, email, or account number"
                                   value="<?php echo htmlspecialchars($login_identifier ?? ''); ?>"
                                   required
                                   autocomplete="username">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="password">Password</label>
                        <div class="input-group">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   placeholder="Enter your password"
                                   required>
                        </div>
                        <div class="forgot-password">
                            <a href="forgot-password.php">Forgot Password?</a>
                        </div>
                    </div>

                    <button type="submit" class="btn-login">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In to Your Account
                    </button>
                </form>

                <div class="signup-link">
                    Don't have an account? <a href="register.php">Open Account</a>
                </div>
            </div>
            <!-- Right Panel - User Banking Visual -->
            <div class="col-md-7 right-panel">
                <div class="right-panel-content">
                    <!-- User Banking Illustration -->
                    <div class="feature-illustration">
                        <svg width="400" height="200" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.4" />
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
                                </linearGradient>
                                <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.5" />
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.2" />
                                </linearGradient>
                            </defs>

                            <!-- Banking Card -->
                            <rect x="50" y="60" width="300" height="80" rx="12" fill="url(#userGradient)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>

                            <!-- Card Chip -->
                            <rect x="80" y="85" width="25" height="20" rx="4" fill="rgba(255,255,255,0.6)"/>
                            <rect x="85" y="90" width="15" height="10" rx="2" fill="rgba(255,255,255,0.3)"/>

                            <!-- Card Number -->
                            <rect x="130" y="95" width="40" height="4" rx="2" fill="rgba(255,255,255,0.6)"/>
                            <rect x="180" y="95" width="40" height="4" rx="2" fill="rgba(255,255,255,0.6)"/>
                            <rect x="230" y="95" width="40" height="4" rx="2" fill="rgba(255,255,255,0.6)"/>
                            <rect x="280" y="95" width="40" height="4" rx="2" fill="rgba(255,255,255,0.6)"/>

                            <!-- Card Holder Name -->
                            <rect x="80" y="115" width="80" height="3" rx="1" fill="rgba(255,255,255,0.5)"/>
                            <rect x="80" y="122" width="60" height="3" rx="1" fill="rgba(255,255,255,0.4)"/>

                            <!-- Expiry Date -->
                            <rect x="280" y="115" width="30" height="3" rx="1" fill="rgba(255,255,255,0.5)"/>
                            <rect x="280" y="122" width="20" height="3" rx="1" fill="rgba(255,255,255,0.4)"/>

                            <!-- Mobile Banking -->
                            <rect x="120" y="20" width="60" height="100" rx="8" fill="url(#cardGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                            <rect x="130" y="30" width="40" height="25" rx="4" fill="rgba(255,255,255,0.4)"/>

                            <!-- App Icons -->
                            <circle cx="135" cy="70" r="6" fill="rgba(255,255,255,0.5)"/>
                            <circle cx="150" cy="70" r="6" fill="rgba(255,255,255,0.5)"/>
                            <circle cx="165" cy="70" r="6" fill="rgba(255,255,255,0.5)"/>

                            <circle cx="135" cy="85" r="6" fill="rgba(255,255,255,0.5)"/>
                            <circle cx="150" cy="85" r="6" fill="rgba(255,255,255,0.5)"/>
                            <circle cx="165" cy="85" r="6" fill="rgba(255,255,255,0.5)"/>

                            <!-- Security Shield -->
                            <path d="M220 20 L240 15 L260 20 L260 40 Q260 50 240 55 Q220 50 220 40 Z" fill="rgba(255,255,255,0.6)" stroke="rgba(255,255,255,0.8)" stroke-width="2"/>
                            <path d="M230 30 L238 35 L250 25" stroke="rgba(255,255,255,0.9)" stroke-width="2" fill="none"/>

                            <!-- Transaction Lines -->
                            <rect x="20" y="160" width="100" height="2" rx="1" fill="rgba(255,255,255,0.4)"/>
                            <rect x="20" y="170" width="80" height="2" rx="1" fill="rgba(255,255,255,0.3)"/>
                            <rect x="20" y="180" width="90" height="2" rx="1" fill="rgba(255,255,255,0.4)"/>

                            <rect x="280" y="160" width="100" height="2" rx="1" fill="rgba(255,255,255,0.4)"/>
                            <rect x="300" y="170" width="80" height="2" rx="1" fill="rgba(255,255,255,0.3)"/>
                            <rect x="290" y="180" width="90" height="2" rx="1" fill="rgba(255,255,255,0.4)"/>

                            <!-- Floating Elements -->
                            <circle cx="30" cy="30" r="3" fill="rgba(255,255,255,0.5)"/>
                            <circle cx="370" cy="25" r="2" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="20" cy="100" r="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="380" cy="120" r="3" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="15" cy="170" r="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="385" cy="180" r="2" fill="rgba(255,255,255,0.4)"/>

                            <!-- Connection Lines -->
                            <path d="M33 30 Q50 25 70 30" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
                            <path d="M370 28 Q350 35 330 30" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
                            <path d="M22 100 Q35 95 50 100" stroke="rgba(255,255,255,0.2)" stroke-width="1" fill="none"/>
                        </svg>
                    </div>

                    <!-- Bottom Text -->
                    <div class="bottom-text">
                        <h3>Your Digital Banking Experience</h3>
                        <p>Access your accounts, transfer funds, pay bills, and manage your finances securely from anywhere, anytime with our advanced banking platform.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation enhancement
        const form = document.querySelector('form');
        const inputs = form.querySelectorAll('input[required]');

        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.style.borderColor = '#ef4444';
                } else {
                    this.style.borderColor = '#4f46e5';
                }
            });
        });

        // Auto-focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('username');
            if (usernameField && !usernameField.value) {
                usernameField.focus();
            }
        });
    </script>
</body>
</html>
