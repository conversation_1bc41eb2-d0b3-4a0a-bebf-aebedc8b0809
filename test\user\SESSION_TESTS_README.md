# 🔐 Comprehensive User Session Test Suite

This directory contains a complete test suite for the online banking system's user session management functionality. The tests cover all aspects of session handling including security, performance, and integration with the banking application.

## 📋 Test Suite Overview

### 1. **Comprehensive Session Tests** (`comprehensive_session_test.php`)
- ✅ Session initialization and configuration
- ✅ User login and logout flows
- ✅ Session data management (set, get, remove)
- ✅ Session timeout functionality
- ✅ OTP session handling
- ✅ Session security measures
- ✅ Session cleanup and destruction

### 2. **Session Security Tests** (`session_security_test.php`)
- 🔒 Session configuration security
- 🔒 Session hijacking protection
- 🔒 CSRF token generation and validation
- 🔒 Session timeout security
- 🔒 Session data integrity
- 🔒 Concurrent session handling
- 🔒 Session cleanup security

### 3. **Session Performance Tests** (`session_performance_test.php`)
- ⚡ Session initialization performance
- ⚡ Session data operations benchmarks
- ⚡ Session validation performance
- ⚡ Memory usage analysis
- ⚡ Concurrent session simulation
- ⚡ Stress testing under load
- ⚡ Timeout calculation performance

### 4. **Session Integration Tests** (`session_integration_test.php`)
- 🔗 Database integration testing
- 🔗 User login flow with real data
- 🔗 Admin login flow testing
- 🔗 OTP integration with email system
- 🔗 Session timeout with database operations
- 🔗 Transaction operations with sessions

## 🚀 Quick Start

### Run All Tests
```bash
# Navigate to the test directory
cd test/user/

# Run the complete test suite
php run_all_session_tests.php
```

### Run Individual Test Suites
```bash
# Core functionality tests
php comprehensive_session_test.php

# Security-focused tests
php session_security_test.php

# Performance and stress tests
php session_performance_test.php

# Integration with banking system
php session_integration_test.php
```

## 📊 Test Results

The test suite provides comprehensive reporting including:

- **Pass/Fail Status**: Clear indication of test results
- **Performance Metrics**: Timing and memory usage data
- **Security Analysis**: Security vulnerability assessments
- **Integration Status**: Database and system integration health
- **Detailed Logs**: Comprehensive test execution details

### Sample Output
```
✅ [PASS] Session manager singleton created
✅ [PASS] Session started successfully
✅ [PASS] User login successful
⚠️ [WARNING] Session cookie secure setting (Should be true for HTTPS)
❌ [FAIL] Session timeout detected

Test Summary:
- Total Tests: 45
- Passed: 42
- Failed: 1
- Warnings: 2
- Success Rate: 93.3%
```

## 🔧 Configuration

### Prerequisites
- PHP 7.4 or higher
- MySQL database with banking schema
- PHPMailer and Google2FA libraries (via Composer)
- Web server (Apache/Nginx) for full integration tests

### Database Setup
Ensure your test database has the required tables:
```sql
-- Core tables needed for testing
accounts, transfers, beneficiaries, tickets, audit_logs,
login_attempts, user_otps, user_security_settings
```

### Environment Configuration
Update `config/config.php` with appropriate test settings:
```php
// Test-specific configurations
define('SESSION_TIMEOUT', 1800);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900);
```

## 🛡️ Security Testing Features

### Session Hijacking Protection
- IP address validation
- User agent consistency checks
- Session ID regeneration
- Periodic security updates

### CSRF Protection
- Token generation and validation
- Token expiration handling
- Cross-site request forgery prevention

### Data Integrity
- Input validation and sanitization
- SQL injection prevention
- XSS protection measures

## ⚡ Performance Benchmarks

### Expected Performance Metrics
- **Session Initialization**: < 10ms
- **Data Operations**: < 1ms per operation
- **Validation Checks**: < 0.1ms
- **Memory Usage**: < 2MB increase per session

### Stress Testing
- 100+ concurrent session simulations
- 1000+ rapid login/logout cycles
- Large session data handling (1MB+)
- Extended timeout scenarios

## 🔗 Integration Testing

### Database Integration
- Real user authentication flows
- Transaction processing with sessions
- Data consistency validation
- Error handling verification

### Email System Integration
- OTP delivery testing
- Email template validation
- SMTP configuration verification

## 📈 Monitoring and Maintenance

### Regular Testing Schedule
- **Daily**: Run comprehensive tests during development
- **Weekly**: Full security and performance analysis
- **Monthly**: Integration testing with production data
- **Release**: Complete test suite before deployment

### Performance Monitoring
```php
// Monitor session performance in production
$sessionInfo = SessionManager::getSessionInfo();
$timeRemaining = $sessionInfo['time_remaining'];
$memoryUsage = memory_get_usage();
```

## 🐛 Troubleshooting

### Common Issues

**Test Database Connection Fails**
```bash
# Check database configuration
php -r "require 'config/database.php'; echo 'DB connection: ' . (getDB() ? 'OK' : 'FAILED');"
```

**Session Tests Timeout**
```bash
# Increase PHP execution time
ini_set('max_execution_time', 300);
```

**Memory Limit Exceeded**
```bash
# Increase memory limit for tests
ini_set('memory_limit', '256M');
```

### Debug Mode
Enable detailed logging by setting:
```php
// In test files
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 📝 Contributing

### Adding New Tests
1. Create test file in appropriate category
2. Follow existing naming conventions
3. Include comprehensive documentation
4. Update this README with new test descriptions

### Test Structure
```php
class NewSessionTest {
    private function logTest($description, $passed, $details = '') {
        // Standard test logging format
    }
    
    public function testNewFeature() {
        // Test implementation
    }
    
    public function runAllTests() {
        // Execute all tests in class
    }
}
```

## 📞 Support

For issues or questions regarding the session test suite:

1. Check the test output for specific error messages
2. Review the configuration settings
3. Verify database connectivity and schema
4. Consult the main application documentation

## 🔄 Version History

- **v1.0**: Initial comprehensive session test suite
- **v1.1**: Added performance benchmarking
- **v1.2**: Enhanced security testing
- **v1.3**: Integrated database testing
- **v1.4**: Added master test runner with reporting

---

**Note**: These tests are designed for development and testing environments. Ensure proper security measures are in place before running in production environments.
