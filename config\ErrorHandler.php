<?php
/**
 * Centralized Error Handler Class
 * Provides unified error logging and exception handling for the banking system
 */

class ErrorHandler {
    
    private static $instance = null;
    private $logFile;
    private $errorLevels = [
        'DEBUG' => 0,
        'INFO' => 1,
        'WARNING' => 2,
        'ERROR' => 3,
        'CRITICAL' => 4,
        'FATAL' => 5
    ];
    
    private function __construct() {
        $this->logFile = __DIR__ . '/../logs/error.log';
        $this->ensureLogDirectory();
        $this->registerHandlers();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Register error and exception handlers
     */
    private function registerHandlers() {
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleFatalError']);
    }
    
    /**
     * Ensure log directory exists
     */
    private function ensureLogDirectory() {
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * Log error with context
     * 
     * @param string $message Error message
     * @param array $context Additional context information
     * @param string $level Error level (DEBUG, INFO, WARNING, ERROR, CRITICAL, FATAL)
     */
    public static function logError($message, $context = [], $level = 'ERROR') {
        $instance = self::getInstance();
        $instance->writeLog($level, $message, $context);
    }
    
    /**
     * Log exception with context
     * 
     * @param Exception $exception The exception to log
     * @param array $context Additional context information
     */
    public static function logException($exception, $context = []) {
        $instance = self::getInstance();
        
        $message = sprintf(
            'Exception: %s in %s:%d',
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine()
        );
        
        $context['exception_class'] = get_class($exception);
        $context['stack_trace'] = $exception->getTraceAsString();
        
        $instance->writeLog('ERROR', $message, $context);
    }
    
    /**
     * Handle PHP errors
     */
    public function handleError($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $level = $this->mapErrorSeverity($severity);
        $context = [
            'file' => $file,
            'line' => $line,
            'severity' => $severity
        ];
        
        $this->writeLog($level, $message, $context);
        
        // Don't execute PHP internal error handler
        return true;
    }
    
    /**
     * Handle uncaught exceptions
     */
    public function handleException($exception) {
        $this->logException($exception, ['uncaught' => true]);
        
        // Show user-friendly error page in production
        if (!$this->isDebugMode()) {
            $this->showErrorPage();
        }
    }
    
    /**
     * Handle fatal errors
     */
    public function handleFatalError() {
        $error = error_get_last();
        
        if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $context = [
                'file' => $error['file'],
                'line' => $error['line'],
                'type' => $error['type']
            ];
            
            $this->writeLog('FATAL', $error['message'], $context);
            
            if (!$this->isDebugMode()) {
                $this->showErrorPage();
            }
        }
    }
    
    /**
     * Write log entry to file
     */
    private function writeLog($level, $message, $context = []) {
        $logEntry = $this->formatLogEntry($level, $message, $context);
        
        // Write to file
        file_put_contents($this->logFile, $logEntry . PHP_EOL, FILE_APPEND | LOCK_EX);
        
        // Also log to system log in production
        if (!$this->isDebugMode()) {
            error_log($logEntry);
        }
    }
    
    /**
     * Format log entry
     */
    public function formatLogEntry($level, $message, $context = []) {
        $timestamp = date('Y-m-d H:i:s');
        $sessionId = session_id() ?: 'no-session';
        $userId = $_SESSION['user_id'] ?? 'anonymous';
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $requestUri = $_SERVER['REQUEST_URI'] ?? 'unknown';
        
        $logData = [
            'timestamp' => $timestamp,
            'level' => $level,
            'message' => $message,
            'session_id' => $sessionId,
            'user_id' => $userId,
            'ip_address' => $ip,
            'user_agent' => substr($userAgent, 0, 200), // Limit length
            'request_uri' => $requestUri,
            'context' => $context
        ];
        
        return json_encode($logData, JSON_UNESCAPED_SLASHES);
    }
    
    /**
     * Map PHP error severity to our levels
     */
    private function mapErrorSeverity($severity) {
        switch ($severity) {
            case E_ERROR:
            case E_CORE_ERROR:
            case E_COMPILE_ERROR:
            case E_USER_ERROR:
                return 'ERROR';
            case E_WARNING:
            case E_CORE_WARNING:
            case E_COMPILE_WARNING:
            case E_USER_WARNING:
                return 'WARNING';
            case E_NOTICE:
            case E_USER_NOTICE:
                return 'INFO';
            case E_STRICT:
            case E_DEPRECATED:
            case E_USER_DEPRECATED:
                return 'DEBUG';
            default:
                return 'ERROR';
        }
    }
    
    /**
     * Check if debug mode is enabled
     */
    private function isDebugMode() {
        return defined('DEBUG_MODE') && DEBUG_MODE === true;
    }
    
    /**
     * Show user-friendly error page
     */
    private function showErrorPage() {
        if (!headers_sent()) {
            http_response_code(500);
            header('Content-Type: text/html; charset=UTF-8');
        }
        
        echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Error - SecureBank</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 40px; background: #f8f9fa; }
        .error-container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .error-icon { font-size: 4rem; color: #dc3545; margin-bottom: 20px; }
        h1 { color: #343a40; margin-bottom: 20px; }
        p { color: #6c757d; line-height: 1.6; margin-bottom: 30px; }
        .btn { background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <h1>System Temporarily Unavailable</h1>
        <p>We apologize for the inconvenience. Our banking system is experiencing technical difficulties and our team has been notified.</p>
        <p>Please try again in a few minutes or contact customer support if the problem persists.</p>
        <a href="/" class="btn">Return to Homepage</a>
    </div>
</body>
</html>';
        exit;
    }
    
    /**
     * Get recent error logs
     */
    public static function getRecentLogs($limit = 100) {
        $instance = self::getInstance();
        
        if (!file_exists($instance->logFile)) {
            return [];
        }
        
        $lines = file($instance->logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $logs = [];
        
        // Get last N lines
        $recentLines = array_slice($lines, -$limit);
        
        foreach ($recentLines as $line) {
            $decoded = json_decode($line, true);
            if ($decoded) {
                $logs[] = $decoded;
            }
        }
        
        return array_reverse($logs); // Most recent first
    }
    
    /**
     * Clear old log entries (keep last 30 days)
     */
    public static function cleanupLogs() {
        $instance = self::getInstance();
        
        if (!file_exists($instance->logFile)) {
            return;
        }
        
        $lines = file($instance->logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $cutoffDate = date('Y-m-d', strtotime('-30 days'));
        $filteredLines = [];
        
        foreach ($lines as $line) {
            $decoded = json_decode($line, true);
            if ($decoded && isset($decoded['timestamp'])) {
                $logDate = substr($decoded['timestamp'], 0, 10);
                if ($logDate >= $cutoffDate) {
                    $filteredLines[] = $line;
                }
            }
        }
        
        file_put_contents($instance->logFile, implode(PHP_EOL, $filteredLines) . PHP_EOL);
    }
}

// Initialize error handler
ErrorHandler::getInstance();
?>
