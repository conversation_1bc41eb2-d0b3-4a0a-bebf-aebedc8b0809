<?php
require_once '../config/config.php';
require_once '../config/super_admin_settings.php';

// Require super admin access
requireSuperAdmin();

$page_title = 'Super Admin Settings';
$success = '';
$errors = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $settings_to_update = [
            'site_name', 'site_url', 'support_email', 'support_phone',
            'security_email', 'security_phone', 'noreply_email', 'admin_email',
            'company_address', 'company_phone', 'email_footer_text',
            'privacy_policy_url', 'terms_of_service_url', 'help_center_url',
            'max_login_attempts', 'session_timeout', 'otp_expiry_minutes',
            'maintenance_mode', 'maintenance_message'
        ];
        
        // Handle SMTP settings separately (more sensitive)
        if (isset($_POST['update_smtp']) && $_POST['update_smtp'] === '1') {
            $smtp_settings = [
                'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password',
                'smtp_encryption', 'smtp_from_email', 'smtp_from_name'
            ];
            $settings_to_update = array_merge($settings_to_update, $smtp_settings);
        }
        
        $updated_count = 0;
        foreach ($settings_to_update as $key) {
            if (isset($_POST[$key])) {
                $value = $_POST[$key];
                
                // Validate setting
                $validation_errors = validateSuperAdminSetting($key, $value);
                if (!empty($validation_errors)) {
                    $errors = array_merge($errors, $validation_errors);
                    continue;
                }
                
                // Special handling for boolean values
                if ($key === 'maintenance_mode') {
                    $value = isset($_POST[$key]) ? '1' : '0';
                }
                
                updateSuperAdminSetting($key, $value, $_POST['change_reason'] ?? 'Settings update via admin panel');
                $updated_count++;
            }
        }
        
        if (empty($errors)) {
            $success = "Successfully updated $updated_count settings.";
        }
        
    } catch (Exception $e) {
        $errors[] = "Error updating settings: " . $e->getMessage();
    }
}

// Get current settings
$current_settings = getSuperAdminSettings();

include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-shield-alt"></i> Super Admin Settings
                    </h3>
                    <p class="mb-0 mt-2">
                        <i class="fas fa-exclamation-triangle"></i> 
                        <strong>Warning:</strong> These settings affect the entire banking system. Only super administrators can modify these values.
                    </p>
                </div>
                
                <div class="card-body">
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> 
                            <strong>Errors:</strong>
                            <ul class="mb-0 mt-2">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <!-- Site Configuration -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-globe"></i> Site Configuration</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="site_name">Site Name</label>
                                            <input type="text" class="form-control" id="site_name" name="site_name" 
                                                   value="<?php echo htmlspecialchars($current_settings['site_name'] ?? ''); ?>" required>
                                            <small class="form-text text-muted">Name displayed throughout the system and in emails</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="site_url">Site URL</label>
                                            <input type="url" class="form-control" id="site_url" name="site_url" 
                                                   value="<?php echo htmlspecialchars($current_settings['site_url'] ?? ''); ?>" required>
                                            <small class="form-text text-muted">Base URL of your banking platform</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email_footer_text">Email Footer Text</label>
                                    <input type="text" class="form-control" id="email_footer_text" name="email_footer_text" 
                                           value="<?php echo htmlspecialchars($current_settings['email_footer_text'] ?? ''); ?>">
                                    <small class="form-text text-muted">Text displayed in email footers</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Contact Information -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-address-book"></i> Contact Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="support_email">Support Email</label>
                                            <input type="email" class="form-control" id="support_email" name="support_email" 
                                                   value="<?php echo htmlspecialchars($current_settings['support_email'] ?? ''); ?>" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="security_email">Security Email</label>
                                            <input type="email" class="form-control" id="security_email" name="security_email" 
                                                   value="<?php echo htmlspecialchars($current_settings['security_email'] ?? ''); ?>" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="admin_email">Admin Email</label>
                                            <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                                   value="<?php echo htmlspecialchars($current_settings['admin_email'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="support_phone">Support Phone</label>
                                            <input type="text" class="form-control" id="support_phone" name="support_phone" 
                                                   value="<?php echo htmlspecialchars($current_settings['support_phone'] ?? ''); ?>" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="security_phone">Security Phone</label>
                                            <input type="text" class="form-control" id="security_phone" name="security_phone" 
                                                   value="<?php echo htmlspecialchars($current_settings['security_phone'] ?? ''); ?>" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="company_phone">Company Phone</label>
                                            <input type="text" class="form-control" id="company_phone" name="company_phone" 
                                                   value="<?php echo htmlspecialchars($current_settings['company_phone'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="company_address">Company Address</label>
                                    <textarea class="form-control" id="company_address" name="company_address" rows="2"><?php echo htmlspecialchars($current_settings['company_address'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- System Settings -->
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0"><i class="fas fa-cogs"></i> System Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="max_login_attempts">Max Login Attempts</label>
                                            <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                                                   value="<?php echo htmlspecialchars($current_settings['max_login_attempts'] ?? '5'); ?>" min="1" max="10">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="session_timeout">Session Timeout (minutes)</label>
                                            <input type="number" class="form-control" id="session_timeout" name="session_timeout" 
                                                   value="<?php echo htmlspecialchars($current_settings['session_timeout'] ?? '30'); ?>" min="5" max="120">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="otp_expiry_minutes">OTP Expiry (minutes)</label>
                                            <input type="number" class="form-control" id="otp_expiry_minutes" name="otp_expiry_minutes" 
                                                   value="<?php echo htmlspecialchars($current_settings['otp_expiry_minutes'] ?? '10'); ?>" min="1" max="30">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="maintenance_mode" name="maintenance_mode" value="1"
                                               <?php echo ($current_settings['maintenance_mode'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="maintenance_mode">
                                            <strong>Maintenance Mode</strong> - Temporarily disable user access
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="maintenance_message">Maintenance Message</label>
                                    <textarea class="form-control" id="maintenance_message" name="maintenance_message" rows="2"><?php echo htmlspecialchars($current_settings['maintenance_message'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Change Reason -->
                        <div class="card mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0"><i class="fas fa-edit"></i> Change Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="change_reason">Reason for Changes (Optional)</label>
                                    <input type="text" class="form-control" id="change_reason" name="change_reason" 
                                           placeholder="e.g., Updated contact information, Security enhancement, etc.">
                                    <small class="form-text text-muted">This will be logged for audit purposes</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> Update Settings
                            </button>
                            <a href="dashboard.php" class="btn btn-secondary btn-lg ml-3">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Maintenance mode warning
document.getElementById('maintenance_mode').addEventListener('change', function() {
    if (this.checked) {
        if (!confirm('Are you sure you want to enable maintenance mode? This will prevent users from accessing the system.')) {
            this.checked = false;
        }
    }
});
</script>

<?php include '../includes/admin_footer.php'; ?>
