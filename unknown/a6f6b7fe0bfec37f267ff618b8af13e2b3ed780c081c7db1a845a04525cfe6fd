<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Beneficiaries';
$site_name = getBankName();

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_beneficiary':
                    $name = sanitizeInput($_POST['name']);
                    $account_number = sanitizeInput($_POST['account_number']);
                    $bank_name = sanitizeInput($_POST['bank_name']);
                    $bank_code = sanitizeInput($_POST['bank_code'] ?? '');
                    $country = sanitizeInput($_POST['country'] ?? 'USA');
                    $currency = sanitizeInput($_POST['currency'] ?? 'USD');
                    $is_favorite = isset($_POST['is_favorite']) ? 1 : 0;
                    
                    // Validation
                    if (empty($name) || empty($account_number) || empty($bank_name)) {
                        throw new Exception('Please fill in all required fields.');
                    }
                    
                    if (strlen($account_number) < 8 || strlen($account_number) > 20) {
                        throw new Exception('Account number must be between 8 and 20 characters.');
                    }
                    
                    // Check if beneficiary already exists
                    $check_sql = "SELECT id FROM beneficiaries WHERE user_id = ? AND account_number = ?";
                    $check_result = $db->query($check_sql, [$user_id, $account_number]);
                    if ($check_result->num_rows > 0) {
                        throw new Exception('This beneficiary already exists in your list.');
                    }
                    
                    // Insert new beneficiary
                    $insert_sql = "INSERT INTO beneficiaries (user_id, name, account_number, bank_name, bank_code, country, currency, is_favorite) 
                                  VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                    $result = $db->query($insert_sql, [$user_id, $name, $account_number, $bank_name, $bank_code, $country, $currency, $is_favorite]);
                    
                    if ($result) {
                        $success_message = 'Beneficiary added successfully!';
                    } else {
                        throw new Exception('Failed to add beneficiary.');
                    }
                    break;
                    
                case 'edit_beneficiary':
                    $beneficiary_id = intval($_POST['beneficiary_id']);
                    $name = sanitizeInput($_POST['name']);
                    $bank_name = sanitizeInput($_POST['bank_name']);
                    $bank_code = sanitizeInput($_POST['bank_code'] ?? '');
                    $country = sanitizeInput($_POST['country'] ?? 'USA');
                    $currency = sanitizeInput($_POST['currency'] ?? 'USD');
                    $is_favorite = isset($_POST['is_favorite']) ? 1 : 0;
                    
                    // Validation
                    if (empty($name) || empty($bank_name)) {
                        throw new Exception('Please fill in all required fields.');
                    }
                    
                    // Update beneficiary
                    $update_sql = "UPDATE beneficiaries SET name = ?, bank_name = ?, bank_code = ?, country = ?, currency = ?, is_favorite = ? 
                                  WHERE id = ? AND user_id = ?";
                    $result = $db->query($update_sql, [$name, $bank_name, $bank_code, $country, $currency, $is_favorite, $beneficiary_id, $user_id]);
                    
                    if ($result) {
                        $success_message = 'Beneficiary updated successfully!';
                    } else {
                        throw new Exception('Failed to update beneficiary.');
                    }
                    break;
                    
                case 'delete_beneficiary':
                    $beneficiary_id = intval($_POST['beneficiary_id']);
                    
                    // Delete beneficiary
                    $delete_sql = "DELETE FROM beneficiaries WHERE id = ? AND user_id = ?";
                    $result = $db->query($delete_sql, [$beneficiary_id, $user_id]);
                    
                    if ($result) {
                        $success_message = 'Beneficiary deleted successfully!';
                    } else {
                        throw new Exception('Failed to delete beneficiary.');
                    }
                    break;
                    
                case 'toggle_favorite':
                    $beneficiary_id = intval($_POST['beneficiary_id']);
                    
                    // Toggle favorite status
                    $toggle_sql = "UPDATE beneficiaries SET is_favorite = NOT is_favorite WHERE id = ? AND user_id = ?";
                    $result = $db->query($toggle_sql, [$beneficiary_id, $user_id]);
                    
                    if ($result) {
                        $success_message = 'Favorite status updated!';
                    } else {
                        throw new Exception('Failed to update favorite status.');
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user's beneficiaries
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get all beneficiaries with transfer statistics
    $beneficiaries_sql = "SELECT b.*, 
                                COUNT(t.id) as transfer_count,
                                SUM(t.amount) as total_transferred,
                                MAX(t.created_at) as last_transfer_date
                         FROM beneficiaries b
                         LEFT JOIN transfers t ON b.account_number = t.recipient_account AND t.sender_id = ?
                         WHERE b.user_id = ?
                         GROUP BY b.id
                         ORDER BY b.is_favorite DESC, b.name ASC";
    $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id, $user_id]);
    $beneficiaries = [];
    while ($beneficiary = $beneficiaries_result->fetch_assoc()) {
        $beneficiaries[] = $beneficiary;
    }
    
    // Get beneficiary statistics
    $stats_sql = "SELECT 
                    COUNT(*) as total_beneficiaries,
                    COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_count,
                    COUNT(DISTINCT country) as countries_count
                  FROM beneficiaries WHERE user_id = ?";
    $stats_result = $db->query($stats_sql, [$user_id]);
    $stats = $stats_result->fetch_assoc();
    
    // Get recent transfers to beneficiaries
    $recent_transfers_sql = "SELECT t.*, b.name as beneficiary_name
                            FROM transfers t
                            JOIN beneficiaries b ON t.recipient_account = b.account_number AND b.user_id = ?
                            WHERE t.sender_id = ?
                            ORDER BY t.created_at DESC
                            LIMIT 5";
    $recent_transfers_result = $db->query($recent_transfers_sql, [$user_id, $user_id]);
    $recent_transfers = [];
    while ($transfer = $recent_transfers_result->fetch_assoc()) {
        $recent_transfers[] = $transfer;
    }

} catch (Exception $e) {
    error_log("Beneficiaries page error: " . $e->getMessage());
    $beneficiaries = [];
    $stats = ['total_beneficiaries' => 0, 'favorite_count' => 0, 'countries_count' => 0];
    $recent_transfers = [];
}

include '../includes/dashboard/header.php';
include '../includes/dashboard/sidebar.php';
?>

<div class="main-content">
    <div class="banking-container">
        <div class="banking-dashboard">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1>Beneficiaries</h1>
                    <p>Manage your transfer recipients</p>
                </div>
                <div class="page-actions">
                    <a href="../transfers/" class="btn-outline">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                        </svg>
                        Send Money
                    </a>
                    <button class="btn-primary" onclick="showAddBeneficiaryModal()">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                        </svg>
                        Add Beneficiary
                    </button>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Beneficiaries Statistics -->
            <div class="beneficiaries-stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_beneficiaries']); ?></h3>
                        <p>Total Beneficiaries</p>
                        <div class="stat-details">
                            <span><?php echo $stats['favorite_count']; ?> favorites</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['countries_count']); ?></h3>
                        <p>Countries</p>
                        <div class="stat-details">
                            <span>Global reach</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo count($recent_transfers); ?></h3>
                        <p>Recent Transfers</p>
                        <div class="stat-details">
                            <span>Last 30 days</span>
                        </div>
                    </div>
                </div>
            </div>
