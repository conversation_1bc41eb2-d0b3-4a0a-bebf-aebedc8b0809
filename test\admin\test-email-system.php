<?php
/**
 * Comprehensive Email System Test Suite
 * Tests all email functionality with real SMTP <NAME_EMAIL>
 */

require_once '../../config/config.php';
require_once '../../config/email.php';
require_once '../../config/EmailManager.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test email address
$test_email = '<EMAIL>';
$test_user_name = 'Test User';
$test_user_id = 999;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email System Test Suite</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 30px 0; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .test-card { border: 1px solid #e9ecef; padding: 20px; border-radius: 8px; background: #f8f9fa; }
        .test-card h4 { margin: 0 0 15px 0; color: #495057; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
        .test-button:disabled { background: #6c757d; cursor: not-allowed; }
        .status-badge { padding: 4px 8px; border-radius: 4px; font-weight: bold; font-size: 0.8rem; }
        .status-pass { background: #28a745; color: white; }
        .status-fail { background: #dc3545; color: white; }
        .status-pending { background: #ffc107; color: #212529; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        .email-preview { border: 1px solid #ddd; padding: 15px; border-radius: 4px; background: white; margin: 10px 0; }
        .loading { display: none; }
        .loading.show { display: inline-block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Email System Test Suite</h1>
        <p><strong>Test Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        <p><strong>Test Email:</strong> <?php echo $test_email; ?></p>

        <!-- Configuration Test -->
        <div class="test-section">
            <h2>⚙️ Email Configuration Test</h2>
            
            <?php
            // Test email configuration
            $config_tests = [];
            
            // Test 1: Check if email config file exists
            if (file_exists('../../config/email.php')) {
                $config_tests['config_file'] = ['status' => 'pass', 'message' => 'Email config file exists'];
            } else {
                $config_tests['config_file'] = ['status' => 'fail', 'message' => 'Email config file missing'];
            }
            
            // Test 2: Check SMTP constants
            if (defined('SMTP_HOST') && defined('SMTP_USERNAME') && defined('SMTP_PASSWORD')) {
                $config_tests['smtp_config'] = ['status' => 'pass', 'message' => 'SMTP configuration defined'];
                $smtp_details = [
                    'Host' => SMTP_HOST,
                    'Port' => SMTP_PORT,
                    'Username' => SMTP_USERNAME,
                    'Encryption' => SMTP_ENCRYPTION,
                    'From Email' => FROM_EMAIL,
                    'From Name' => FROM_NAME
                ];
            } else {
                $config_tests['smtp_config'] = ['status' => 'fail', 'message' => 'SMTP configuration missing'];
            }
            
            // Test 3: Check EmailManager class
            if (class_exists('EmailManager')) {
                $config_tests['email_manager'] = ['status' => 'pass', 'message' => 'EmailManager class available'];
            } else {
                $config_tests['email_manager'] = ['status' => 'fail', 'message' => 'EmailManager class missing'];
            }
            
            // Test 4: Check PHPMailer
            if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
                $config_tests['phpmailer'] = ['status' => 'pass', 'message' => 'PHPMailer available'];
            } else {
                $config_tests['phpmailer'] = ['status' => 'fail', 'message' => 'PHPMailer not available'];
            }
            ?>
            
            <div class="test-grid">
                <?php foreach ($config_tests as $test_name => $test_result): ?>
                <div class="test-card">
                    <h4><?php echo ucwords(str_replace('_', ' ', $test_name)); ?></h4>
                    <span class="status-badge status-<?php echo $test_result['status']; ?>">
                        <?php echo $test_result['status'] === 'pass' ? '✅ PASSED' : '❌ FAILED'; ?>
                    </span>
                    <p><?php echo $test_result['message']; ?></p>
                </div>
                <?php endforeach; ?>
            </div>
            
            <?php if (isset($smtp_details)): ?>
            <div class="info">
                <h4>SMTP Configuration Details:</h4>
                <pre><?php echo json_encode($smtp_details, JSON_PRETTY_PRINT); ?></pre>
            </div>
            <?php endif; ?>
        </div>

        <!-- Email Testing Interface -->
        <div class="test-section">
            <h2>🧪 Email Testing Interface</h2>
            <p>Click the buttons below to test different email types. All emails will be sent to: <strong><?php echo $test_email; ?></strong></p>
            
            <div class="test-grid">
                <div class="test-card">
                    <h4>🎉 Welcome Email</h4>
                    <p>Test user registration confirmation email</p>
                    <button class="test-button" onclick="sendTestEmail('welcome')">
                        Send Welcome Email
                    </button>
                    <div id="welcome-result"></div>
                </div>
                
                <div class="test-card">
                    <h4>🔐 OTP Code Email</h4>
                    <p>Test OTP code delivery email</p>
                    <button class="test-button" onclick="sendTestEmail('otp')">
                        Send OTP Email
                    </button>
                    <div id="otp-result"></div>
                </div>
                
                <div class="test-card">
                    <h4>🔒 Password Reset Email</h4>
                    <p>Test password reset notification</p>
                    <button class="test-button" onclick="sendTestEmail('password_reset')">
                        Send Password Reset
                    </button>
                    <div id="password_reset-result"></div>
                </div>
                
                <div class="test-card">
                    <h4>⚠️ Account Suspension</h4>
                    <p>Test account suspension notification</p>
                    <button class="test-button" onclick="sendTestEmail('suspension')">
                        Send Suspension Alert
                    </button>
                    <div id="suspension-result"></div>
                </div>
                
                <div class="test-card">
                    <h4>🗑️ Account Deletion</h4>
                    <p>Test account deletion notification</p>
                    <button class="test-button" onclick="sendTestEmail('deletion')">
                        Send Deletion Alert
                    </button>
                    <div id="deletion-result"></div>
                </div>
                
                <div class="test-card">
                    <h4>💰 Transaction Alert</h4>
                    <p>Test transaction notification email</p>
                    <button class="test-button" onclick="sendTestEmail('transaction')">
                        Send Transaction Alert
                    </button>
                    <div id="transaction-result"></div>
                </div>
                
                <div class="test-card">
                    <h4>🛡️ Security Alert</h4>
                    <p>Test security notification email</p>
                    <button class="test-button" onclick="sendTestEmail('security')">
                        Send Security Alert
                    </button>
                    <div id="security-result"></div>
                </div>
                
                <div class="test-card">
                    <h4>🧪 SMTP Test</h4>
                    <p>Test basic SMTP connectivity</p>
                    <button class="test-button" onclick="sendTestEmail('smtp_test')">
                        Test SMTP Connection
                    </button>
                    <div id="smtp_test-result"></div>
                </div>
            </div>
        </div>

        <!-- Test All Button -->
        <div class="test-section">
            <h2>🚀 Comprehensive Test</h2>
            <p>Run all email tests sequentially to verify complete email system functionality.</p>
            <button class="test-button" onclick="runAllTests()" style="background: #28a745; font-size: 1.1rem; padding: 15px 30px;">
                🧪 Run All Email Tests
            </button>
            <div id="all-tests-result"></div>
        </div>

        <!-- Results Summary -->
        <div class="test-section">
            <h2>📊 Test Results Summary</h2>
            <div id="results-summary">
                <p>Click "Run All Email Tests" to see comprehensive results...</p>
            </div>
        </div>
    </div>

    <script>
        let testResults = {};
        let testCount = 0;
        let completedTests = 0;

        function sendTestEmail(type) {
            const button = event.target;
            const resultDiv = document.getElementById(type + '-result');
            
            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = '<span class="loading show">⏳</span> Sending...';
            
            // Clear previous results
            resultDiv.innerHTML = '';
            
            // Send AJAX request
            fetch('test-email-handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: type,
                    email: '<?php echo $test_email; ?>',
                    user_name: '<?php echo $test_user_name; ?>',
                    user_id: <?php echo $test_user_id; ?>
                })
            })
            .then(response => response.json())
            .then(data => {
                // Store result
                testResults[type] = data;
                
                // Display result
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Email sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed: ' + data.error + '</div>';
                }
                
                // Re-enable button
                button.disabled = false;
                button.innerHTML = button.innerHTML.replace('<span class="loading show">⏳</span> Sending...', 'Send ' + type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));
                
                completedTests++;
                updateResultsSummary();
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = '<div class="error">❌ Network error: ' + error.message + '</div>';
                
                // Re-enable button
                button.disabled = false;
                button.innerHTML = button.innerHTML.replace('<span class="loading show">⏳</span> Sending...', 'Send ' + type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));
            });
        }

        function runAllTests() {
            const allTestsButton = event.target;
            const resultDiv = document.getElementById('all-tests-result');
            
            allTestsButton.disabled = true;
            allTestsButton.innerHTML = '⏳ Running All Tests...';
            
            const emailTypes = ['welcome', 'otp', 'password_reset', 'suspension', 'deletion', 'transaction', 'security', 'smtp_test'];
            testCount = emailTypes.length;
            completedTests = 0;
            testResults = {};
            
            resultDiv.innerHTML = '<div class="info">Running ' + testCount + ' email tests...</div>';
            
            // Run tests sequentially with delay
            emailTypes.forEach((type, index) => {
                setTimeout(() => {
                    // Simulate button click for each test
                    const button = document.querySelector(`button[onclick="sendTestEmail('${type}')"]`);
                    if (button) {
                        button.click();
                    }
                }, index * 2000); // 2 second delay between tests
            });
            
            // Re-enable button after all tests
            setTimeout(() => {
                allTestsButton.disabled = false;
                allTestsButton.innerHTML = '🧪 Run All Email Tests';
            }, emailTypes.length * 2000 + 5000);
        }

        function updateResultsSummary() {
            const summaryDiv = document.getElementById('results-summary');
            
            if (completedTests === 0) return;
            
            let successCount = 0;
            let failCount = 0;
            let summaryHTML = '<h4>Test Results:</h4><ul>';
            
            for (const [type, result] of Object.entries(testResults)) {
                if (result.success) {
                    successCount++;
                    summaryHTML += `<li>✅ ${type.replace('_', ' ').toUpperCase()}: Success</li>`;
                } else {
                    failCount++;
                    summaryHTML += `<li>❌ ${type.replace('_', ' ').toUpperCase()}: Failed - ${result.error}</li>`;
                }
            }
            
            summaryHTML += '</ul>';
            summaryHTML += `<div class="info"><strong>Summary:</strong> ${successCount} passed, ${failCount} failed out of ${completedTests} tests completed.</div>`;
            
            if (completedTests === testCount && testCount > 0) {
                if (failCount === 0) {
                    summaryHTML += '<div class="success"><strong>🎉 ALL EMAIL TESTS PASSED!</strong> Email system is fully functional.</div>';
                } else {
                    summaryHTML += '<div class="warning"><strong>⚠️ Some tests failed.</strong> Please review the failed tests above.</div>';
                }
            }
            
            summaryDiv.innerHTML = summaryHTML;
        }
    </script>
</body>
</html>
