<?php
/**
 * User Documents & KYC Management Section
 * Fresh implementation with clean code and better error handling
 */

// Ensure we have user data
if (!isset($user) || !$user) {
    echo '<div class="alert alert-danger">User data not available</div>';
    return;
}

$user_id = $user['id'];

try {
    $db = getDB();
    
    // Get user documents with better error handling
    $documents_query = "SELECT * FROM user_documents WHERE user_id = ? ORDER BY created_at DESC";
    $documents_result = $db->query($documents_query, [$user_id]);
    $user_documents = $documents_result ? $documents_result->fetch_all(MYSQLI_ASSOC) : [];
    
    // Get KYC application if exists
    $kyc_query = "SELECT * FROM kyc_applications WHERE user_id = ? ORDER BY created_at DESC LIMIT 1";
    $kyc_result = $db->query($kyc_query, [$user_id]);
    $kyc_application = $kyc_result && $kyc_result->num_rows > 0 ? $kyc_result->fetch_assoc() : null;
    
} catch (Exception $e) {
    error_log("Error loading user documents: " . $e->getMessage());
    $user_documents = [];
    $kyc_application = null;
}

// Helper functions are already declared in the main view-user.php file
?>

<!-- Documents & KYC Management -->
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-alt me-2"></i>
                    Documents & KYC Management
                </h3>
                <div class="card-actions">
                    <button class="btn btn-sm btn-primary" onclick="uploadDocument(<?php echo $user_id; ?>)">
                        <i class="fas fa-upload me-1"></i>
                        Upload Document
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- KYC Status Overview -->
                <?php if ($kyc_application): ?>
                <div class="alert alert-<?php echo $kyc_application['application_status'] === 'approved' ? 'success' : ($kyc_application['application_status'] === 'rejected' ? 'danger' : 'warning'); ?> mb-4">
                    <div class="d-flex">
                        <div>
                            <i class="fas fa-<?php echo $kyc_application['application_status'] === 'approved' ? 'check-circle' : ($kyc_application['application_status'] === 'rejected' ? 'times-circle' : 'clock'); ?> me-2"></i>
                        </div>
                        <div>
                            <h4 class="alert-title">KYC Status: <?php echo ucfirst(str_replace('_', ' ', $kyc_application['application_status'])); ?></h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Approval Level:</strong> <?php echo ucfirst($kyc_application['approval_level'] ?? 'Basic'); ?><br>
                                    <strong>Submitted:</strong> <?php echo $kyc_application['submitted_at'] ? formatDate($kyc_application['submitted_at']) : 'Not submitted'; ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Reviewed By:</strong> <?php echo $kyc_application['reviewed_by'] ? 'Admin' : 'Pending'; ?><br>
                                    <strong>Reviewed:</strong> <?php echo $kyc_application['reviewed_at'] ? formatDate($kyc_application['reviewed_at']) : 'Pending'; ?>
                                </div>
                            </div>
                            <?php if (!empty($kyc_application['notes'])): ?>
                            <div class="mt-2">
                                <strong>Notes:</strong> <?php echo htmlspecialchars($kyc_application['notes']); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-info mb-4">
                    <div class="d-flex">
                        <div><i class="fas fa-info-circle me-2"></i></div>
                        <div>
                            <h4 class="alert-title">No KYC Application</h4>
                            <p class="mb-0">This user has not submitted a KYC application yet.</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Documents Table -->
                <?php if (!empty($user_documents)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>Document Type</th>
                                <th>Document Name</th>
                                <th>File Info</th>
                                <th>Status</th>
                                <th>Upload Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($user_documents as $doc): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-<?php
                                        echo match($doc['document_type']) {
                                            'passport' => 'passport',
                                            'id_card' => 'id-card',
                                            'drivers_license' => 'car',
                                            'utility_bill' => 'file-invoice',
                                            'bank_statement' => 'university',
                                            'cheque' => 'money-check',
                                            'kyc_selfie' => 'camera',
                                            default => 'file'
                                        }; ?> me-2 text-muted"></i>
                                        <span><?php echo ucfirst(str_replace('_', ' ', $doc['document_type'])); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($doc['document_name']); ?>">
                                        <?php echo htmlspecialchars($doc['document_name']); ?>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <small class="text-muted">
                                            <?php echo strtoupper(pathinfo($doc['file_path'], PATHINFO_EXTENSION)); ?> •
                                            <?php echo formatFileSize($doc['file_size']); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $doc['verification_status'] === 'approved' ? 'success' : ($doc['verification_status'] === 'rejected' ? 'danger' : 'warning'); ?>-lt">
                                        <?php echo ucfirst($doc['verification_status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div><?php echo formatDate($doc['created_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($doc['created_at'], 'g:i A'); ?></small>
                                </td>
                                <td>
                                    <div class="btn-list">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewDocument(<?php echo $doc['id']; ?>)" title="View Document">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="approveDocument(<?php echo $doc['id']; ?>)" title="Approve Document">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="rejectDocument(<?php echo $doc['id']; ?>)" title="Reject Document">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="deleteDocument(<?php echo $doc['id']; ?>)" title="Delete Document">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-file-alt mb-2" style="font-size: 2rem;"></i>
                    <p>No documents uploaded</p>
                    <button class="btn btn-primary btn-sm" onclick="uploadDocument(<?php echo $user_id; ?>)">
                        <i class="fas fa-upload me-1"></i>
                        Upload First Document
                    </button>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Document Preview Modal -->
<div class="modal modal-blur fade" id="documentPreviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Document Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="documentPreviewContent">
                <!-- Document details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
