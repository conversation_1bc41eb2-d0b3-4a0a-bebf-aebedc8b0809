<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Account Statements';
$site_name = getBankName();

// Get user account information and generate statements
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get account info
    $account_sql = "SELECT * FROM accounts WHERE id = ?";
    $account_result = $db->query($account_sql, [$user_id]);
    $account = $account_result->fetch_assoc();
    
    // Generate monthly statements for the last 12 months
    $statements = [];
    for ($i = 0; $i < 12; $i++) {
        $month_start = date('Y-m-01', strtotime("-$i months"));
        $month_end = date('Y-m-t', strtotime("-$i months"));
        $month_name = date('F Y', strtotime("-$i months"));
        
        // Get transactions for this month
        $trans_sql = "SELECT 
                        COUNT(*) as transaction_count,
                        SUM(CASE WHEN transaction_type = 'credit' THEN amount ELSE 0 END) as credits,
                        SUM(CASE WHEN transaction_type = 'debit' THEN amount ELSE 0 END) as debits
                      FROM transactions 
                      WHERE user_id = ? AND DATE(created_at) BETWEEN ? AND ?";
        $trans_result = $db->query($trans_sql, [$user_id, $month_start, $month_end]);
        $trans_data = $trans_result->fetch_assoc();
        
        $statements[] = [
            'month' => $month_name,
            'period' => $month_start . ' to ' . $month_end,
            'transaction_count' => $trans_data['transaction_count'],
            'credits' => $trans_data['credits'] ?? 0,
            'debits' => $trans_data['debits'] ?? 0,
            'net_change' => ($trans_data['credits'] ?? 0) - ($trans_data['debits'] ?? 0),
            'available' => $trans_data['transaction_count'] > 0
        ];
    }
    
} catch (Exception $e) {
    error_log("Statements page error: " . $e->getMessage());
    $account = $_SESSION;
    $statements = [];
}

include '../includes/dashboard/header.php';
include '../includes/dashboard/sidebar.php';
?>

<div class="main-content">
    <div class="banking-container">
        <div class="banking-dashboard">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1>Account Statements</h1>
                    <p>Download and view your monthly account statements</p>
                </div>
                <div class="page-actions">
                    <a href="../transactions/" class="btn-outline">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                        View Transactions
                    </a>
                    <button class="btn-primary" onclick="downloadAllStatements()">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                        Download All
                    </button>
                </div>
            </div>

            <!-- Account Summary -->
            <div class="account-summary-section">
                <div class="summary-card">
                    <div class="summary-header">
                        <h3>Account Information</h3>
                        <span class="account-type"><?php echo ucfirst($account['account_type'] ?? 'Savings'); ?> Account</span>
                    </div>
                    <div class="summary-details">
                        <div class="detail-row">
                            <span class="label">Account Number</span>
                            <span class="value"><?php echo htmlspecialchars($account['account_number'] ?? ''); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Account Holder</span>
                            <span class="value"><?php echo htmlspecialchars(($account['first_name'] ?? '') . ' ' . ($account['last_name'] ?? '')); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Current Balance</span>
                            <span class="value balance"><?php echo formatCurrency($account['balance'] ?? 0, $account['currency'] ?? 'USD'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statements List -->
            <div class="statements-section">
                <div class="section-header">
                    <h3>Monthly Statements</h3>
                    <p>Select a statement to download or view details</p>
                </div>
                
                <div class="statements-list">
                    <?php if (!empty($statements)): ?>
                        <?php foreach ($statements as $index => $statement): ?>
                            <div class="statement-card <?php echo !$statement['available'] ? 'unavailable' : ''; ?>">
                                <div class="statement-header">
                                    <div class="statement-info">
                                        <h4><?php echo htmlspecialchars($statement['month']); ?></h4>
                                        <p class="statement-period"><?php echo htmlspecialchars($statement['period']); ?></p>
                                    </div>
                                    <div class="statement-status">
                                        <?php if ($statement['available']): ?>
                                            <span class="status-badge available">Available</span>
                                        <?php else: ?>
                                            <span class="status-badge unavailable">No Activity</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <?php if ($statement['available']): ?>
                                    <div class="statement-summary">
                                        <div class="summary-item">
                                            <span class="summary-label">Transactions</span>
                                            <span class="summary-value"><?php echo number_format($statement['transaction_count']); ?></span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Credits</span>
                                            <span class="summary-value positive">+<?php echo formatCurrency($statement['credits'], $account['currency'] ?? 'USD'); ?></span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Debits</span>
                                            <span class="summary-value negative">-<?php echo formatCurrency($statement['debits'], $account['currency'] ?? 'USD'); ?></span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Net Change</span>
                                            <span class="summary-value <?php echo $statement['net_change'] >= 0 ? 'positive' : 'negative'; ?>">
                                                <?php echo ($statement['net_change'] >= 0 ? '+' : '') . formatCurrency($statement['net_change'], $account['currency'] ?? 'USD'); ?>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="statement-actions">
                                        <button class="action-btn secondary" onclick="viewStatement('<?php echo $statement['month']; ?>')">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                            </svg>
                                            View Details
                                        </button>
                                        <button class="action-btn primary" onclick="downloadStatement('<?php echo $statement['month']; ?>')">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                            </svg>
                                            Download PDF
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="no-activity">
                                        <p>No transactions recorded for this period</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="empty-statements">
                            <div class="empty-icon">
                                <svg width="64" height="64" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <h4>No statements available</h4>
                            <p>Start making transactions to generate your first statement.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewStatement(month) {
    alert('Statement details for ' + month + ' will be displayed here.');
}

function downloadStatement(month) {
    alert('Downloading statement for ' + month + '...');
    // In a real implementation, this would trigger a PDF download
}

function downloadAllStatements() {
    alert('Downloading all available statements...');
    // In a real implementation, this would trigger a ZIP download of all PDFs
}
</script>

<?php include '../includes/dashboard/footer.php'; ?>
