<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Security Center';
$site_name = getBankName();

// Handle security actions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'change_password':
                    $current_password = $_POST['current_password'];
                    $new_password = $_POST['new_password'];
                    $confirm_password = $_POST['confirm_password'];
                    
                    // Validation
                    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                        throw new Exception('Please fill in all password fields.');
                    }
                    
                    if ($new_password !== $confirm_password) {
                        throw new Exception('New passwords do not match.');
                    }
                    
                    if (strlen($new_password) < 8) {
                        throw new Exception('New password must be at least 8 characters long.');
                    }
                    
                    // Verify current password
                    $user_sql = "SELECT password FROM accounts WHERE id = ?";
                    $user_result = $db->query($user_sql, [$user_id]);
                    $user = $user_result->fetch_assoc();
                    
                    if (!password_verify($current_password, $user['password'])) {
                        throw new Exception('Current password is incorrect.');
                    }
                    
                    // Update password
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $update_sql = "UPDATE accounts SET password = ?, updated_at = NOW() WHERE id = ?";
                    $result = $db->query($update_sql, [$hashed_password, $user_id]);
                    
                    if ($result) {
                        $success_message = 'Password changed successfully!';
                        
                        // Log security event
                        $log_sql = "INSERT INTO security_logs (user_id, event_type, description, ip_address) VALUES (?, 'password_change', 'Password changed successfully', ?)";
                        $db->query($log_sql, [$user_id, $_SERVER['REMOTE_ADDR']]);
                    } else {
                        throw new Exception('Failed to update password.');
                    }
                    break;
                    
                case 'change_pin':
                    $current_pin = $_POST['current_pin'];
                    $new_pin = $_POST['new_pin'];
                    $confirm_pin = $_POST['confirm_pin'];
                    
                    // Validation
                    if (empty($current_pin) || empty($new_pin) || empty($confirm_pin)) {
                        throw new Exception('Please fill in all PIN fields.');
                    }
                    
                    if ($new_pin !== $confirm_pin) {
                        throw new Exception('New PINs do not match.');
                    }
                    
                    if (!preg_match('/^\d{4,6}$/', $new_pin)) {
                        throw new Exception('PIN must be 4-6 digits only.');
                    }
                    
                    // Verify current PIN
                    $pin_sql = "SELECT transaction_pin FROM accounts WHERE id = ?";
                    $pin_result = $db->query($pin_sql, [$user_id]);
                    $pin_data = $pin_result->fetch_assoc();
                    
                    if (!password_verify($current_pin, $pin_data['transaction_pin'])) {
                        throw new Exception('Current PIN is incorrect.');
                    }
                    
                    // Update PIN
                    $hashed_pin = password_hash($new_pin, PASSWORD_DEFAULT);
                    $update_sql = "UPDATE accounts SET transaction_pin = ?, updated_at = NOW() WHERE id = ?";
                    $result = $db->query($update_sql, [$hashed_pin, $user_id]);
                    
                    if ($result) {
                        $success_message = 'Transaction PIN changed successfully!';
                        
                        // Log security event
                        $log_sql = "INSERT INTO security_logs (user_id, event_type, description, ip_address) VALUES (?, 'pin_change', 'Transaction PIN changed successfully', ?)";
                        $db->query($log_sql, [$user_id, $_SERVER['REMOTE_ADDR']]);
                    } else {
                        throw new Exception('Failed to update PIN.');
                    }
                    break;
                    
                case 'update_security_questions':
                    $question1 = sanitizeInput($_POST['question1']);
                    $answer1 = sanitizeInput($_POST['answer1']);
                    $question2 = sanitizeInput($_POST['question2']);
                    $answer2 = sanitizeInput($_POST['answer2']);
                    
                    if (empty($question1) || empty($answer1) || empty($question2) || empty($answer2)) {
                        throw new Exception('Please fill in all security question fields.');
                    }
                    
                    // Hash answers for security
                    $hashed_answer1 = password_hash(strtolower($answer1), PASSWORD_DEFAULT);
                    $hashed_answer2 = password_hash(strtolower($answer2), PASSWORD_DEFAULT);
                    
                    // Update security questions
                    $update_sql = "UPDATE accounts SET 
                                  security_question1 = ?, security_answer1 = ?,
                                  security_question2 = ?, security_answer2 = ?,
                                  updated_at = NOW() 
                                  WHERE id = ?";
                    $result = $db->query($update_sql, [$question1, $hashed_answer1, $question2, $hashed_answer2, $user_id]);
                    
                    if ($result) {
                        $success_message = 'Security questions updated successfully!';
                        
                        // Log security event
                        $log_sql = "INSERT INTO security_logs (user_id, event_type, description, ip_address) VALUES (?, 'security_questions_update', 'Security questions updated', ?)";
                        $db->query($log_sql, [$user_id, $_SERVER['REMOTE_ADDR']]);
                    } else {
                        throw new Exception('Failed to update security questions.');
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user security information
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get user security details
    $user_sql = "SELECT id, email, phone, last_login, created_at, 
                        security_question1, security_question2,
                        CASE WHEN transaction_pin IS NOT NULL THEN 1 ELSE 0 END as has_pin
                 FROM accounts WHERE id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();
    
    // Get recent security logs
    $logs_sql = "SELECT * FROM security_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT 10";
    $logs_result = $db->query($logs_sql, [$user_id]);
    $security_logs = [];
    while ($log = $logs_result->fetch_assoc()) {
        $security_logs[] = $log;
    }
    
    // Get login history
    $login_sql = "SELECT * FROM login_history WHERE user_id = ? ORDER BY login_time DESC LIMIT 10";
    $login_result = $db->query($login_sql, [$user_id]);
    $login_history = [];
    while ($login = $login_result->fetch_assoc()) {
        $login_history[] = $login;
    }
    
    // Security score calculation
    $security_score = 0;
    $security_items = [];
    
    // Password strength (assume strong if exists)
    $security_score += 25;
    $security_items[] = ['item' => 'Strong Password', 'status' => 'complete', 'points' => 25];
    
    // Transaction PIN
    if ($user['has_pin']) {
        $security_score += 25;
        $security_items[] = ['item' => 'Transaction PIN Set', 'status' => 'complete', 'points' => 25];
    } else {
        $security_items[] = ['item' => 'Transaction PIN Set', 'status' => 'incomplete', 'points' => 25];
    }
    
    // Security Questions
    if (!empty($user['security_question1']) && !empty($user['security_question2'])) {
        $security_score += 25;
        $security_items[] = ['item' => 'Security Questions', 'status' => 'complete', 'points' => 25];
    } else {
        $security_items[] = ['item' => 'Security Questions', 'status' => 'incomplete', 'points' => 25];
    }
    
    // Email verification (assume verified if account exists)
    $security_score += 25;
    $security_items[] = ['item' => 'Email Verified', 'status' => 'complete', 'points' => 25];

} catch (Exception $e) {
    error_log("Security page error: " . $e->getMessage());
    $user = $_SESSION;
    $security_logs = [];
    $login_history = [];
    $security_score = 0;
    $security_items = [];
}

include '../includes/dashboard/header.php';
include '../includes/dashboard/sidebar.php';
?>

<div class="main-content">
    <div class="banking-container">
        <div class="banking-dashboard">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1>Security Center</h1>
                    <p>Manage your account security and privacy settings</p>
                </div>
                <div class="page-actions">
                    <a href="../profile/" class="btn-outline">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                        Profile Settings
                    </a>
                    <a href="../support/" class="btn-primary">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                        </svg>
                        Get Help
                    </a>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Security Score Overview -->
            <div class="security-overview">
                <div class="security-score-card">
                    <div class="score-circle">
                        <div class="score-value"><?php echo $security_score; ?>%</div>
                        <div class="score-label">Security Score</div>
                    </div>
                    <div class="score-details">
                        <h3>Your Account Security</h3>
                        <p><?php 
                        if ($security_score >= 75) echo "Excellent! Your account is well protected.";
                        elseif ($security_score >= 50) echo "Good security, but there's room for improvement.";
                        else echo "Your account needs better security measures.";
                        ?></p>
                        <div class="security-checklist">
                            <?php foreach ($security_items as $item): ?>
                                <div class="checklist-item <?php echo $item['status']; ?>">
                                    <div class="item-icon">
                                        <?php if ($item['status'] === 'complete'): ?>
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                            </svg>
                                        <?php else: ?>
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                            </svg>
                                        <?php endif; ?>
                                    </div>
                                    <span class="item-text"><?php echo $item['item']; ?></span>
                                    <span class="item-points">+<?php echo $item['points']; ?>%</span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
