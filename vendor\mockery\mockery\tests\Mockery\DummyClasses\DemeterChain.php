<?php
/**
 * Mockery
 *
 * LICENSE
 *
 * This source file is subject to the new BSD license that is bundled
 * with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://github.com/padraic/mockery/master/LICENSE
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category   Mockery
 * @package    Mockery
 * @subpackage UnitTests
 * @copyright  Copyright (c) 2010 <PERSON><PERSON><PERSON><PERSON> (http://blog.astrumfutura.com)
 * @license    http://github.com/padraic/mockery/blob/master/LICENSE New BSD License
 */

namespace DemeterChain;

class C
{
    public function baz(): \stdClass
    {
        return new \stdClass();
    }
}

class B
{
    public function bar(): C
    {
        return new C();
    }

    public function qux(): C
    {
        return new C();
    }
}

class A
{
    public function foo(): B
    {
        return new B();
    }
}

class Main
{
    public function callDemeter(A $a)
    {
        return $a->foo()->bar()->baz();
    }
}
