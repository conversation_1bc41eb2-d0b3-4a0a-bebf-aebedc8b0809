<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$user_id = intval($_GET['user_id'] ?? 0);

if ($user_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
    exit;
}

try {
    $db = getDB();
    
    // Verify user exists
    $user_query = "SELECT id, first_name, last_name FROM accounts WHERE id = ? AND is_admin = 0";
    $user_result = $db->query($user_query, [$user_id]);
    
    if ($user_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Get OTP history for the user (last 20 entries)
    $otp_query = "SELECT otp_code, created_at, expires_at, used, source
                  FROM user_otps
                  WHERE user_id = ?
                  ORDER BY created_at DESC
                  LIMIT 20";
    
    $otp_result = $db->query($otp_query, [$user_id]);
    
    $history = [];
    if ($otp_result && $otp_result->num_rows > 0) {
        while ($row = $otp_result->fetch_assoc()) {
            $history[] = [
                'otp_code' => $row['otp_code'],
                'created_at' => $row['created_at'],
                'expires_at' => $row['expires_at'],
                'used' => (bool)$row['used'],
                'source' => $row['source'] ?? 'login'
            ];
        }
    }
    
    echo json_encode([
        'success' => true,
        'history' => $history,
        'total_count' => count($history)
    ]);
    
} catch (Exception $e) {
    error_log("OTP history error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while fetching OTP history']);
}
?>
