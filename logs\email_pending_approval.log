=== EMAIL LOG ===
Date: 2025-06-05 09:07:03
To: <EMAIL>
Subject: Registration Received - Pending Admin Approval
Type: pending_approval
Body (HTML):

    <!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <meta http-equiv='X-UA-Compatible' content='ie=edge'>
        <title>Registration Pending Approval - SecureBank Pro - Complete Banking Solution</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
            
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                line-height: 1.6; color: #374151; background-color: #f9fafb; margin: 0; padding: 0;
            }
            .email-container { 
                max-width: 600px; margin: 0 auto; background-color: #ffffff; 
                box-shadow: 0 10px 25px rgba(0,0,0,0.1); border-radius: 12px; overflow: hidden;
            }
            .header { 
                background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); 
                color: white; padding: 40px 30px; text-align: center; position: relative;
            }
            .header::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            }
            .header-content { position: relative; z-index: 1; }
            .bank-logo { 
                font-size: 28px; font-weight: 700; margin-bottom: 10px; 
                text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            .header-title { 
                font-size: 24px; font-weight: 600; margin-bottom: 8px; 
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .header-subtitle { 
                font-size: 16px; opacity: 0.9; font-weight: 400; 
            }
            .content { padding: 40px 30px; }
            .content h2 { color: #1f2937; margin-bottom: 20px; font-size: 20px; font-weight: 600; }
            .content h3 { color: #374151; margin: 25px 0 15px 0; font-size: 18px; font-weight: 500; }
            .content p { margin-bottom: 16px; color: #4b5563; line-height: 1.7; }
            .content ul, .content ol { margin: 16px 0; padding-left: 24px; color: #4b5563; }
            .content li { margin-bottom: 8px; line-height: 1.6; }
            
            .info-box {
                background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
                border: 1px solid #bfdbfe; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.08);
                position: relative; overflow: hidden;
            }
            .info-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #1e40af 0%, #60a5fa 100%);
            }
            .success-box {
                background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
                border: 1px solid #a7f3d0; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(5, 150, 105, 0.08);
                position: relative; overflow: hidden;
            }
            .success-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #059669 0%, #34d399 100%);
            }
            .warning-box {
                background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
                border: 1px solid #fed7aa; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(217, 119, 6, 0.08);
                position: relative; overflow: hidden;
            }
            .warning-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #d97706 0%, #fbbf24 100%);
            }
            .danger-box {
                background: linear-gradient(135deg, #fef2f2 0%, #fef1f1 100%);
                border: 1px solid #fecaca; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(220, 38, 38, 0.08);
                position: relative; overflow: hidden;
            }
            .danger-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #dc2626 0%, #f87171 100%);
            }
            
            .account-details { 
                background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; 
                padding: 24px; margin: 24px 0; 
            }
            .detail-row { 
                display: flex; justify-content: space-between; align-items: center; 
                padding: 12px 0; border-bottom: 1px solid #e5e7eb; 
            }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: 500; color: #6b7280; }
            .detail-value { font-weight: 600; color: #1f2937; }
            .highlight { 
                background: #1e40af; color: white; padding: 4px 12px; 
                border-radius: 6px; font-weight: 600; font-size: 14px;
            }
            
            .footer { 
                background: #f8fafc; padding: 30px; text-align: center; 
                border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; 
            }
            .footer-links { margin: 20px 0; }
            .footer-links a { 
                color: #1e40af; text-decoration: none; margin: 0 15px; 
                font-weight: 500; 
            }
            .footer-links a:hover { text-decoration: underline; }
            
            @media (max-width: 600px) {
                .email-container { margin: 0; border-radius: 0; }
                .header, .content, .footer { padding: 30px 20px; }
                .detail-row { flex-direction: column; align-items: flex-start; }
                .detail-label { margin-bottom: 4px; }
                .bank-logo { font-size: 24px; }
                .header-title { font-size: 20px; }
            }
        </style>
    </head>
    <body>
        <div style='background-color: #f9fafb; padding: 20px 0; min-height: 100vh;'>
            <div class='email-container'>
                <div class='header'>
                    <div class='header-content'>
                        <div class='bank-logo'>🏦 SecureBank Pro - Complete Banking Solution</div>
                        <div class='header-title'>Registration Received - Pending Approval</div>
                        <div class='header-subtitle'>Secure • Reliable • Professional</div>
                    </div>
                </div>
                
                <div class='content'>
                    
    <div class='info-box'>
        <h2 style='color: #3b82f6; margin: 0 0 15px 0;'>⏳ Registration Received Successfully!</h2>
        <p style='margin: 0; font-size: 16px;'>Thank you for registering with SecureBank Pro - Complete Banking Solution. Your account has been created and is currently pending admin approval.</p>
    </div>

    <div class='account-summary'>
        <h3 style='color: #1e40af; margin: 0 0 15px 0;'>📋 Registration Details</h3>
        <div class='detail-row'>
            <span class='detail-label'>Full Name:</span>
            <span class='detail-value'>Demo User</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Username:</span>
            <span class='detail-value'>demohomexx</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Email:</span>
            <span class='detail-value'><EMAIL></span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Status:</span>
            <span class='detail-value' style='color: #d97706; font-weight: 600;'>⏳ Pending Approval</span>
        </div>
    </div>

    <div class='warning-box'>
        <h3 style='color: #d97706; margin: 0 0 15px 0;'>⚠️ What Happens Next?</h3>
        <ul style='margin: 0; color: #92400e;'>
            <li>Our admin team will review your registration within 24-48 hours</li>
            <li>You'll receive an email notification once your account is approved</li>
            <li>You cannot log in until your account is activated</li>
            <li>No action is required from you at this time</li>
        </ul>
    </div>

    <div class='info-box'>
        <h3 style='color: #1e40af; margin: 0 0 15px 0;'>📞 Need Help?</h3>
        <p style='margin: 0; color: #1e3a8a;'>If you have any questions about your registration:</p>
        <ul style='margin: 10px 0 0 0; color: #1e3a8a;'>
            <li>📧 Email: <EMAIL></li>
            <li>📞 Phone: 1-800-BANKING (24/7)</li>
            <li>💬 Live chat available on our website</li>
        </ul>
    </div>
                    
                </div>
                
                <div class='footer'>
                    <div style='margin-bottom: 20px;'>
                        <strong>SecureBank Pro - Complete Banking Solution</strong><br>
                        Your trusted financial partner - Now with improved email templates!
                    </div>

                    <div class='footer-links'>
                        <a href='http://localhost/online_banking'>Online Banking</a>
                        <a href='http://localhost/online_banking/help-center.php'>Support Center</a>
                        <a href='mailto:<EMAIL>'>Contact Us</a>
                        <a href='mailto:<EMAIL>'>Security</a>
                    </div>
                    
                    <div style='margin: 20px 0; padding: 15px; background: #f1f5f9; border-radius: 6px; font-style: italic;'>This email confirms your registration. Please keep it for your records.</div>
                    
                    <div style='margin-top: 20px; font-size: 12px; color: #9ca3af;'>
                        <p>This email was sent from a secure, monitored system. Please do not reply to this email.</p>
                        <p>&copy; 2025 SecureBank Pro - Complete Banking Solution. All rights reserved.</p>
                        <p>If you have questions, please contact our support team.</p>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
Body (Text):

    
    
    
        
        
        
        Registration Pending Approval - SecureBank Pro - Complete Banking Solution
        
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
            
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                line-height: 1.6; color: #374151; background-color: #f9fafb; margin: 0; padding: 0;
            }
            .email-container { 
                max-width: 600px; margin: 0 auto; background-color: #ffffff; 
                box-shadow: 0 10px 25px rgba(0,0,0,0.1); border-radius: 12px; overflow: hidden;
            }
            .header { 
                background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); 
                color: white; padding: 40px 30px; text-align: center; position: relative;
            }
            .header::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                background: url('data:image/svg+xml,') repeat;
            }
            .header-content { position: relative; z-index: 1; }
            .bank-logo { 
                font-size: 28px; font-weight: 700; margin-bottom: 10px; 
                text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            .header-title { 
                font-size: 24px; font-weight: 600; margin-bottom: 8px; 
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .header-subtitle { 
                font-size: 16px; opacity: 0.9; font-weight: 400; 
            }
            .content { padding: 40px 30px; }
            .content h2 { color: #1f2937; margin-bottom: 20px; font-size: 20px; font-weight: 600; }
            .content h3 { color: #374151; margin: 25px 0 15px 0; font-size: 18px; font-weight: 500; }
            .content p { margin-bottom: 16px; color: #4b5563; line-height: 1.7; }
            .content ul, .content ol { margin: 16px 0; padding-left: 24px; color: #4b5563; }
            .content li { margin-bottom: 8px; line-height: 1.6; }
            
            .info-box {
                background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
                border: 1px solid #bfdbfe; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.08);
                position: relative; overflow: hidden;
            }
            .info-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #1e40af 0%, #60a5fa 100%);
            }
            .success-box {
                background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
                border: 1px solid #a7f3d0; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(5, 150, 105, 0.08);
                position: relative; overflow: hidden;
            }
            .success-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #059669 0%, #34d399 100%);
            }
            .warning-box {
                background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
                border: 1px solid #fed7aa; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(217, 119, 6, 0.08);
                position: relative; overflow: hidden;
            }
            .warning-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #d97706 0%, #fbbf24 100%);
            }
            .danger-box {
                background: linear-gradient(135deg, #fef2f2 0%, #fef1f1 100%);
                border: 1px solid #fecaca; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(220, 38, 38, 0.08);
                position: relative; overflow: hidden;
            }
            .danger-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, #dc2626 0%, #f87171 100%);
            }
            
            .account-details { 
                background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; 
                padding: 24px; margin: 24px 0; 
            }
            .detail-row { 
                display: flex; justify-content: space-between; align-items: center; 
                padding: 12px 0; border-bottom: 1px solid #e5e7eb; 
            }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: 500; color: #6b7280; }
            .detail-value { font-weight: 600; color: #1f2937; }
            .highlight { 
                background: #1e40af; color: white; padding: 4px 12px; 
                border-radius: 6px; font-weight: 600; font-size: 14px;
            }
            
            .footer { 
                background: #f8fafc; padding: 30px; text-align: center; 
                border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; 
            }
            .footer-links { margin: 20px 0; }
            .footer-links a { 
                color: #1e40af; text-decoration: none; margin: 0 15px; 
                font-weight: 500; 
            }
            .footer-links a:hover { text-decoration: underline; }
            
            @media (max-width: 600px) {
                .email-container { margin: 0; border-radius: 0; }
                .header, .content, .footer { padding: 30px 20px; }
                .detail-row { flex-direction: column; align-items: flex-start; }
                .detail-label { margin-bottom: 4px; }
                .bank-logo { font-size: 24px; }
                .header-title { font-size: 20px; }
            }
        
    
    
        
            
                
                    
                        🏦 SecureBank Pro - Complete Banking Solution
                        Registration Received - Pending Approval
                        Secure • Reliable • Professional
                    
                
                
                
                    
    
        ⏳ Registration Received Successfully!
        Thank you for registering with SecureBank Pro - Complete Banking Solution. Your account has been created and is currently pending admin approval.
    

    
        📋 Registration Details
        
            Full Name:
            Demo User
        
        
            Username:
            demohomexx
        
        
            Email:
            <EMAIL>
        
        
            Status:
            ⏳ Pending Approval
        
    

    
        ⚠️ What Happens Next?
        
            Our admin team will review your registration within 24-48 hours
            You'll receive an email notification once your account is approved
            You cannot log in until your account is activated
            No action is required from you at this time
        
    

    
        📞 Need Help?
        If you have any questions about your registration:
        
            📧 Email: <EMAIL>
            📞 Phone: 1-800-BANKING (24/7)
            💬 Live chat available on our website
        
    
                    
                
                
                
                    
                        SecureBank Pro - Complete Banking Solution
                        Your trusted financial partner - Now with improved email templates!
                    

                    
                        Online Banking
                        Support Center
                        Contact Us
                        Security
                    
                    
                    This email confirms your registration. Please keep it for your records.
                    
                    
                        This email was sent from a secure, monitored system. Please do not reply to this email.
                        &copy; 2025 SecureBank Pro - Complete Banking Solution. All rights reserved.
                        If you have questions, please contact our support team.
                    
                
            
        
    
    
==================

