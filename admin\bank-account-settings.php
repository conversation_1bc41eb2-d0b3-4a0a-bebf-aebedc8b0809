<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Bank Account Settings';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        $bank_name = trim($_POST['bank_name']);
        $bank_address = trim($_POST['bank_address']);
        $account_number = trim($_POST['account_number']);
        $account_name = trim($_POST['account_name']);
        $swift_code = trim($_POST['swift_code']);
        $routing_number = trim($_POST['routing_number']);
        
        // Validate inputs
        if (empty($bank_name) || empty($bank_address) || empty($account_number) || 
            empty($account_name) || empty($swift_code) || empty($routing_number)) {
            throw new Exception("All fields are required.");
        }
        
        // Deactivate current active settings
        $deactivate_current = "UPDATE bank_account_settings SET is_active = 0 WHERE is_active = 1";
        $db->query($deactivate_current);
        
        // Insert new settings
        $insert_settings = "INSERT INTO bank_account_settings (
            bank_name, bank_address, account_number, account_name, swift_code, routing_number,
            created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $settings_id = $db->insert($insert_settings, [
            $bank_name, $bank_address, $account_number, $account_name, $swift_code, $routing_number,
            $_SESSION['user_id'], $_SESSION['user_id']
        ]);
        
        if ($settings_id) {
            $success = "✅ Bank account settings updated successfully!";
        } else {
            throw new Exception("Failed to update bank account settings.");
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get current active bank account settings
try {
    $db = getDB();

    // Check if table exists first
    $table_check = $db->query("SHOW TABLES LIKE 'bank_account_settings'");
    if ($table_check->num_rows == 0) {
        throw new Exception("Bank account settings table does not exist. Please run the setup script first.");
    }

    $settings_query = "SELECT bas.*,
                      created_admin.first_name as created_by_first_name, created_admin.last_name as created_by_last_name,
                      updated_admin.first_name as updated_by_first_name, updated_admin.last_name as updated_by_last_name
                      FROM bank_account_settings bas
                      LEFT JOIN accounts created_admin ON bas.created_by = created_admin.id
                      LEFT JOIN accounts updated_admin ON bas.updated_by = updated_admin.id
                      WHERE bas.is_active = 1
                      ORDER BY bas.created_at DESC
                      LIMIT 1";
    $settings_result = $db->query($settings_query);
    $current_settings = $settings_result ? $settings_result->fetch_assoc() : null;

    // Get settings history
    $history_query = "SELECT bas.*,
                     created_admin.first_name as created_by_first_name, created_admin.last_name as created_by_last_name,
                     updated_admin.first_name as updated_by_first_name, updated_admin.last_name as updated_by_last_name
                     FROM bank_account_settings bas
                     LEFT JOIN accounts created_admin ON bas.created_by = created_admin.id
                     LEFT JOIN accounts updated_admin ON bas.updated_by = updated_admin.id
                     ORDER BY bas.created_at DESC
                     LIMIT 10";
    $history_result = $db->query($history_query);
    $settings_history = [];
    if ($history_result) {
        while ($row = $history_result->fetch_assoc()) {
            $settings_history[] = $row;
        }
    }

} catch (Exception $e) {
    $error = "Failed to load bank account settings: " . $e->getMessage();
    $current_settings = null;
    $settings_history = [];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Bank Account Settings</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo $success; ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- Current Settings Display -->
    <div class="col-12">
        <?php if ($current_settings): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-university me-2"></i>
                    Current Bank Account Details
                </h3>
                <div class="card-actions">
                    <button type="button" class="btn btn-primary btn-sm" onclick="editBankSettings()">
                        <i class="fas fa-edit me-2"></i>
                        Edit Settings
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Bank Name:</dt>
                            <dd class="col-sm-7"><strong><?php echo htmlspecialchars($current_settings['bank_name']); ?></strong></dd>
                            
                            <dt class="col-sm-5">Bank Address:</dt>
                            <dd class="col-sm-7"><?php echo htmlspecialchars($current_settings['bank_address']); ?></dd>
                            
                            <dt class="col-sm-5">Account Number:</dt>
                            <dd class="col-sm-7"><code><?php echo htmlspecialchars($current_settings['account_number']); ?></code></dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Account Name:</dt>
                            <dd class="col-sm-7"><strong><?php echo htmlspecialchars($current_settings['account_name']); ?></strong></dd>
                            
                            <dt class="col-sm-5">SWIFT Code:</dt>
                            <dd class="col-sm-7"><code><?php echo htmlspecialchars($current_settings['swift_code']); ?></code></dd>
                            
                            <dt class="col-sm-5">Routing Number:</dt>
                            <dd class="col-sm-7"><code><?php echo htmlspecialchars($current_settings['routing_number']); ?></code></dd>
                        </dl>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>Last Updated:</strong> <?php echo date('M j, Y g:i A', strtotime($current_settings['updated_at'])); ?>
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>Updated By:</strong> <?php echo htmlspecialchars($current_settings['updated_by_first_name'] . ' ' . $current_settings['updated_by_last_name']); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="card mb-4">
            <div class="card-body">
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-university" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No Bank Account Settings</p>
                    <p class="empty-subtitle text-muted">
                        No bank account details have been configured yet.
                    </p>
                    <div class="empty-action">
                        <button type="button" class="btn btn-primary" onclick="editBankSettings()">
                            <i class="fas fa-plus me-2"></i>
                            Add Bank Account Details
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Edit Form (Hidden by default) -->
        <div class="card" id="editForm" style="display: none;">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-edit me-2"></i>
                    Update Bank Account Settings
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Bank Name <span class="text-danger">*</span></label>
                                <input type="text" name="bank_name" class="form-control" 
                                       placeholder="e.g., Homestar Credit Union" 
                                       value="<?php echo htmlspecialchars($current_settings['bank_name'] ?? ''); ?>" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Bank Address <span class="text-danger">*</span></label>
                                <input type="text" name="bank_address" class="form-control" 
                                       placeholder="e.g., Germany" 
                                       value="<?php echo htmlspecialchars($current_settings['bank_address'] ?? ''); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Bank Account Number <span class="text-danger">*</span></label>
                                <input type="text" name="account_number" class="form-control" 
                                       placeholder="e.g., *************" 
                                       value="<?php echo htmlspecialchars($current_settings['account_number'] ?? ''); ?>" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Account Name <span class="text-danger">*</span></label>
                                <input type="text" name="account_name" class="form-control" 
                                       placeholder="e.g., Williams Kelly" 
                                       value="<?php echo htmlspecialchars($current_settings['account_name'] ?? ''); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">SWIFT Code <span class="text-danger">*</span></label>
                                <input type="text" name="swift_code" class="form-control" 
                                       placeholder="e.g., UPNBUS44" 
                                       value="<?php echo htmlspecialchars($current_settings['swift_code'] ?? ''); ?>" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Routing Number <span class="text-danger">*</span></label>
                                <input type="text" name="routing_number" class="form-control" 
                                       placeholder="e.g., *********" 
                                       value="<?php echo htmlspecialchars($current_settings['routing_number'] ?? ''); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Save Bank Account Settings
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="cancelEdit()">
                            <i class="fas fa-times me-2"></i>
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>

    </div>
</div>

<!-- Settings History -->
<?php if (!empty($settings_history)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Settings History
                </h3>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th>Bank Name</th>
                                <th>Account Number</th>
                                <th>Account Name</th>
                                <th>SWIFT Code</th>
                                <th>Status</th>
                                <th>Updated By</th>
                                <th>Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($settings_history as $setting): ?>
                            <tr>
                                <td>
                                    <div class="fw-bold"><?php echo htmlspecialchars($setting['bank_name']); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($setting['bank_address']); ?></small>
                                </td>

                                <td>
                                    <code><?php echo htmlspecialchars($setting['account_number']); ?></code>
                                </td>

                                <td>
                                    <?php echo htmlspecialchars($setting['account_name']); ?>
                                </td>

                                <td>
                                    <code><?php echo htmlspecialchars($setting['swift_code']); ?></code>
                                </td>

                                <td>
                                    <?php if ($setting['is_active']): ?>
                                    <span class="badge bg-success badge-sm">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-secondary badge-sm">Inactive</span>
                                    <?php endif; ?>
                                </td>

                                <td>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($setting['updated_by_first_name'] . ' ' . $setting['updated_by_last_name']); ?>
                                    </small>
                                </td>

                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($setting['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($setting['created_at'])); ?></small>
                                    </div>
                                </td>

                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewSettingDetails(<?php echo htmlspecialchars(json_encode($setting)); ?>)" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Setting Details Modal -->
<div class="modal modal-blur fade" id="settingDetailsModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bank Account Setting Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="settingDetailsContent">
                <!-- Setting details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function editBankSettings() {
    document.getElementById('editForm').style.display = 'block';
    document.getElementById('editForm').scrollIntoView({ behavior: 'smooth' });
}

function cancelEdit() {
    document.getElementById('editForm').style.display = 'none';
}

function viewSettingDetails(settingData) {
    const setting = JSON.parse(settingData);

    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Bank Information</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-5">Bank Name:</dt>
                            <dd class="col-sm-7"><strong>${setting.bank_name}</strong></dd>

                            <dt class="col-sm-5">Bank Address:</dt>
                            <dd class="col-sm-7">${setting.bank_address}</dd>

                            <dt class="col-sm-5">Account Number:</dt>
                            <dd class="col-sm-7"><code>${setting.account_number}</code></dd>

                            <dt class="col-sm-5">Account Name:</dt>
                            <dd class="col-sm-7"><strong>${setting.account_name}</strong></dd>

                            <dt class="col-sm-5">SWIFT Code:</dt>
                            <dd class="col-sm-7"><code>${setting.swift_code}</code></dd>

                            <dt class="col-sm-5">Routing Number:</dt>
                            <dd class="col-sm-7"><code>${setting.routing_number}</code></dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Setting Information</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-5">Setting ID:</dt>
                            <dd class="col-sm-7">#${setting.id}</dd>

                            <dt class="col-sm-5">Status:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-${setting.is_active ? 'success' : 'secondary'}">
                                    ${setting.is_active ? 'Active' : 'Inactive'}
                                </span>
                            </dd>

                            <dt class="col-sm-5">Created By:</dt>
                            <dd class="col-sm-7">${setting.created_by_first_name} ${setting.created_by_last_name}</dd>

                            <dt class="col-sm-5">Created Date:</dt>
                            <dd class="col-sm-7">
                                <div>${formatDate(setting.created_at)}</div>
                                <small class="text-muted">${formatTime(setting.created_at)}</small>
                            </dd>

                            <dt class="col-sm-5">Last Updated By:</dt>
                            <dd class="col-sm-7">${setting.updated_by_first_name} ${setting.updated_by_last_name}</dd>

                            <dt class="col-sm-5">Last Updated:</dt>
                            <dd class="col-sm-7">
                                <div>${formatDate(setting.updated_at)}</div>
                                <small class="text-muted">${formatTime(setting.updated_at)}</small>
                                ${setting.updated_at !== setting.created_at ?
                                    '<small class="badge bg-warning ms-2">Modified</small>' : ''}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('settingDetailsContent').innerHTML = detailsHtml;
    const modal = new bootstrap.Modal(document.getElementById('settingDetailsModal'));
    modal.show();
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatTime(dateString) {
    return new Date(dateString).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}
</script>

<?php include 'includes/admin-footer.php'; ?>
