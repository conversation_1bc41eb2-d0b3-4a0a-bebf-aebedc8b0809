<?php
/**
 * Input Validation Class
 * Provides comprehensive input validation and sanitization for the banking system
 */

require_once 'ErrorHandler.php';

class InputValidator {
    
    private static $instance = null;
    private $errors = [];
    
    private function __construct() {}
    
    /**
     * Get singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Validate and sanitize input data
     * 
     * @param array $data Input data to validate
     * @param array $rules Validation rules
     * @return array Sanitized data or false if validation fails
     */
    public static function validate($data, $rules) {
        $instance = self::getInstance();
        $instance->errors = [];
        $sanitized = [];
        
        foreach ($rules as $field => $fieldRules) {
            $value = $data[$field] ?? null;
            $sanitizedValue = $instance->validateField($field, $value, $fieldRules);
            
            if ($sanitizedValue !== false) {
                $sanitized[$field] = $sanitizedValue;
            }
        }
        
        if (!empty($instance->errors)) {
            ErrorHandler::logError('Input validation failed', [
                'errors' => $instance->errors,
                'data_keys' => array_keys($data)
            ], 'WARNING');
            return false;
        }
        
        return $sanitized;
    }
    
    /**
     * Get validation errors
     */
    public static function getErrors() {
        $instance = self::getInstance();
        return $instance->errors;
    }
    
    /**
     * Validate individual field
     */
    private function validateField($field, $value, $rules) {
        $rulesArray = is_string($rules) ? explode('|', $rules) : $rules;
        $sanitizedValue = $value;
        
        foreach ($rulesArray as $rule) {
            $ruleParts = explode(':', $rule);
            $ruleName = $ruleParts[0];
            $ruleParam = $ruleParts[1] ?? null;
            
            switch ($ruleName) {
                case 'required':
                    if (empty($value) && $value !== '0') {
                        $this->addError($field, 'This field is required');
                        return false;
                    }
                    break;
                    
                case 'email':
                    if (!empty($value)) {
                        $sanitizedValue = filter_var($value, FILTER_SANITIZE_EMAIL);
                        if (!filter_var($sanitizedValue, FILTER_VALIDATE_EMAIL)) {
                            $this->addError($field, 'Invalid email format');
                            return false;
                        }
                    }
                    break;
                    
                case 'numeric':
                    if (!empty($value) && !is_numeric($value)) {
                        $this->addError($field, 'Must be a number');
                        return false;
                    }
                    $sanitizedValue = is_numeric($value) ? (float)$value : $value;
                    break;
                    
                case 'integer':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                        $this->addError($field, 'Must be an integer');
                        return false;
                    }
                    $sanitizedValue = is_numeric($value) ? (int)$value : $value;
                    break;
                    
                case 'min_length':
                    if (!empty($value) && strlen($value) < (int)$ruleParam) {
                        $this->addError($field, "Must be at least {$ruleParam} characters");
                        return false;
                    }
                    break;
                    
                case 'max_length':
                    if (!empty($value) && strlen($value) > (int)$ruleParam) {
                        $this->addError($field, "Must not exceed {$ruleParam} characters");
                        return false;
                    }
                    break;
                    
                case 'min_value':
                    if (!empty($value) && is_numeric($value) && (float)$value < (float)$ruleParam) {
                        $this->addError($field, "Must be at least {$ruleParam}");
                        return false;
                    }
                    break;
                    
                case 'max_value':
                    if (!empty($value) && is_numeric($value) && (float)$value > (float)$ruleParam) {
                        $this->addError($field, "Must not exceed {$ruleParam}");
                        return false;
                    }
                    break;
                    
                case 'phone':
                    if (!empty($value)) {
                        $sanitizedValue = $this->sanitizePhone($value);
                        if (!$this->validatePhone($sanitizedValue)) {
                            $this->addError($field, 'Invalid phone number format');
                            return false;
                        }
                    }
                    break;
                    
                case 'account_number':
                    if (!empty($value)) {
                        $sanitizedValue = preg_replace('/[^0-9]/', '', $value);
                        if (!$this->validateAccountNumber($sanitizedValue)) {
                            $this->addError($field, 'Invalid account number format');
                            return false;
                        }
                    }
                    break;
                    
                case 'currency':
                    if (!empty($value)) {
                        $sanitizedValue = strtoupper(trim($value));
                        if (!$this->validateCurrency($sanitizedValue)) {
                            $this->addError($field, 'Invalid currency code');
                            return false;
                        }
                    }
                    break;
                    
                case 'amount':
                    if (!empty($value)) {
                        $sanitizedValue = $this->sanitizeAmount($value);
                        if (!$this->validateAmount($sanitizedValue)) {
                            $this->addError($field, 'Invalid amount format');
                            return false;
                        }
                    }
                    break;
                    
                case 'alpha':
                    if (!empty($value) && !ctype_alpha(str_replace(' ', '', $value))) {
                        $this->addError($field, 'Must contain only letters');
                        return false;
                    }
                    $sanitizedValue = trim($value);
                    break;
                    
                case 'alphanumeric':
                    if (!empty($value) && !ctype_alnum(str_replace([' ', '-', '_'], '', $value))) {
                        $this->addError($field, 'Must contain only letters and numbers');
                        return false;
                    }
                    $sanitizedValue = trim($value);
                    break;
                    
                case 'date':
                    if (!empty($value)) {
                        $sanitizedValue = $this->sanitizeDate($value);
                        if (!$this->validateDate($sanitizedValue)) {
                            $this->addError($field, 'Invalid date format');
                            return false;
                        }
                    }
                    break;
                    
                case 'in':
                    $allowedValues = explode(',', $ruleParam);
                    if (!empty($value) && !in_array($value, $allowedValues)) {
                        $this->addError($field, 'Invalid value selected');
                        return false;
                    }
                    break;
                    
                case 'file_type':
                    if (!empty($value) && is_array($value)) {
                        $allowedTypes = explode(',', $ruleParam);
                        if (!$this->validateFileType($value, $allowedTypes)) {
                            $this->addError($field, 'Invalid file type');
                            return false;
                        }
                    }
                    break;
                    
                case 'file_size':
                    if (!empty($value) && is_array($value)) {
                        $maxSize = (int)$ruleParam * 1024 * 1024; // Convert MB to bytes
                        if (!$this->validateFileSize($value, $maxSize)) {
                            $this->addError($field, "File size must not exceed {$ruleParam}MB");
                            return false;
                        }
                    }
                    break;
                    
                case 'sanitize':
                    $sanitizedValue = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
                    break;
            }
        }
        
        return $sanitizedValue;
    }
    
    /**
     * Add validation error
     */
    private function addError($field, $message) {
        $this->errors[$field] = $message;
    }
    
    /**
     * Sanitize phone number
     */
    private function sanitizePhone($phone) {
        return preg_replace('/[^0-9+\-\(\)\s]/', '', $phone);
    }
    
    /**
     * Validate phone number
     */
    private function validatePhone($phone) {
        // Basic phone validation - adjust pattern as needed
        return preg_match('/^[\+]?[0-9\-\(\)\s]{10,20}$/', $phone);
    }
    
    /**
     * Validate account number
     */
    private function validateAccountNumber($accountNumber) {
        // Account numbers should be 10-20 digits
        return preg_match('/^[0-9]{10,20}$/', $accountNumber);
    }
    
    /**
     * Validate currency code
     */
    private function validateCurrency($currency) {
        $validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'CNY'];
        return in_array($currency, $validCurrencies);
    }
    
    /**
     * Sanitize amount
     */
    private function sanitizeAmount($amount) {
        return preg_replace('/[^0-9\.\-]/', '', $amount);
    }
    
    /**
     * Validate amount
     */
    private function validateAmount($amount) {
        return is_numeric($amount) && $amount >= 0;
    }
    
    /**
     * Sanitize date
     */
    private function sanitizeDate($date) {
        return trim($date);
    }
    
    /**
     * Validate date
     */
    private function validateDate($date) {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
    
    /**
     * Validate file type
     */
    private function validateFileType($file, $allowedTypes) {
        if (!isset($file['type']) || !isset($file['name'])) {
            return false;
        }
        
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        return in_array($fileExtension, $allowedTypes);
    }
    
    /**
     * Validate file size
     */
    private function validateFileSize($file, $maxSize) {
        return isset($file['size']) && $file['size'] <= $maxSize;
    }
    
    /**
     * Sanitize SQL input (for legacy code migration)
     */
    public static function sanitizeSQL($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeSQL'], $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Generate CSRF token
     */
    public static function generateCSRFToken() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        $_SESSION['csrf_token_time'] = time();
        
        return $token;
    }
    
    /**
     * Validate CSRF token
     */
    public static function validateCSRFToken($token) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }
        
        // Token expires after 1 hour
        if (time() - $_SESSION['csrf_token_time'] > 3600) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
}
?>
