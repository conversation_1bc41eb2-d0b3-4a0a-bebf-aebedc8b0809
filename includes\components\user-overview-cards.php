<?php
/**
 * User Overview Cards Component
 * Displays key user statistics in card format
 */

if (!isset($user) || !isset($user_stats)) {
    throw new Exception('User and user stats data required for overview cards component');
}

// Calculate cheque indicator
$has_cheques = (!empty($cheque_deposits) || $cheque_documents_count > 0);
$cheque_count = count($cheque_deposits ?? []) + ($cheque_documents_count ?? 0);
?>

<!-- User Overview Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-wallet"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php echo formatCurrency($user['balance'], $user['currency']); ?>
                        </div>
                        <div class="text-muted">Current Balance</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-arrow-up"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php echo formatCurrency($user_stats['total_credited'] ?? 0, $user['currency']); ?>
                        </div>
                        <div class="text-muted">Total Credited</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-danger text-white avatar">
                            <i class="fas fa-arrow-down"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php echo formatCurrency($user_stats['total_debited'] ?? 0, $user['currency']); ?>
                        </div>
                        <div class="text-muted">Total Debited</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-<?php echo $has_cheques ? 'warning' : 'info'; ?> text-white avatar">
                            <i class="fas fa-<?php echo $has_cheques ? 'money-check' : 'chart-line'; ?>"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php 
                            if ($has_cheques) {
                                echo $cheque_count;
                            } else {
                                echo number_format($user_stats['monthly_transactions'] ?? 0);
                            }
                            ?>
                        </div>
                        <div class="text-muted">
                            <?php 
                            if ($has_cheques) {
                                echo 'Cheque Activity';
                            } else {
                                echo 'Monthly Activity';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
