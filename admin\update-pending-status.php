<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Add Pending Status';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_database'])) {
    $success = false;
    $error = '';
    
    try {
        $db = getDB();
        
        // Check if pending status already exists
        $result = $db->query("SHOW COLUMNS FROM accounts LIKE 'status'");
        $column = $result->fetch_assoc();
        
        if (strpos($column['Type'], "'pending'") !== false) {
            $success = true;
            $message = "The 'pending' status already exists in the accounts table.";
        } else {
            // Modify the ENUM to include 'pending'
            $alterQuery = "ALTER TABLE accounts 
                           MODIFY status ENUM('active','suspended','closed','pending') 
                           DEFAULT 'pending' NOT NULL";
            
            $db->query($alterQuery);
            
            // Update existing accounts to keep their current status
            $updateQuery = "UPDATE accounts SET status = status";
            $db->query($updateQuery);
            
            $success = true;
            $message = "Database updated successfully! Added 'pending' status.";
        }
    } catch (Exception $e) {
        $error = "Database error: " . $e->getMessage();
    }
}

include '../includes/admin_header.php';
?>

<div class="page-wrapper">
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <div class="page-pretitle">Administration</div>
                    <h2 class="page-title">Add Pending Status</h2>
                </div>
            </div>
        </div>
    </div>
    
    <div class="page-body">
        <div class="container-xl">
            <div class="row row-cards">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Add Pending Status for User Registration</h3>
                        </div>
                        <div class="card-body">
                            <?php if (isset($success) && $success): ?>
                                <div class="alert alert-success">
                                    <h4><?php echo htmlspecialchars($message); ?></h4>
                                    <p>The accounts table now includes a 'pending' status for new registrations.</p>
                                </div>
                            <?php elseif (isset($error) && $error): ?>
                                <div class="alert alert-danger">
                                    <h4>Error</h4>
                                    <p><?php echo htmlspecialchars($error); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <p>This update will modify the accounts table to:</p>
                            <ul>
                                <li>Add a new 'pending' status to the status ENUM</li>
                                <li>Set default status to 'pending' for new registrations</li>
                                <li>Preserve existing account statuses</li>
                            </ul>
                            
                            <form method="POST" action="">
                                <button type="submit" name="update_database" class="btn btn-primary" 
                                        onclick="return confirm('Are you sure you want to update the database?')">
                                    <i class="fas fa-database me-2"></i>
                                    Update Database
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
