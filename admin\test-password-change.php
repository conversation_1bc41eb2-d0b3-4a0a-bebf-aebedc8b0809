<?php
require_once '../config/config.php';
requireAdmin();

$message = '';
$error = '';

// Test user ID (change this to the user you want to test)
$test_user_id = intval($_GET['user_id'] ?? 0);

if ($test_user_id <= 0) {
    $error = 'Please provide a valid user_id parameter in the URL (e.g., ?user_id=16)';
} else {
    try {
        $db = getDB();
        
        // Get current user data
        $user_query = "SELECT id, username, password, first_name, last_name FROM accounts WHERE id = ? AND is_admin = 0";
        $user_result = $db->query($user_query, [$test_user_id]);
        
        if ($user_result->num_rows === 0) {
            $error = "User with ID $test_user_id not found";
        } else {
            $user = $user_result->fetch_assoc();
            
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $new_password = $_POST['new_password'] ?? '';
                
                if (empty($new_password)) {
                    $error = 'Please enter a new password';
                } else {
                    // Test password hashing
                    $hashed_password = hashPassword($new_password);
                    
                    // Update password in database
                    $update_sql = "UPDATE accounts SET password = ? WHERE id = ?";
                    $db->query($update_sql, [$hashed_password, $test_user_id]);
                    
                    // Verify the update worked
                    $verify_query = "SELECT password FROM accounts WHERE id = ?";
                    $verify_result = $db->query($verify_query, [$test_user_id]);
                    $updated_hash = $verify_result->fetch_assoc()['password'];
                    
                    // Test password verification
                    $verification_test = verifyPassword($new_password, $updated_hash);
                    
                    $message = "
                        <h4>Password Change Test Results:</h4>
                        <p><strong>User:</strong> {$user['first_name']} {$user['last_name']} ({$user['username']})</p>
                        <p><strong>New Password:</strong> $new_password</p>
                        <p><strong>Password Hash:</strong> " . substr($hashed_password, 0, 50) . "...</p>
                        <p><strong>Database Update:</strong> ✅ Success</p>
                        <p><strong>Password Verification Test:</strong> " . ($verification_test ? '✅ PASSED' : '❌ FAILED') . "</p>
                        <hr>
                        <p><strong>Next Steps:</strong></p>
                        <ol>
                            <li>Try logging in as this user with the new password</li>
                            <li>Check if there are any session conflicts</li>
                            <li>Verify OTP functionality works with the new password</li>
                        </ol>
                    ";
                    
                    // Log the activity
                    logActivity($_SESSION['user_id'], 'Admin tested password change', 'accounts', $test_user_id, null, [
                        'test_password' => $new_password,
                        'verification_passed' => $verification_test
                    ]);
                }
            }
        }
        
    } catch (Exception $e) {
        $error = 'Database error: ' . $e->getMessage();
        error_log("Password test error: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Change Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>Password Change Test Tool</h3>
                        <p class="mb-0 text-muted">Test password change functionality for debugging</p>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <?php if ($message): ?>
                            <div class="alert alert-success"><?php echo $message; ?></div>
                        <?php endif; ?>
                        
                        <?php if ($test_user_id > 0 && isset($user)): ?>
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">Current User</label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name'] . ' (' . $user['username'] . ')'); ?>" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">New Password</label>
                                    <input type="text" class="form-control" id="new_password" name="new_password" placeholder="Enter new password to test" required>
                                    <div class="form-text">This will actually change the user's password in the database</div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">Test Password Change</button>
                                <a href="view-user.php?id=<?php echo $test_user_id; ?>" class="btn btn-secondary">Back to User</a>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <h5>How to use this tool:</h5>
                                <ol>
                                    <li>Add <code>?user_id=X</code> to the URL (replace X with actual user ID)</li>
                                    <li>Enter a new password to test</li>
                                    <li>The tool will update the password and verify it works</li>
                                    <li>Try logging in as that user to confirm</li>
                                </ol>
                                <p><strong>Example:</strong> <code>test-password-change.php?user_id=16</code></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
