<?php
/**
 * Session Integration Test Suite
 * Tests session functionality within the context of the banking application
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/SessionManager.php';
require_once __DIR__ . '/../../config/database.php';

class SessionIntegrationTest {
    
    private $db;
    private $testResults = [];
    private $testUserId = null;
    private $testAdminId = null;
    
    public function __construct() {
        $this->db = getDB();
        echo "<h1>Session Integration Test Suite</h1>\n";
        echo "<style>
            .test-pass { color: green; font-weight: bold; }
            .test-fail { color: red; font-weight: bold; }
            .test-info { color: blue; }
            .test-section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
            .test-result { margin: 5px 0; padding: 5px; }
            .integration-note { background: #e6f3ff; padding: 10px; margin: 10px 0; border-left: 4px solid #0066cc; }
        </style>\n";
    }
    
    private function logTest($description, $passed, $details = '') {
        $status = $passed ? 'PASS' : 'FAIL';
        $class = $passed ? 'test-pass' : 'test-fail';
        
        echo "<div class='test-result'>";
        echo "<span class='$class'>[$status]</span> $description";
        if ($details) {
            echo " <span class='test-info'>($details)</span>";
        }
        echo "</div>\n";
        
        $this->testResults[] = [
            'description' => $description,
            'passed' => $passed,
            'details' => $details
        ];
    }
    
    /**
     * Create test users for integration testing
     */
    private function createTestUsers() {
        try {
            // Create regular user
            $userAccountNumber = generateAccountNumber();
            $userSql = "INSERT INTO accounts (account_number, username, password, email, first_name, last_name, 
                       balance, status, kyc_status, is_admin) 
                       VALUES (?, ?, ?, ?, 'Integration', 'User', 1000.00, 'active', 'verified', 0)";
            
            $userResult = $this->db->query($userSql, [
                $userAccountNumber,
                'integration_user_' . time(),
                hashPassword('TestPassword123!'),
                'integrationuser' . time() . '@test.com'
            ]);
            
            if ($userResult) {
                $this->testUserId = $this->db->getConnection()->insert_id;
            }
            
            // Create admin user
            $adminAccountNumber = generateAccountNumber();
            $adminSql = "INSERT INTO accounts (account_number, username, password, email, first_name, last_name, 
                        balance, status, kyc_status, is_admin) 
                        VALUES (?, ?, ?, ?, 'Integration', 'Admin', 0.00, 'active', 'verified', 1)";
            
            $adminResult = $this->db->query($adminSql, [
                $adminAccountNumber,
                'integration_admin_' . time(),
                hashPassword('AdminPassword123!'),
                'integrationadmin' . time() . '@test.com'
            ]);
            
            if ($adminResult) {
                $this->testAdminId = $this->db->getConnection()->insert_id;
            }
            
            $this->logTest("Test users created", 
                $this->testUserId && $this->testAdminId,
                "User ID: {$this->testUserId}, Admin ID: {$this->testAdminId}");
            
            return true;
            
        } catch (Exception $e) {
            $this->logTest("Failed to create test users: " . $e->getMessage(), false);
            return false;
        }
    }
    
    /**
     * Clean up test users
     */
    private function cleanupTestUsers() {
        try {
            if ($this->testUserId) {
                $this->db->query("DELETE FROM accounts WHERE id = ?", [$this->testUserId]);
            }
            if ($this->testAdminId) {
                $this->db->query("DELETE FROM accounts WHERE id = ?", [$this->testAdminId]);
            }
            $this->logTest("Test users cleaned up", true);
        } catch (Exception $e) {
            $this->logTest("Error cleaning up test users: " . $e->getMessage(), false);
        }
    }
    
    /**
     * Test user login flow integration
     */
    public function testUserLoginFlow() {
        echo "<div class='test-section'><h2>User Login Flow Integration</h2>";
        
        if (!$this->testUserId) {
            $this->logTest("Cannot test login flow - no test user", false);
            echo "</div>";
            return;
        }
        
        // Test user login with database integration
        $sql = "SELECT id, username, password, first_name, last_name, email, account_number, 
                       balance, status, is_admin, kyc_status FROM accounts WHERE id = ?";
        $result = $this->db->query($sql, [$this->testUserId]);
        
        if ($result && $result->num_rows === 1) {
            $user = $result->fetch_assoc();
            
            // Simulate login process
            SessionManager::login($user['id'], 'user', [
                'username' => $user['username'],
                'first_name' => $user['first_name'],
                'last_name' => $user['last_name'],
                'email' => $user['email'],
                'account_number' => $user['account_number'],
                'balance' => $user['balance'],
                'kyc_status' => $user['kyc_status']
            ]);
            
            $this->logTest("User login successful", SessionManager::isLoggedIn());
            
            // Test session data matches database
            $this->logTest("Session user ID matches database", 
                SessionManager::get('user_id') == $user['id']);
            
            $this->logTest("Session username matches database", 
                SessionManager::get('username') === $user['username']);
            
            $this->logTest("Session balance matches database", 
                SessionManager::get('balance') == $user['balance']);
            
            // Test user access functions
            $this->logTest("isLoggedIn() returns true", isLoggedIn());
            $this->logTest("isUserLoggedIn() returns true", isUserLoggedIn());
            $this->logTest("isAdmin() returns false for user", !isAdmin());
            
        } else {
            $this->logTest("Failed to retrieve test user from database", false);
        }
        
        echo "</div>";
    }
    
    /**
     * Test admin login flow integration
     */
    public function testAdminLoginFlow() {
        echo "<div class='test-section'><h2>Admin Login Flow Integration</h2>";
        
        if (!$this->testAdminId) {
            $this->logTest("Cannot test admin login flow - no test admin", false);
            echo "</div>";
            return;
        }
        
        // Clear previous session
        SessionManager::logout();
        
        // Test admin login
        $sql = "SELECT id, username, password, first_name, last_name, email, account_number, 
                       balance, status, is_admin, kyc_status FROM accounts WHERE id = ?";
        $result = $this->db->query($sql, [$this->testAdminId]);
        
        if ($result && $result->num_rows === 1) {
            $admin = $result->fetch_assoc();
            
            // Simulate admin login
            SessionManager::login($admin['id'], 'admin', [
                'username' => $admin['username'],
                'first_name' => $admin['first_name'],
                'last_name' => $admin['last_name'],
                'email' => $admin['email'],
                'account_number' => $admin['account_number'],
                'is_admin' => true,
                'is_admin_session' => true
            ]);
            
            $this->logTest("Admin login successful", SessionManager::isLoggedIn());
            
            // Test admin-specific session data
            $this->logTest("Admin flag set in session", 
                SessionManager::get('is_admin') === true);
            
            $this->logTest("Admin session flag set", 
                SessionManager::get('is_admin_session') === true);
            
            // Test admin access functions
            $this->logTest("isAdmin() returns true for admin", SessionManager::isAdmin());
            $this->logTest("isAdminLoggedIn() returns true", isAdminLoggedIn());
            
        } else {
            $this->logTest("Failed to retrieve test admin from database", false);
        }
        
        echo "</div>";
    }
    
    /**
     * Test OTP session integration
     */
    public function testOTPSessionIntegration() {
        echo "<div class='test-section'><h2>OTP Session Integration</h2>";
        
        // Clear session and simulate OTP flow
        session_destroy();
        session_start();
        
        // Simulate OTP pending state
        $_SESSION['otp_user_id'] = $this->testUserId;
        $_SESSION['otp_pending'] = true;
        $_SESSION['otp_email'] = '<EMAIL>';
        
        $this->logTest("OTP session variables set", 
            isset($_SESSION['otp_user_id']) && 
            isset($_SESSION['otp_pending']) && 
            isset($_SESSION['otp_email']));
        
        // Test that user is not logged in during OTP
        $this->logTest("User not logged in during OTP", !isLoggedIn());
        
        // Simulate OTP verification and complete login
        if ($this->testUserId) {
            $sql = "SELECT * FROM accounts WHERE id = ?";
            $result = $this->db->query($sql, [$this->testUserId]);
            
            if ($result && $result->num_rows === 1) {
                $user = $result->fetch_assoc();
                
                // Clear OTP session and set user session
                unset($_SESSION['otp_user_id'], $_SESSION['otp_pending'], $_SESSION['otp_email']);
                
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['first_name'] = $user['first_name'];
                $_SESSION['last_name'] = $user['last_name'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['account_number'] = $user['account_number'];
                $_SESSION['balance'] = $user['balance'];
                $_SESSION['is_admin'] = (bool)$user['is_admin'];
                $_SESSION['kyc_status'] = $user['kyc_status'];
                $_SESSION['last_activity'] = time();
                
                $this->logTest("OTP verification completed and user logged in", isLoggedIn());
                
                $this->logTest("OTP session variables cleared", 
                    !isset($_SESSION['otp_user_id']) && 
                    !isset($_SESSION['otp_pending']) && 
                    !isset($_SESSION['otp_email']));
            }
        }
        
        echo "</div>";
    }
    
    /**
     * Test session timeout with database operations
     */
    public function testSessionTimeoutWithDatabase() {
        echo "<div class='test-section'><h2>Session Timeout with Database Operations</h2>";
        
        // Set short timeout
        SessionManager::setTimeout(2);
        SessionManager::login($this->testUserId, 'user');
        
        $this->logTest("User logged in for timeout test", SessionManager::isLoggedIn());
        
        // Simulate database operation
        $sql = "SELECT balance FROM accounts WHERE id = ?";
        $result = $this->db->query($sql, [$this->testUserId]);
        
        $this->logTest("Database operation successful while logged in", 
            $result && $result->num_rows === 1);
        
        // Wait for timeout
        sleep(3);
        
        // Test session validation after timeout
        $sessionInfo = SessionManager::getSessionInfo();
        $timeRemaining = $sessionInfo['time_remaining'] ?? 0;
        
        $this->logTest("Session timeout detected", $timeRemaining <= 0);
        
        echo "</div>";
    }
    
    /**
     * Test session with transaction operations
     */
    public function testSessionWithTransactions() {
        echo "<div class='test-section'><h2>Session with Transaction Operations</h2>";
        
        SessionManager::login($this->testUserId, 'user');
        
        // Test session persistence during transaction
        $this->db->getConnection()->begin_transaction();
        
        try {
            // Simulate transaction creation
            $transactionId = generateTransactionId();
            $sql = "INSERT INTO transfers (transaction_id, sender_id, amount, currency, status) 
                    VALUES (?, ?, 100.00, 'USD', 'pending')";
            
            $result = $this->db->query($sql, [$transactionId, $this->testUserId]);
            
            $this->logTest("Transaction created while session active", $result !== false);
            
            // Test session still valid during transaction
            $this->logTest("Session valid during database transaction", SessionManager::isLoggedIn());
            
            $this->db->getConnection()->commit();
            
            // Clean up transaction
            $this->db->query("DELETE FROM transfers WHERE transaction_id = ?", [$transactionId]);
            
        } catch (Exception $e) {
            $this->db->getConnection()->rollback();
            $this->logTest("Transaction test failed: " . $e->getMessage(), false);
        }
        
        echo "</div>";
    }
    
    /**
     * Run all integration tests
     */
    public function runAllTests() {
        echo "<h2>Starting Session Integration Tests...</h2>";
        
        echo "<div class='integration-note'>";
        echo "<strong>Note:</strong> These tests verify session functionality within the banking application context, ";
        echo "including database integration, user flows, and real-world scenarios.";
        echo "</div>";
        
        if (!$this->createTestUsers()) {
            echo "<div class='test-fail'>Cannot proceed with integration tests - test user creation failed</div>";
            return;
        }
        
        $this->testUserLoginFlow();
        $this->testAdminLoginFlow();
        $this->testOTPSessionIntegration();
        $this->testSessionTimeoutWithDatabase();
        $this->testSessionWithTransactions();
        
        $this->cleanupTestUsers();
        $this->displaySummary();
    }
    
    /**
     * Display test summary
     */
    private function displaySummary() {
        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, function($test) {
            return $test['passed'];
        }));
        $failedTests = $totalTests - $passedTests;
        
        echo "<div class='test-section'>";
        echo "<h2>Integration Test Summary</h2>";
        echo "<p><strong>Total Tests:</strong> $totalTests</p>";
        echo "<p class='test-pass'><strong>Passed:</strong> $passedTests</p>";
        echo "<p class='test-fail'><strong>Failed:</strong> $failedTests</p>";
        echo "<p><strong>Integration Score:</strong> " . round(($passedTests / $totalTests) * 100, 2) . "%</p>";
        echo "</div>";
        
        if ($failedTests > 0) {
            echo "<div class='test-section'>";
            echo "<h3>Failed Integration Tests:</h3>";
            foreach ($this->testResults as $test) {
                if (!$test['passed']) {
                    echo "<div class='test-fail'>• {$test['description']}</div>";
                }
            }
            echo "</div>";
        }
    }
}

// Only run if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'session_integration_test.php') {
    $integrationTest = new SessionIntegrationTest();
    $integrationTest->runAllTests();
}
?>
