<?php
/**
 * Email Test Handler
 * Handles AJAX requests for email testing
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../../config/config.php';
require_once '../../config/email.php';
require_once '../../config/ErrorHandler.php';
require_once '../../config/AuditLogger.php';

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['type']) || !isset($input['email'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid request data']);
    exit;
}

$type = $input['type'];
$email = $input['email'];
$user_name = $input['user_name'] ?? 'Test User';
$user_id = $input['user_id'] ?? 999;

try {
    $result = false;
    $error_message = '';
    
    switch ($type) {
        case 'welcome':
            $result = sendWelcomeTestEmail($email, $user_name);
            break;
            
        case 'otp':
            $result = sendOTPTestEmail($email, $user_name);
            break;
            
        case 'password_reset':
            $result = sendPasswordResetTestEmail($email, $user_name);
            break;
            
        case 'suspension':
            $result = sendSuspensionTestEmail($email, $user_name);
            break;
            
        case 'deletion':
            $result = sendDeletionTestEmail($email, $user_name);
            break;
            
        case 'transaction':
            $result = sendTransactionTestEmail($email, $user_name);
            break;
            
        case 'security':
            $result = sendSecurityTestEmail($email, $user_name);
            break;
            
        case 'smtp_test':
            $result = sendSMTPTestEmail($email);
            break;
            
        default:
            throw new Exception('Unknown email type: ' . $type);
    }
    
    // Log the test
    AuditLogger::log(AuditLogger::ACTION_ADMIN, "Email test sent: {$type}", [
        'resource_type' => 'email_test',
        'resource_id' => $type,
        'additional_data' => [
            'recipient' => $email,
            'success' => $result,
            'test_type' => $type
        ]
    ]);
    
    if ($result) {
        echo json_encode(['success' => true, 'message' => 'Email sent successfully']);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to send email']);
    }
    
} catch (Exception $e) {
    ErrorHandler::logError('Email test failed', [
        'type' => $type,
        'email' => $email,
        'error' => $e->getMessage()
    ], 'ERROR');
    
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}

/**
 * Send welcome test email
 */
function sendWelcomeTestEmail($email, $user_name) {
    $subject = "Welcome to " . getBankName() . " - Test Email";
    
    $message = generateBankingEmailTemplate(
        "Welcome to Our Banking System!",
        "🎉 Account Created Successfully",
        "
        <p>Dear {$user_name},</p>
        <p>Welcome to " . getBankName() . "! Your account has been successfully created and is ready to use.</p>
        <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h4 style='color: #28a745; margin: 0 0 10px 0;'>✅ Account Details</h4>
            <p><strong>Account Number:</strong> TEST123456789</p>
            <p><strong>Account Type:</strong> Savings Account</p>
            <p><strong>Initial Balance:</strong> $0.00</p>
        </div>
        <p><strong>Next Steps:</strong></p>
        <ul>
            <li>Log in to your account using your credentials</li>
            <li>Complete your profile information</li>
            <li>Set up your security preferences</li>
            <li>Explore our banking services</li>
        </ul>
        <p>If you have any questions, please don't hesitate to contact our customer support team.</p>
        ",
        "This is a test email sent from the email testing system."
    );
    
    return sendEmail($email, $subject, $message, true);
}

/**
 * Send OTP test email
 */
function sendOTPTestEmail($email, $user_name) {
    $otp_code = sprintf('%06d', mt_rand(100000, 999999));
    
    $subject = "Your Login Verification Code - " . getBankName();
    
    $message = generateBankingEmailTemplate(
        "Login Verification Code",
        "🔐 Your OTP Code",
        "
        <p>Dear {$user_name},</p>
        <p>You have requested to log in to your " . getBankName() . " account. Please use the verification code below:</p>
        <div style='background: #007bff; color: white; padding: 30px; border-radius: 8px; text-align: center; margin: 20px 0;'>
            <h2 style='margin: 0; font-size: 2.5rem; letter-spacing: 5px;'>{$otp_code}</h2>
        </div>
        <p><strong>Important Security Information:</strong></p>
        <ul>
            <li>This code will expire in 10 minutes</li>
            <li>Do not share this code with anyone</li>
            <li>Our staff will never ask for this code</li>
            <li>If you didn't request this code, please contact us immediately</li>
        </ul>
        ",
        "This is a test OTP email. The code above is for testing purposes only."
    );
    
    return sendEmail($email, $subject, $message, true);
}

/**
 * Send password reset test email
 */
function sendPasswordResetTestEmail($email, $user_name) {
    $reset_token = bin2hex(random_bytes(32));
    
    $subject = "Password Reset Request - " . getBankName();
    
    $message = generateBankingEmailTemplate(
        "Password Reset Request",
        "🔒 Reset Your Password",
        "
        <p>Dear {$user_name},</p>
        <p>We received a request to reset your password for your " . getBankName() . " account.</p>
        <div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ Security Notice</h4>
            <p style='margin: 0;'>If you did not request this password reset, please ignore this email and contact our security team immediately.</p>
        </div>
        <p>To reset your password, click the button below:</p>
        <div style='text-align: center; margin: 30px 0;'>
            <a href='#' style='background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Reset Password</a>
        </div>
        <p><strong>Security Information:</strong></p>
        <ul>
            <li>This link will expire in 1 hour</li>
            <li>You can only use this link once</li>
            <li>Reset Token: <code>{$reset_token}</code></li>
        </ul>
        ",
        "This is a test password reset email. The reset link above is not functional."
    );
    
    return sendEmail($email, $subject, $message, true);
}

/**
 * Send account suspension test email
 */
function sendSuspensionTestEmail($email, $user_name) {
    $subject = "Account Suspension Notice - " . getBankName();
    
    $message = generateBankingEmailTemplate(
        "Account Suspension Notice",
        "⚠️ Account Temporarily Suspended",
        "
        <p>Dear {$user_name},</p>
        <div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h4 style='color: #721c24; margin: 0 0 10px 0;'>🚫 Account Suspended</h4>
            <p style='margin: 0;'>Your account has been temporarily suspended due to security concerns.</p>
        </div>
        <p><strong>Suspension Details:</strong></p>
        <ul>
            <li><strong>Reason:</strong> Unusual activity detected</li>
            <li><strong>Date:</strong> " . date('Y-m-d H:i:s') . "</li>
            <li><strong>Reference:</strong> SUSP-TEST-" . time() . "</li>
        </ul>
        <p><strong>What happens next:</strong></p>
        <ol>
            <li>Our security team will review your account</li>
            <li>You may be contacted for additional verification</li>
            <li>Account access will be restored once review is complete</li>
        </ol>
        <p>If you believe this suspension is in error, please contact our customer support immediately.</p>
        ",
        "This is a test suspension email. Your account is not actually suspended."
    );
    
    return sendEmail($email, $subject, $message, true);
}

/**
 * Send account deletion test email
 */
function sendDeletionTestEmail($email, $user_name) {
    $subject = "Account Deletion Confirmation - " . getBankName();
    
    $message = generateBankingEmailTemplate(
        "Account Deletion Confirmation",
        "🗑️ Account Successfully Deleted",
        "
        <p>Dear {$user_name},</p>
        <p>This email confirms that your " . getBankName() . " account has been permanently deleted as requested.</p>
        <div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h4 style='color: #0c5460; margin: 0 0 10px 0;'>ℹ️ Deletion Summary</h4>
            <p><strong>Account Number:</strong> TEST123456789</p>
            <p><strong>Deletion Date:</strong> " . date('Y-m-d H:i:s') . "</p>
            <p><strong>Final Balance:</strong> $0.00</p>
            <p style='margin: 0;'><strong>Reference:</strong> DEL-TEST-" . time() . "</p>
        </div>
        <p><strong>What has been deleted:</strong></p>
        <ul>
            <li>All account information and transaction history</li>
            <li>Personal identification documents</li>
            <li>Saved payment methods and beneficiaries</li>
            <li>All associated virtual cards and crypto wallets</li>
        </ul>
        <p><strong>Important:</strong> This action cannot be undone. If you need banking services in the future, you will need to create a new account.</p>
        <p>Thank you for banking with us. We're sorry to see you go!</p>
        ",
        "This is a test deletion email. Your account has not been actually deleted."
    );
    
    return sendEmail($email, $subject, $message, true);
}

/**
 * Send transaction alert test email
 */
function sendTransactionTestEmail($email, $user_name) {
    $amount = number_format(250.00, 2);
    $transaction_id = 'TXN-TEST-' . time();
    
    $subject = "Transaction Alert - " . getBankName();
    
    $message = generateBankingEmailTemplate(
        "Transaction Alert",
        "💰 Account Activity Notification",
        "
        <p>Dear {$user_name},</p>
        <p>A transaction has been processed on your " . getBankName() . " account:</p>
        <div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h4 style='color: #155724; margin: 0 0 15px 0;'>✅ Credit Transaction</h4>
            <table style='width: 100%; border-collapse: collapse;'>
                <tr><td style='padding: 5px 0; border-bottom: 1px solid #c3e6cb;'><strong>Amount:</strong></td><td style='padding: 5px 0; border-bottom: 1px solid #c3e6cb; text-align: right;'>+$" . $amount . "</td></tr>
                <tr><td style='padding: 5px 0; border-bottom: 1px solid #c3e6cb;'><strong>Transaction ID:</strong></td><td style='padding: 5px 0; border-bottom: 1px solid #c3e6cb; text-align: right;'>" . $transaction_id . "</td></tr>
                <tr><td style='padding: 5px 0; border-bottom: 1px solid #c3e6cb;'><strong>Date & Time:</strong></td><td style='padding: 5px 0; border-bottom: 1px solid #c3e6cb; text-align: right;'>" . date('Y-m-d H:i:s') . "</td></tr>
                <tr><td style='padding: 5px 0; border-bottom: 1px solid #c3e6cb;'><strong>Description:</strong></td><td style='padding: 5px 0; border-bottom: 1px solid #c3e6cb; text-align: right;'>Test Credit Transaction</td></tr>
                <tr><td style='padding: 5px 0;'><strong>New Balance:</strong></td><td style='padding: 5px 0; text-align: right; font-size: 1.2rem; color: #28a745;'><strong>$1,250.00</strong></td></tr>
            </table>
        </div>
        <p><strong>Security Reminder:</strong></p>
        <ul>
            <li>If you did not authorize this transaction, contact us immediately</li>
            <li>Monitor your account regularly for unauthorized activity</li>
            <li>Never share your account credentials with anyone</li>
        </ul>
        ",
        "This is a test transaction email. No actual transaction has been processed."
    );
    
    return sendEmail($email, $subject, $message, true);
}

/**
 * Send security alert test email
 */
function sendSecurityTestEmail($email, $user_name) {
    $subject = "Security Alert - " . getBankName();
    
    $message = generateBankingEmailTemplate(
        "Security Alert",
        "🛡️ Account Security Notification",
        "
        <p>Dear {$user_name},</p>
        <div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h4 style='color: #856404; margin: 0 0 10px 0;'>🔔 Security Event Detected</h4>
            <p style='margin: 0;'>We detected a new login to your account from an unrecognized device.</p>
        </div>
        <p><strong>Login Details:</strong></p>
        <ul>
            <li><strong>Date & Time:</strong> " . date('Y-m-d H:i:s') . "</li>
            <li><strong>IP Address:</strong> *************</li>
            <li><strong>Location:</strong> Test City, Test Country</li>
            <li><strong>Device:</strong> Chrome Browser on Windows</li>
        </ul>
        <p><strong>If this was you:</strong></p>
        <ul>
            <li>No action is required</li>
            <li>Consider adding this device to your trusted devices</li>
        </ul>
        <p><strong>If this was NOT you:</strong></p>
        <ol>
            <li>Change your password immediately</li>
            <li>Review your recent account activity</li>
            <li>Contact our security team</li>
            <li>Consider enabling additional security features</li>
        </ol>
        <div style='text-align: center; margin: 30px 0;'>
            <a href='#' style='background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Secure My Account</a>
        </div>
        ",
        "This is a test security alert email. No actual security event has occurred."
    );
    
    return sendEmail($email, $subject, $message, true);
}

/**
 * Send SMTP test email
 */
function sendSMTPTestEmail($email) {
    $subject = "SMTP Connection Test - " . getBankName();
    
    $message = generateBankingEmailTemplate(
        "SMTP Connection Test",
        "🧪 Email System Test",
        "
        <p>This is a test email to verify SMTP connectivity and email delivery.</p>
        <div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h4 style='color: #0c5460; margin: 0 0 15px 0;'>📧 Test Configuration</h4>
            <table style='width: 100%; border-collapse: collapse;'>
                <tr><td style='padding: 5px 0; border-bottom: 1px solid #bee5eb;'><strong>SMTP Host:</strong></td><td style='padding: 5px 0; border-bottom: 1px solid #bee5eb; text-align: right;'>" . SMTP_HOST . "</td></tr>
                <tr><td style='padding: 5px 0; border-bottom: 1px solid #bee5eb;'><strong>SMTP Port:</strong></td><td style='padding: 5px 0; border-bottom: 1px solid #bee5eb; text-align: right;'>" . SMTP_PORT . "</td></tr>
                <tr><td style='padding: 5px 0; border-bottom: 1px solid #bee5eb;'><strong>Encryption:</strong></td><td style='padding: 5px 0; border-bottom: 1px solid #bee5eb; text-align: right;'>" . SMTP_ENCRYPTION . "</td></tr>
                <tr><td style='padding: 5px 0; border-bottom: 1px solid #bee5eb;'><strong>From Email:</strong></td><td style='padding: 5px 0; border-bottom: 1px solid #bee5eb; text-align: right;'>" . FROM_EMAIL . "</td></tr>
                <tr><td style='padding: 5px 0;'><strong>Test Time:</strong></td><td style='padding: 5px 0; text-align: right;'>" . date('Y-m-d H:i:s') . "</td></tr>
            </table>
        </div>
        <p>If you received this email, the SMTP configuration is working correctly!</p>
        ",
        "This email confirms that the SMTP server connection and email delivery system are functioning properly."
    );
    
    return sendEmail($email, $subject, $message, true);
}

/**
 * Generate professional banking email template
 */
function generateBankingEmailTemplate($title, $header, $content, $footer_note = '') {
    $bank_name = getBankName();
    $current_year = date('Y');
    
    return "
    <!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>{$title}</title>
    </head>
    <body style='margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8f9fa;'>
        <div style='max-width: 600px; margin: 0 auto; background-color: white;'>
            <!-- Header -->
            <div style='background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 30px; text-align: center;'>
                <h1 style='margin: 0; font-size: 1.8rem;'>🏦 {$bank_name}</h1>
                <p style='margin: 10px 0 0 0; opacity: 0.9;'>Secure Online Banking</p>
            </div>
            
            <!-- Content -->
            <div style='padding: 40px 30px;'>
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h2 style='color: #2c3e50; margin: 0; font-size: 1.5rem;'>{$header}</h2>
                </div>
                
                <div style='line-height: 1.6; color: #333;'>
                    {$content}
                </div>
            </div>
            
            <!-- Footer -->
            <div style='background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #e9ecef;'>
                " . (!empty($footer_note) ? "<p style='color: #856404; background: #fff3cd; padding: 15px; border-radius: 5px; margin: 0 0 20px 0; border: 1px solid #ffeaa7;'><strong>Note:</strong> {$footer_note}</p>" : "") . "
                <p style='color: #6c757d; margin: 0 0 10px 0; font-size: 0.9rem;'>This is an automated message from {$bank_name}.</p>
                <p style='color: #6c757d; margin: 0; font-size: 0.8rem;'>&copy; {$current_year} {$bank_name}. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>";
}
?>
