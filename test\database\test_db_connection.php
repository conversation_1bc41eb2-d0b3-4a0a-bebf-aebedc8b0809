<?php
/**
 * Database Connection Test Script
 * This script tests the database connection and creates the database if needed
 */

// Database configuration (same as in config/database.php)
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', 'root');
define('DB_NAME', 'online_banking');
define('DB_CHARSET', 'utf8mb4');

echo "<h2>Database Connection Test</h2>";
echo "<hr>";

// Test 1: Basic MySQL connection
echo "<h3>Step 1: Testing MySQL Connection</h3>";
try {
    $connection = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD);
    
    if ($connection->connect_error) {
        throw new Exception("Connection failed: " . $connection->connect_error);
    }
    
    echo "✅ <strong>SUCCESS:</strong> Connected to MySQL server successfully!<br>";
    echo "📊 <strong>MySQL Version:</strong> " . $connection->server_info . "<br><br>";
    
} catch (Exception $e) {
    echo "❌ <strong>ERROR:</strong> " . $e->getMessage() . "<br>";
    echo "💡 <strong>Solution:</strong> Check if MySQL/MAMP is running and credentials are correct.<br><br>";
    exit;
}

// Test 2: Check if database exists
echo "<h3>Step 2: Checking Database Existence</h3>";
$db_exists = false;
$result = $connection->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
if ($result && $result->num_rows > 0) {
    echo "✅ <strong>SUCCESS:</strong> Database '" . DB_NAME . "' exists!<br><br>";
    $db_exists = true;
} else {
    echo "⚠️ <strong>WARNING:</strong> Database '" . DB_NAME . "' does not exist.<br>";
    echo "🔧 <strong>Creating database...</strong><br>";
    
    if ($connection->query("CREATE DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")) {
        echo "✅ <strong>SUCCESS:</strong> Database '" . DB_NAME . "' created successfully!<br><br>";
        $db_exists = true;
    } else {
        echo "❌ <strong>ERROR:</strong> Failed to create database: " . $connection->error . "<br><br>";
        exit;
    }
}

// Test 3: Connect to the specific database
echo "<h3>Step 3: Connecting to Database</h3>";
$connection->select_db(DB_NAME);
if ($connection->error) {
    echo "❌ <strong>ERROR:</strong> Cannot select database: " . $connection->error . "<br><br>";
    exit;
} else {
    echo "✅ <strong>SUCCESS:</strong> Connected to database '" . DB_NAME . "' successfully!<br><br>";
}

// Test 4: Check if tables exist
echo "<h3>Step 4: Checking Database Tables</h3>";
$required_tables = ['accounts', 'transfers', 'beneficiaries', 'tickets', 'audit_logs', 'system_settings', 'login_attempts'];
$existing_tables = [];

$result = $connection->query("SHOW TABLES");
if ($result) {
    while ($row = $result->fetch_array()) {
        $existing_tables[] = $row[0];
    }
}

$missing_tables = array_diff($required_tables, $existing_tables);

if (empty($missing_tables)) {
    echo "✅ <strong>SUCCESS:</strong> All required tables exist!<br>";
    foreach ($required_tables as $table) {
        echo "   📋 " . $table . "<br>";
    }
    echo "<br>";
} else {
    echo "⚠️ <strong>WARNING:</strong> Missing tables detected:<br>";
    foreach ($missing_tables as $table) {
        echo "   ❌ " . $table . "<br>";
    }
    echo "<br>";
    echo "💡 <strong>Solution:</strong> Import the database/schema.sql file to create missing tables.<br>";
    echo "📝 <strong>Instructions:</strong><br>";
    echo "   1. Open phpMyAdmin or MySQL command line<br>";
    echo "   2. Select the '" . DB_NAME . "' database<br>";
    echo "   3. Import the file: database/schema.sql<br><br>";
}

// Test 5: Test basic database operations
if (empty($missing_tables)) {
    echo "<h3>Step 5: Testing Database Operations</h3>";
    
    // Test SELECT operation
    try {
        $result = $connection->query("SELECT COUNT(*) as count FROM accounts");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "✅ <strong>SELECT Test:</strong> Found " . $row['count'] . " accounts in database<br>";
        }
        
        // Test if default admin user exists
        $result = $connection->query("SELECT username, first_name, last_name, is_admin FROM accounts WHERE username = 'admin'");
        if ($result && $result->num_rows > 0) {
            $admin = $result->fetch_assoc();
            echo "✅ <strong>Admin User:</strong> " . $admin['first_name'] . " " . $admin['last_name'] . " (Admin: " . ($admin['is_admin'] ? 'Yes' : 'No') . ")<br>";
        } else {
            echo "⚠️ <strong>WARNING:</strong> Default admin user not found<br>";
        }
        
        // Test if sample user exists
        $result = $connection->query("SELECT username, first_name, last_name, balance FROM accounts WHERE username = 'john_doe'");
        if ($result && $result->num_rows > 0) {
            $user = $result->fetch_assoc();
            echo "✅ <strong>Sample User:</strong> " . $user['first_name'] . " " . $user['last_name'] . " (Balance: $" . number_format($user['balance'], 2) . ")<br>";
        } else {
            echo "⚠️ <strong>WARNING:</strong> Sample user not found<br>";
        }
        
        echo "<br>";
        
    } catch (Exception $e) {
        echo "❌ <strong>ERROR:</strong> Database operation failed: " . $e->getMessage() . "<br><br>";
    }
}

// Test 6: Test the application's database class
echo "<h3>Step 6: Testing Application Database Class</h3>";
try {
    require_once 'config/database.php';
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    if ($conn && !$conn->connect_error) {
        echo "✅ <strong>SUCCESS:</strong> Application database class working correctly!<br>";
        
        // Test a simple query using the application's method
        $result = $db->query("SELECT COUNT(*) as total FROM accounts");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "✅ <strong>Query Test:</strong> Database class can execute queries (Found " . $row['total'] . " accounts)<br>";
        }
        
    } else {
        echo "❌ <strong>ERROR:</strong> Application database class failed to connect<br>";
    }
    
} catch (Exception $e) {
    echo "❌ <strong>ERROR:</strong> " . $e->getMessage() . "<br>";
}

echo "<br>";

// Final summary
echo "<h3>🎯 Summary</h3>";
if (empty($missing_tables)) {
    echo "🎉 <strong>EXCELLENT!</strong> Your database is fully configured and ready to use!<br>";
    echo "🚀 <strong>Next Steps:</strong><br>";
    echo "   1. Visit: <a href='http://localhost/online_banking' target='_blank'>http://localhost/online_banking</a><br>";
    echo "   2. Login with: admin / admin123 (Admin) or john_doe / user123 (User)<br>";
    echo "   3. Start testing the banking features!<br>";
} else {
    echo "⚠️ <strong>ACTION REQUIRED:</strong> Please import the database schema first.<br>";
    echo "📁 <strong>File to import:</strong> database/schema.sql<br>";
    echo "🔧 <strong>After importing, refresh this page to test again.</strong><br>";
}

echo "<br><hr>";
echo "<p><small>💡 <strong>Tip:</strong> Delete this test file (test_db_connection.php) after confirming everything works.</small></p>";

$connection->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
h2, h3 { color: #333; }
hr { border: 1px solid #ddd; }
</style>
