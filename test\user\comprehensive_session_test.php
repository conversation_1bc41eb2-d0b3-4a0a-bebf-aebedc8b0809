<?php
/**
 * Comprehensive User Session Management Test Suite
 * Tests all aspects of user session functionality including security, timeout, and state management
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/SessionManager.php';
require_once __DIR__ . '/../../config/database.php';

class UserSessionTest {
    
    private $db;
    private $testResults = [];
    private $testUserId = null;
    private $originalSessionData = [];
    
    public function __construct() {
        $this->db = getDB();
        $this->backupSession();
        echo "<h1>Comprehensive User Session Management Test Suite</h1>\n";
        echo "<style>
            .test-pass { color: green; font-weight: bold; }
            .test-fail { color: red; font-weight: bold; }
            .test-info { color: blue; }
            .test-section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
            .test-result { margin: 5px 0; padding: 5px; }
        </style>\n";
    }
    
    /**
     * Backup current session data
     */
    private function backupSession() {
        if (session_status() === PHP_SESSION_ACTIVE) {
            $this->originalSessionData = $_SESSION;
        }
    }
    
    /**
     * Restore original session data
     */
    private function restoreSession() {
        session_destroy();
        session_start();
        $_SESSION = $this->originalSessionData;
    }
    
    /**
     * Create test user for session testing
     */
    private function createTestUser() {
        try {
            $username = 'session_test_user_' . time();
            $email = 'sessiontest' . time() . '@test.com';
            $password = hashPassword('TestPassword123!');
            $accountNumber = generateAccountNumber();
            
            $sql = "INSERT INTO accounts (account_number, username, password, email, first_name, last_name, status, kyc_status) 
                    VALUES (?, ?, ?, ?, 'Session', 'Test', 'active', 'verified')";
            
            $result = $this->db->query($sql, [$accountNumber, $username, $password, $email]);
            
            if ($result) {
                $this->testUserId = $this->db->getConnection()->insert_id;
                $this->logTest("Test user created successfully", true, "User ID: {$this->testUserId}");
                return true;
            }
            
            $this->logTest("Failed to create test user", false);
            return false;
            
        } catch (Exception $e) {
            $this->logTest("Error creating test user: " . $e->getMessage(), false);
            return false;
        }
    }
    
    /**
     * Clean up test user
     */
    private function cleanupTestUser() {
        if ($this->testUserId) {
            try {
                $sql = "DELETE FROM accounts WHERE id = ?";
                $this->db->query($sql, [$this->testUserId]);
                $this->logTest("Test user cleaned up", true);
            } catch (Exception $e) {
                $this->logTest("Error cleaning up test user: " . $e->getMessage(), false);
            }
        }
    }
    
    /**
     * Log test result
     */
    private function logTest($description, $passed, $details = '') {
        $status = $passed ? 'PASS' : 'FAIL';
        $class = $passed ? 'test-pass' : 'test-fail';
        
        echo "<div class='test-result'>";
        echo "<span class='$class'>[$status]</span> $description";
        if ($details) {
            echo " <span class='test-info'>($details)</span>";
        }
        echo "</div>\n";
        
        $this->testResults[] = [
            'description' => $description,
            'passed' => $passed,
            'details' => $details
        ];
    }
    
    /**
     * Test session initialization
     */
    public function testSessionInitialization() {
        echo "<div class='test-section'><h2>Session Initialization Tests</h2>";
        
        // Test session start
        session_destroy();
        $sessionManager = SessionManager::getInstance();
        
        $this->logTest("Session manager singleton created", 
            $sessionManager instanceof SessionManager);
        
        $this->logTest("Session started successfully", 
            session_status() === PHP_SESSION_ACTIVE);
        
        $this->logTest("Session ID generated", 
            !empty(session_id()));
        
        $this->logTest("Session security variables initialized", 
            isset($_SESSION['initiated']) && 
            isset($_SESSION['created_at']) && 
            isset($_SESSION['last_activity']));
        
        echo "</div>";
    }
    
    /**
     * Test user login session creation
     */
    public function testUserLoginSession() {
        echo "<div class='test-section'><h2>User Login Session Tests</h2>";
        
        if (!$this->createTestUser()) {
            $this->logTest("Cannot proceed with login tests - test user creation failed", false);
            echo "</div>";
            return;
        }
        
        // Test login with SessionManager
        $loginResult = SessionManager::login($this->testUserId, 'user', [
            'username' => 'session_test_user',
            'first_name' => 'Session',
            'last_name' => 'Test',
            'email' => '<EMAIL>',
            'account_number' => '**********',
            'balance' => 1000.00,
            'kyc_status' => 'verified'
        ]);
        
        $this->logTest("User login successful", $loginResult === true);
        
        $this->logTest("User ID stored in session", 
            isset($_SESSION['user_id']) && $_SESSION['user_id'] == $this->testUserId);
        
        $this->logTest("User type set correctly", 
            isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'user');
        
        $this->logTest("Login flag set", 
            isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true);
        
        $this->logTest("Login time recorded", 
            isset($_SESSION['login_time']) && is_numeric($_SESSION['login_time']));
        
        // Test session validation functions
        $this->logTest("isLoggedIn() returns true", SessionManager::isLoggedIn());
        $this->logTest("isAdmin() returns false", !SessionManager::isAdmin());
        
        echo "</div>";
    }
    
    /**
     * Test session timeout functionality
     */
    public function testSessionTimeout() {
        echo "<div class='test-section'><h2>Session Timeout Tests</h2>";
        
        // Set short timeout for testing
        SessionManager::setTimeout(5); // 5 seconds
        
        $this->logTest("Session timeout set to 5 seconds", true);
        
        // Test session extension
        SessionManager::extendSession();
        $lastActivity = $_SESSION['last_activity'];
        
        $this->logTest("Session extended successfully", 
            isset($_SESSION['last_activity']) && $_SESSION['last_activity'] >= $lastActivity);
        
        // Test session info
        $sessionInfo = SessionManager::getSessionInfo();
        
        $this->logTest("Session info retrieved", 
            is_array($sessionInfo) && isset($sessionInfo['user_id']));
        
        $this->logTest("Time remaining calculated", 
            isset($sessionInfo['time_remaining']) && is_numeric($sessionInfo['time_remaining']));
        
        echo "</div>";
    }
    
    /**
     * Test session security measures
     */
    public function testSessionSecurity() {
        echo "<div class='test-section'><h2>Session Security Tests</h2>";
        
        // Test session regeneration
        $oldSessionId = session_id();
        session_regenerate_id(true);
        $newSessionId = session_id();
        
        $this->logTest("Session ID regenerated", $oldSessionId !== $newSessionId);
        
        // Test IP address tracking
        $this->logTest("IP address stored in session", 
            isset($_SESSION['ip_address']));
        
        // Test user agent tracking
        $this->logTest("User agent stored in session", 
            isset($_SESSION['user_agent']));
        
        // Test session data validation
        $this->logTest("Session has creation timestamp", 
            isset($_SESSION['created_at']) && is_numeric($_SESSION['created_at']));
        
        echo "</div>";
    }
    
    /**
     * Test OTP session handling
     */
    public function testOTPSession() {
        echo "<div class='test-section'><h2>OTP Session Tests</h2>";
        
        // Simulate OTP session setup
        $_SESSION['otp_user_id'] = $this->testUserId;
        $_SESSION['otp_pending'] = true;
        $_SESSION['otp_email'] = '<EMAIL>';
        
        $this->logTest("OTP user ID set", 
            isset($_SESSION['otp_user_id']) && $_SESSION['otp_user_id'] == $this->testUserId);
        
        $this->logTest("OTP pending flag set", 
            isset($_SESSION['otp_pending']) && $_SESSION['otp_pending'] === true);
        
        $this->logTest("OTP email stored", 
            isset($_SESSION['otp_email']) && !empty($_SESSION['otp_email']));
        
        // Test OTP session cleanup
        unset($_SESSION['otp_user_id'], $_SESSION['otp_pending'], $_SESSION['otp_email']);
        
        $this->logTest("OTP session variables cleared", 
            !isset($_SESSION['otp_user_id']) && 
            !isset($_SESSION['otp_pending']) && 
            !isset($_SESSION['otp_email']));
        
        echo "</div>";
    }
    
    /**
     * Test session data management
     */
    public function testSessionDataManagement() {
        echo "<div class='test-section'><h2>Session Data Management Tests</h2>";
        
        // Test setting session data
        SessionManager::set('test_key', 'test_value');
        $this->logTest("Session data set successfully", 
            SessionManager::get('test_key') === 'test_value');
        
        // Test getting session data
        $retrievedValue = SessionManager::get('test_key');
        $this->logTest("Session data retrieved successfully", 
            $retrievedValue === 'test_value');
        
        // Test removing session data
        SessionManager::remove('test_key');
        $this->logTest("Session data removed successfully", 
            SessionManager::get('test_key') === null);
        
        // Test checking if session data exists
        SessionManager::set('exists_key', 'value');
        $this->logTest("Session data exists check works", 
            SessionManager::has('exists_key') === true);
        
        $this->logTest("Session data not exists check works", 
            SessionManager::has('non_existent_key') === false);
        
        echo "</div>";
    }
    
    /**
     * Test session logout and cleanup
     */
    public function testSessionLogout() {
        echo "<div class='test-section'><h2>Session Logout Tests</h2>";
        
        // Verify user is logged in before logout
        $this->logTest("User logged in before logout", SessionManager::isLoggedIn());
        
        // Test logout
        SessionManager::logout('Test logout');
        
        $this->logTest("User logged out successfully", !SessionManager::isLoggedIn());
        
        $this->logTest("Session destroyed", session_status() === PHP_SESSION_NONE);
        
        echo "</div>";
    }
    
    /**
     * Run all tests
     */
    public function runAllTests() {
        echo "<h2>Starting Comprehensive Session Tests...</h2>";
        
        $this->testSessionInitialization();
        $this->testUserLoginSession();
        $this->testSessionTimeout();
        $this->testSessionSecurity();
        $this->testOTPSession();
        $this->testSessionDataManagement();
        $this->testSessionLogout();
        
        $this->cleanupTestUser();
        $this->restoreSession();
        $this->displaySummary();
    }
    
    /**
     * Display test summary
     */
    private function displaySummary() {
        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, function($test) {
            return $test['passed'];
        }));
        $failedTests = $totalTests - $passedTests;
        
        echo "<div class='test-section'>";
        echo "<h2>Test Summary</h2>";
        echo "<p><strong>Total Tests:</strong> $totalTests</p>";
        echo "<p class='test-pass'><strong>Passed:</strong> $passedTests</p>";
        echo "<p class='test-fail'><strong>Failed:</strong> $failedTests</p>";
        echo "<p><strong>Success Rate:</strong> " . round(($passedTests / $totalTests) * 100, 2) . "%</p>";
        echo "</div>";
        
        if ($failedTests > 0) {
            echo "<div class='test-section'>";
            echo "<h3>Failed Tests:</h3>";
            foreach ($this->testResults as $test) {
                if (!$test['passed']) {
                    echo "<div class='test-fail'>• {$test['description']}</div>";
                }
            }
            echo "</div>";
        }
    }
}

// Run the tests
$sessionTest = new UserSessionTest();
$sessionTest->runAllTests();
?>
