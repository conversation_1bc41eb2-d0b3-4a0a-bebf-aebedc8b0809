<?php
/**
 * Test Login Page Functionality
 * Quick test to verify the modular login page works correctly
 */

echo "<h1>Testing Login Page Components</h1>";

// Test 1: Check if all required files exist
$required_files = [
    'config/config.php',
    'auth/includes/login_logic.php',
    'auth/includes/login_header.php',
    'auth/includes/login_form.php',
    'auth/includes/login_footer.php',
    'auth/styles/login.css'
];

echo "<h2>File Existence Test</h2>";
foreach ($required_files as $file) {
    $exists = file_exists($file);
    $status = $exists ? '✅ EXISTS' : '❌ MISSING';
    echo "<p>$status - $file</p>";
}

// Test 2: Check if functions are available
echo "<h2>Function Availability Test</h2>";

require_once 'config/config.php';

$functions = [
    'isLoggedIn',
    'redirect',
    'sanitizeInput',
    'getDB',
    'hashPassword',
    'verifyPassword',
    'url',
    'hasFlashMessage',
    'getFlashMessage'
];

foreach ($functions as $function) {
    $exists = function_exists($function);
    $status = $exists ? '✅ AVAILABLE' : '❌ MISSING';
    echo "<p>$status - $function()</p>";
}

// Test 3: Test site settings function
echo "<h2>Site Settings Test</h2>";
try {
    require_once 'auth/includes/login_logic.php';
    
    if (isset($site_settings) && is_array($site_settings)) {
        echo "<p>✅ Site settings loaded successfully</p>";
        echo "<ul>";
        foreach ($site_settings as $key => $value) {
            echo "<li><strong>$key:</strong> " . htmlspecialchars($value ?: 'Not set') . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ Site settings not available</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error loading site settings: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 4: CSS file accessibility
echo "<h2>CSS File Test</h2>";
$css_file = 'auth/styles/login.css';
if (file_exists($css_file)) {
    $css_size = filesize($css_file);
    echo "<p>✅ CSS file exists ($css_size bytes)</p>";
    
    // Check if CSS contains key styles
    $css_content = file_get_contents($css_file);
    $key_styles = ['.left-panel', '.right-panel', '.btn-login', '.form-control'];
    
    foreach ($key_styles as $style) {
        $found = strpos($css_content, $style) !== false;
        $status = $found ? '✅ FOUND' : '❌ MISSING';
        echo "<p>$status - $style</p>";
    }
} else {
    echo "<p>❌ CSS file missing</p>";
}

echo "<h2>Test Complete</h2>";
echo "<p><a href='login.php' style='background: #4f46e5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Login Page</a></p>";
echo "<p><a href='auth/login_improvements_demo.html' style='background: #16a34a; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Demo</a></p>";
?>
