<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Credit/Debit Users';
$errors = [];
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = intval($_POST['user_id'] ?? 0);
    $action = $_POST['action'] ?? '';
    $amount = floatval($_POST['amount'] ?? 0);
    $description = trim($_POST['description'] ?? '');
    $transaction_date = $_POST['transaction_date'] ?? date('Y-m-d');
    
    // Basic validation
    if ($user_id <= 0) {
        $errors[] = 'Please select a valid user.';
    }
    
    if (!in_array($action, ['credit', 'debit'])) {
        $errors[] = 'Please select a valid operation type.';
    }
    
    if ($amount <= 0) {
        $errors[] = 'Amount must be greater than zero.';
    }
    
    if (empty($description)) {
        $errors[] = 'Description is required.';
    }
    
    // Process if no errors
    if (empty($errors)) {
        try {
            $db = getDB();
            $db->beginTransaction();
            
            // Get user details
            $user_query = "SELECT id, first_name, last_name, username, account_number, balance, currency FROM accounts WHERE id = ? AND is_admin = 0";
            $user_result = $db->query($user_query, [$user_id]);
            
            if ($user_result->num_rows === 0) {
                throw new Exception('User not found.');
            }
            
            $user = $user_result->fetch_assoc();
            $current_balance = floatval($user['balance']);
            
            // Calculate new balance
            if ($action === 'credit') {
                $new_balance = $current_balance + $amount;
                $operation_name = 'Credit';
            } else {
                $new_balance = $current_balance - $amount;
                $operation_name = 'Debit';
                
                // Check for sufficient balance
                if ($new_balance < 0) {
                    throw new Exception('Insufficient balance. Current balance: ' . formatCurrency($current_balance, $user['currency']));
                }
            }
            
            // Update account balance
            $update_query = "UPDATE accounts SET balance = ? WHERE id = ?";
            $update_result = $db->query($update_query, [$new_balance, $user_id]);
            
            if (!$update_result) {
                throw new Exception('Failed to update account balance.');
            }
            
            // Generate reference number
            $reference = 'ADM' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Record transaction
            $transaction_query = "INSERT INTO account_transactions (
                account_id, transaction_type, amount, currency, description, 
                reference_number, category, status, processed_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, 'adjustment', 'completed', ?, ?)";
            
            $transaction_params = [
                $user_id,
                $action,
                $amount,
                $user['currency'] ?? 'USD',
                $description,
                $reference,
                $_SESSION['user_id'],
                $transaction_date . ' ' . date('H:i:s')
            ];
            
            $transaction_result = $db->query($transaction_query, $transaction_params);
            
            if (!$transaction_result) {
                throw new Exception('Failed to record transaction.');
            }
            
            // Commit transaction
            $db->commit();
            
            $success = "$operation_name of " . formatCurrency($amount, $user['currency']) . 
                      " successful! New balance: " . formatCurrency($new_balance, $user['currency']) . 
                      " (Reference: $reference)";
            
        } catch (Exception $e) {
            $db->rollback();
            $errors[] = $e->getMessage();
        }
    }
}

// Get users for dropdown
$users = [];
try {
    $db = getDB();
    $users_query = "SELECT id, first_name, last_name, username, account_number, balance, currency 
                    FROM accounts WHERE is_admin = 0 AND status = 'active' 
                    ORDER BY first_name, last_name";
    $users_result = $db->query($users_query);
    
    while ($user = $users_result->fetch_assoc()) {
        $users[] = $user;
    }
} catch (Exception $e) {
    $errors[] = 'Failed to load users: ' . $e->getMessage();
}

// Get recent transactions
$recent_transactions = [];
try {
    $recent_query = "SELECT at.*, a.first_name, a.last_name, a.username 
                     FROM account_transactions at 
                     JOIN accounts a ON at.account_id = a.id 
                     WHERE at.category = 'adjustment' 
                     ORDER BY at.created_at DESC 
                     LIMIT 10";
    $recent_result = $db->query($recent_query);
    
    while ($transaction = $recent_result->fetch_assoc()) {
        $recent_transactions[] = $transaction;
    }
} catch (Exception $e) {
    // Ignore errors for recent transactions
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Credit/Debit Users</li>
    </ol>
</nav>

<!-- Error Messages -->
<?php if (!empty($errors)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Success Messages -->
<?php if (!empty($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row row-cards">
    <!-- User Details Panel (Hidden by default) -->
    <div class="col-md-4" id="userDetailsPanel" style="display: none;">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: #4361ee; color: white;">
                <h3 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    Selected User Details
                </h3>
            </div>
            <div class="card-body" id="userDetailsContent">
                <!-- User details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Credit/Debit Form -->
    <div class="col-md-8" id="formColumn">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-coins me-2"></i>
                    Credit/Debit User Account
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <!-- User Selection -->
                    <div class="mb-3">
                        <label class="form-label required">Select User</label>
                        <select name="user_id" id="userSelect" class="form-select" required onchange="loadUserDetails(this.value)">
                            <option value="">Choose a user...</option>
                            <?php foreach ($users as $user): ?>
                            <option value="<?php echo $user['id']; ?>"
                                    data-user='<?php echo json_encode($user); ?>'>
                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                (@<?php echo htmlspecialchars($user['username']); ?>) -
                                Balance: <?php echo formatCurrency($user['balance'], $user['currency'] ?? 'USD'); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-hint">Select the user account to credit or debit</div>
                    </div>

                    <!-- Operation Type -->
                    <div class="mb-3">
                        <label class="form-label required">Operation Type</label>
                        <div class="form-selectgroup">
                            <label class="form-selectgroup-item">
                                <input type="radio" name="action" value="credit" class="form-selectgroup-input" checked>
                                <span class="form-selectgroup-label">
                                    <i class="fas fa-plus-circle text-success me-2"></i>
                                    Credit (Add Funds)
                                </span>
                            </label>
                            <label class="form-selectgroup-item">
                                <input type="radio" name="action" value="debit" class="form-selectgroup-input">
                                <span class="form-selectgroup-label">
                                    <i class="fas fa-minus-circle text-danger me-2"></i>
                                    Debit (Deduct Funds)
                                </span>
                            </label>
                        </div>
                    </div>

                    <!-- Amount -->
                    <div class="mb-3">
                        <label class="form-label required">Amount</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" name="amount" class="form-control" step="0.01" min="0.01" required>
                        </div>
                        <div class="form-hint">Enter the amount to credit or debit</div>
                    </div>

                    <!-- Transaction Date -->
                    <div class="mb-3">
                        <label class="form-label required">Transaction Date</label>
                        <input type="date" name="transaction_date" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                        <div class="form-hint">Select current date or backdate the transaction</div>
                    </div>

                    <!-- Description -->
                    <div class="mb-3">
                        <label class="form-label required">Description</label>
                        <textarea name="description" class="form-control" rows="3" placeholder="Enter reason for this operation..." required></textarea>
                        <div class="form-hint">Provide a clear description for audit purposes</div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check me-2"></i>
                            Process Operation
                        </button>
                        <button type="reset" class="btn btn-secondary ms-2">
                            <i class="fas fa-undo me-2"></i>
                            Reset Form
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>


</div>

<!-- Recent Transactions -->
<?php if (!empty($recent_transactions)): ?>
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Recent Credit/Debit Transactions
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>User</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Reference</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_transactions as $transaction): ?>
                            <tr>
                                <td>
                                    <div class="text-muted"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></div>
                                    <small class="text-muted"><?php echo date('g:i A', strtotime($transaction['created_at'])); ?></small>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2">
                                            <?php echo strtoupper(substr($transaction['first_name'], 0, 1) . substr($transaction['last_name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($transaction['first_name'] . ' ' . $transaction['last_name']); ?></div>
                                            <div class="text-muted">@<?php echo htmlspecialchars($transaction['username']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($transaction['transaction_type'] === 'credit'): ?>
                                    <span class="badge bg-success">Credit</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">Debit</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="fw-bold <?php echo $transaction['transaction_type'] === 'credit' ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo ($transaction['transaction_type'] === 'credit' ? '+' : '-') . formatCurrency($transaction['amount'], $transaction['currency']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($transaction['description']); ?>">
                                        <?php echo htmlspecialchars($transaction['description']); ?>
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted"><?php echo htmlspecialchars($transaction['reference_number']); ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $transaction['status'] === 'completed' ? 'success' : 'warning'; ?>">
                                        <?php echo ucfirst($transaction['status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function loadUserDetails(userId) {
    const userDetailsPanel = document.getElementById('userDetailsPanel');
    const formColumn = document.getElementById('formColumn');
    const userDetailsContent = document.getElementById('userDetailsContent');

    if (!userId) {
        // Hide user details panel and restore original layout
        userDetailsPanel.style.display = 'none';
        formColumn.className = 'col-md-12';
        return;
    }

    // Get user data from the selected option
    const selectElement = document.getElementById('userSelect');
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const userData = JSON.parse(selectedOption.getAttribute('data-user'));

    // Show user details panel and adjust layout
    userDetailsPanel.style.display = 'block';
    formColumn.className = 'col-md-8';

    // Format currency
    const formatCurrency = (amount, currency = 'USD') => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };

    // Generate user initials
    const initials = (userData.first_name.charAt(0) + userData.last_name.charAt(0)).toUpperCase();

    // Create user details HTML
    const userDetailsHTML = `
        <div class="text-center mb-4">
            <div class="avatar avatar-xl mx-auto mb-3" style="background: #4361ee; color: white; font-size: 1.5rem;">
                ${initials}
            </div>
            <h4 class="mb-1">${userData.first_name} ${userData.last_name}</h4>
            <p class="text-muted mb-0">@${userData.username}</p>
        </div>

        <div class="row mb-3">
            <div class="col-12">
                <div class="card card-sm bg-primary-lt">
                    <div class="card-body text-center">
                        <div class="text-primary mb-1">
                            <i class="fas fa-wallet" style="font-size: 1.5rem;"></i>
                        </div>
                        <h3 class="mb-1">${formatCurrency(parseFloat(userData.balance), userData.currency || 'USD')}</h3>
                        <p class="text-muted mb-0">Current Balance</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-2">
            <div class="col-12">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-4 text-center">
                                <div class="text-muted small">Account</div>
                                <div class="fw-bold small">${userData.account_number}</div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="text-muted small">Currency</div>
                                <div class="fw-bold small">${userData.currency || 'USD'}</div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="text-muted small">ID</div>
                                <div class="fw-bold small">#${userData.id}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-3">
            <div class="alert alert-info mb-0">
                <div class="d-flex">
                    <div class="me-2">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div>
                        <h4 class="alert-title">Quick Info</h4>
                        <p class="mb-1"><strong>Account Status:</strong> Active</p>
                        <p class="mb-0"><strong>Available for:</strong> Credit & Debit operations</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    userDetailsContent.innerHTML = userDetailsHTML;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // If there's a selected user (e.g., after form submission), load their details
    const userSelect = document.getElementById('userSelect');
    if (userSelect.value) {
        loadUserDetails(userSelect.value);
    }
});
</script>

<?php include 'includes/admin-footer.php'; ?>
