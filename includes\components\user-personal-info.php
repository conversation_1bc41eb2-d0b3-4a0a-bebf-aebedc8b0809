<?php
/**
 * User Personal Information Component
 * Displays user's personal details in a structured format
 */

if (!isset($user) || !is_array($user)) {
    throw new Exception('User data is required for personal info component');
}
?>

<!-- Personal Information -->
<div class="col-lg-6">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-user me-2"></i>
                Personal Information
            </h3>
        </div>
        <div class="card-body personal-info-section">
            <div class="row">
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-5 border-bottom border-light pb-2 mb-3">Full Name:</dt>
                        <dd class="col-7 border-bottom border-light pb-2 mb-3">
                            <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                        </dd>

                        <dt class="col-5 border-bottom border-light pb-2 mb-3">Email:</dt>
                        <dd class="col-7 border-bottom border-light pb-2 mb-3">
                            <?php echo htmlspecialchars($user['email']); ?>
                        </dd>

                        <dt class="col-5 border-bottom border-light pb-2 mb-3">Phone:</dt>
                        <dd class="col-7 border-bottom border-light pb-2 mb-3">
                            <?php echo htmlspecialchars($user['phone'] ?? 'Not provided'); ?>
                        </dd>

                        <dt class="col-5 pb-2">Date of Birth:</dt>
                        <dd class="col-7 pb-2">
                            <?php echo $user['date_of_birth'] ? formatDate($user['date_of_birth'], 'M j, Y') : 'Not provided'; ?>
                        </dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-5 border-bottom border-light pb-2 mb-3">Gender:</dt>
                        <dd class="col-7 border-bottom border-light pb-2 mb-3">
                            <?php echo ucfirst($user['gender'] ?? 'Not specified'); ?>
                        </dd>

                        <dt class="col-5 border-bottom border-light pb-2 mb-3">Marital Status:</dt>
                        <dd class="col-7 border-bottom border-light pb-2 mb-3">
                            <?php echo ucfirst($user['marital_status'] ?? 'Not specified'); ?>
                        </dd>

                        <dt class="col-5 border-bottom border-light pb-2 mb-3">Occupation:</dt>
                        <dd class="col-7 border-bottom border-light pb-2 mb-3">
                            <?php echo htmlspecialchars($user['occupation'] ?? 'Not provided'); ?>
                        </dd>

                        <dt class="col-5 pb-2">KYC Status:</dt>
                        <dd class="col-7 pb-2">
                            <span class="badge bg-<?php echo $user['kyc_status'] === 'verified' ? 'green' : ($user['kyc_status'] === 'rejected' ? 'red' : 'yellow'); ?>-lt">
                                <?php echo ucfirst($user['kyc_status']); ?>
                            </span>
                        </dd>
                    </dl>
                </div>
            </div>

            <?php if (!empty($user['address'])): ?>
            <div class="mt-3">
                <dl class="row">
                    <dt class="col-2">Address:</dt>
                    <dd class="col-10"><?php echo htmlspecialchars($user['address']); ?></dd>
                </dl>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
