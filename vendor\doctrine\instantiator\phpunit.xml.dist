<?xml version="1.0"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
    bootstrap="./vendor/autoload.php"
    colors="true"
    convertErrorsToExceptions="true"
    convertNoticesToExceptions="true"
    convertWarningsToExceptions="true"
    convertDeprecationsToExceptions="true"
    verbose="true"
    stopOnFailure="false"
    processIsolation="false"
    backupGlobals="false"
>
    <php>
        <ini name="error_reporting" value="-1" />
    </php>

    <testsuite name="Doctrine\Instantiator tests">
        <directory>./tests/DoctrineTest/InstantiatorTest</directory>
    </testsuite>
    <filter>
        <whitelist addUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./src</directory>
        </whitelist>
    </filter>
</phpunit>
