<?php
/**
 * Simple System Settings Page
 * Minimal working version for testing
 */

session_start();

// Include required files
require_once 'includes/auth.php';

// Require super admin authentication
requireSuperAdminAuth();

$page_title = 'System Settings';
$page_subtitle = 'Configure system-wide settings and contact information';

$success = '';
$errors = [];

// Simple function to get settings
function getSimpleSettings() {
    try {
        require_once '../config/database.php';
        $db = getDB();
        
        $settings = [];
        $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings");
        
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }
        
        // Add defaults
        $defaults = [
            'site_name' => 'Online Banking System',
            'site_url' => 'http://localhost/online_banking',
            'support_email' => '<EMAIL>',
            'support_phone' => '1-800-BANKING'
        ];
        
        foreach ($defaults as $key => $default_value) {
            if (!isset($settings[$key])) {
                $settings[$key] = $default_value;
            }
        }
        
        return $settings;
    } catch (Exception $e) {
        error_log("Error getting settings: " . $e->getMessage());
        return [
            'site_name' => 'Online Banking System',
            'site_url' => 'http://localhost/online_banking',
            'support_email' => '<EMAIL>',
            'support_phone' => '1-800-BANKING'
        ];
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        require_once '../config/database.php';
        $db = getDB();
        
        $site_name = $_POST['site_name'] ?? '';
        $site_url = $_POST['site_url'] ?? '';
        $support_email = $_POST['support_email'] ?? '';
        $support_phone = $_POST['support_phone'] ?? '';
        
        // Simple validation
        if (empty($site_name)) {
            $errors[] = 'Site name is required';
        }
        if (!filter_var($support_email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email format';
        }
        
        if (empty($errors)) {
            // Update settings
            $settings_to_update = [
                'site_name' => $site_name,
                'site_url' => $site_url,
                'support_email' => $support_email,
                'support_phone' => $support_phone
            ];
            
            foreach ($settings_to_update as $key => $value) {
                $sql = "INSERT INTO super_admin_settings (setting_key, setting_value) 
                        VALUES (?, ?) 
                        ON DUPLICATE KEY UPDATE 
                        setting_value = VALUES(setting_value)";
                $db->query($sql, [$key, $value]);
            }
            
            $success = 'Settings updated successfully!';
            
            // Log the action
            logSuperAdminAction('settings_update', 'Updated system settings');
        }
        
    } catch (Exception $e) {
        $errors[] = 'Error updating settings: ' . $e->getMessage();
    }
}

// Get current settings
$current_settings = getSimpleSettings();

// Include header
include 'includes/header.php';
?>

<!-- Success/Error Messages -->
<?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle"></i> 
        <strong>Errors:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<form method="POST" class="needs-validation" novalidate>
    <!-- Site Configuration -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-globe"></i> Site Configuration
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_name" class="form-label">Site Name</label>
                        <input type="text" class="form-control" id="site_name" name="site_name" 
                               value="<?php echo htmlspecialchars($current_settings['site_name'] ?? ''); ?>" required>
                        <div class="form-text">Name displayed throughout the system and in emails</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_url" class="form-label">Site URL</label>
                        <input type="url" class="form-control" id="site_url" name="site_url" 
                               value="<?php echo htmlspecialchars($current_settings['site_url'] ?? ''); ?>" required>
                        <div class="form-text">Base URL of your banking platform</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Contact Information -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-address-book"></i> Contact Information
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="support_email" class="form-label">Support Email</label>
                        <input type="email" class="form-control" id="support_email" name="support_email" 
                               value="<?php echo htmlspecialchars($current_settings['support_email'] ?? ''); ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="support_phone" class="form-label">Support Phone</label>
                        <input type="text" class="form-control" id="support_phone" name="support_phone" 
                               value="<?php echo htmlspecialchars($current_settings['support_phone'] ?? ''); ?>" required>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="d-flex justify-content-between">
        <a href="dashboard.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
        
        <div>
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save"></i> Update Settings
            </button>
        </div>
    </div>
</form>

<div class="mt-4">
    <div class="card">
        <div class="card-header">
            <h6>Debug Information</h6>
        </div>
        <div class="card-body">
            <p><strong>Current Settings:</strong></p>
            <pre><?php echo htmlspecialchars(print_r($current_settings, true)); ?></pre>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
