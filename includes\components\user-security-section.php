<?php
/**
 * User Security Section Component
 * Displays OTP management and login activity
 */

if (!isset($user) || !isset($otp_history) || !isset($login_attempts)) {
    throw new Exception('User, OTP history, and login attempts data required for security section');
}

// Find current active OTP
$current_otp = null;
foreach ($otp_history as $otp) {
    if (!$otp['used'] && strtotime($otp['expires_at']) > time()) {
        $current_otp = $otp;
        break;
    }
}
?>

<!-- Security & OTP Management -->
<div class="row row-cards mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-shield-alt me-2"></i>
                    Security & Authentication
                </h3>
            </div>
            <div class="card-body security-card">
                <!-- Current OTP Status -->
                <?php if ($current_otp): ?>
                <div class="alert alert-info mb-3">
                    <div class="d-flex">
                        <div><i class="fas fa-key me-2"></i></div>
                        <div>
                            <h4 class="alert-title">Active OTP Code</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Code:</strong> <span class="font-monospace badge bg-primary"><?php echo htmlspecialchars($current_otp['otp_code']); ?></span><br>
                                    <strong>Source:</strong> <?php echo ucfirst($current_otp['source'] ?? 'login'); ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Expires:</strong> <?php echo formatDate($current_otp['expires_at'], 'M j, Y g:i A'); ?><br>
                                    <strong>Created:</strong> <?php echo formatDate($current_otp['created_at'], 'M j, Y g:i A'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-secondary mb-3">
                    <div class="d-flex">
                        <div><i class="fas fa-info-circle me-2"></i></div>
                        <div>
                            <h4 class="alert-title">No Active OTP</h4>
                            <p class="mb-0">This user does not currently have an active OTP code.</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- OTP Management Actions -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card card-sm bg-light otp-management-card">
                            <div class="card-body text-center">
                                <i class="fas fa-key text-primary mb-2" style="font-size: 1.5rem;"></i>
                                <h4 class="card-title">Generate OTP</h4>
                                <p class="text-muted mb-3">Create a new OTP code for user support</p>
                                <button class="btn btn-primary btn-sm" onclick="generateOTPForUser(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-plus me-1"></i>
                                    Generate New OTP
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-sm bg-light otp-management-card">
                            <div class="card-body text-center">
                                <i class="fas fa-history text-info mb-2" style="font-size: 1.5rem;"></i>
                                <h4 class="card-title">OTP History</h4>
                                <p class="text-muted mb-3">View recent OTP codes and usage</p>
                                <button class="btn btn-info btn-sm" onclick="showOTPHistory()">
                                    <i class="fas fa-list me-1"></i>
                                    View History
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Login Activity
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($login_attempts)): ?>
                <div class="list-group list-group-flush">
                    <?php foreach (array_slice($login_attempts, 0, 5) as $attempt): ?>
                    <div class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">
                                <?php echo $attempt['success'] ? 'Successful' : 'Failed'; ?>
                            </div>
                            <small class="text-muted">
                                <?php echo formatDate($attempt['attempted_at'], 'M j, g:i A'); ?>
                            </small>
                        </div>
                        <span class="badge bg-<?php echo $attempt['success'] ? 'success' : 'danger'; ?> rounded-pill">
                            <?php echo htmlspecialchars($attempt['ip_address']); ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle mb-2"></i>
                    <p>No login attempts recorded</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
