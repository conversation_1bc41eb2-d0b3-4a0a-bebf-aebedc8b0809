<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$user_id = intval($_POST['user_id'] ?? 0);

if ($user_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
    exit;
}

try {
    $db = getDB();
    
    // Get user details
    $user_query = "SELECT id, first_name, last_name, email, username FROM accounts WHERE id = ? AND is_admin = 0";
    $user_result = $db->query($user_query, [$user_id]);
    
    if ($user_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    $user = $user_result->fetch_assoc();
    
    // Generate OTP
    $otp = generateOTP();
    $user_name = $user['first_name'] . ' ' . $user['last_name'];
    
    // Store OTP with 'admin_support' source
    if (storeOTP($user['id'], $otp, 10, 'admin_support')) {
        // Try to send email
        $emailSent = sendOTPEmail($user['email'], $otp, $user_name);
        
        // Log the activity
        logActivity($_SESSION['user_id'], 'Admin generated OTP for user support', 'accounts', $user['id'], null, [
            'target_user' => $user['username'],
            'email_sent' => $emailSent
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'OTP generated successfully',
            'otp_code' => $otp,
            'email_sent' => $emailSent,
            'user_email' => $user['email'],
            'user_name' => $user_name,
            'expires_in' => '10 minutes'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to generate OTP']);
    }
    
} catch (Exception $e) {
    error_log("OTP generation error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while generating OTP']);
}
?>
