<?php declare(strict_types=1);
$meta #
#semval($) $self->semValue
#semval($,%t) $self->semValue
#semval(%n) $stackPos-(%l-%n)
#semval(%n,%t) $stackPos-(%l-%n)

namespace PhpParser\Parser;

use PhpParser\Error;
use PhpParser\Modifiers;
use PhpParser\Node;
use PhpParser\Node\Expr;
use PhpParser\Node\Name;
use PhpParser\Node\Scalar;
use PhpParser\Node\Stmt;
#include;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar file grammar/php.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class #(-p) extends \PhpParser\ParserAbstract
{
#tokenval
    public const %s = %n;
#endtokenval

    protected int $tokenToSymbolMapSize = #(YYMAXLEX);
    protected int $actionTableSize = #(YYLAST);
    protected int $gotoTableSize = #(YYGLAST);

    protected int $invalidSymbol = #(YYBADCH);
    protected int $errorSymbol = #(YYINTERRTOK);
    protected int $defaultAction = #(YYDEFAULT);
    protected int $unexpectedTokenRule = #(YYUNEXPECTED);

    protected int $YY2TBLSTATE = #(YY2TBLSTATE);
    protected int $numNonLeafStates = #(YYNLSTATES);

    protected array $symbolToName = array(
        #listvar terminals
    );

    protected array $tokenToSymbol = array(
        #listvar yytranslate
    );

    protected array $action = array(
        #listvar yyaction
    );

    protected array $actionCheck = array(
        #listvar yycheck
    );

    protected array $actionBase = array(
        #listvar yybase
    );

    protected array $actionDefault = array(
        #listvar yydefault
    );

    protected array $goto = array(
        #listvar yygoto
    );

    protected array $gotoCheck = array(
        #listvar yygcheck
    );

    protected array $gotoBase = array(
        #listvar yygbase
    );

    protected array $gotoDefault = array(
        #listvar yygdefault
    );

    protected array $ruleToNonTerminal = array(
        #listvar yylhs
    );

    protected array $ruleToLength = array(
        #listvar yylen
    );
#if -t

    protected array $productions = array(
        #production-strings;
    );
#endif

    protected function initReduceCallbacks(): void {
        $this->reduceCallbacks = [
#reduce
            %n => static function ($self, $stackPos) {
                %b
            },
#noact
            %n => null,
#endreduce
        ];
    }
}
#tailcode;
