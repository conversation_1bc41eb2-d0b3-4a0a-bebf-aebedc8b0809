# https://help.github.com/en/categories/automating-your-workflow-with-github-actions
name: Main
on:
  push:
  pull_request:

jobs:
  tests_coverage:
    runs-on: "ubuntu-latest"
    name: "PHP 7.4 Unit Tests (with coverage)"
    steps:
      - name: "Checkout"
        uses: "actions/checkout@v4"
      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "xdebug"
          php-version: "7.4"
          tools: composer:v2
      - name: "Install dependencies"
        run: |
          composer require php-coveralls/php-coveralls:^2.2 --dev --no-update
          COMPOSER_ROOT_VERSION=dev-master composer update --no-progress --prefer-dist
      - name: "Tests"
        run: "php vendor/bin/phpunit --coverage-clover build/logs/clover.xml"
      - name: Coveralls
        env:
          COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: "php vendor/bin/php-coveralls"
        if: ${{ success() }}
  tests:
    runs-on: "ubuntu-latest"
    name: "PHP ${{ matrix.php-version }} Unit Tests"
    strategy:
      matrix:
        php-version:
          - "8.0"
          - "8.1"
          - "8.2"
          - "8.3"
          - "8.4"
      fail-fast: false
    steps:
      - name: "Checkout"
        uses: "actions/checkout@v4"
      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "${{ matrix.php-version }}"
          ini-file: "development"
          tools: composer:v2
      - name: "Install dependencies"
        run: "COMPOSER_ROOT_VERSION=dev-master composer update --no-progress --prefer-dist ${{ matrix.flags }}"
      - name: "PHPUnit"
        run: "php vendor/bin/phpunit"
  test_old_73_80:
    runs-on: "ubuntu-latest"
    name: "PHP 7.4 Code on PHP 8.4 Integration Tests"
    steps:
      - name: "Checkout"
        uses: "actions/checkout@v4"
      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "8.4"
          ini-file: "development"
          tools: composer:v2
      - name: "Install PHP 8 dependencies"
        run: "COMPOSER_ROOT_VERSION=dev-master composer update --no-progress --prefer-dist"
      - name: "Tests"
        run: "test_old/run-php-src.sh 7.4.33"
  test_old_80_70:
    runs-on: "ubuntu-latest"
    name: "PHP 8.4 Code on PHP 7.4 Integration Tests"
    steps:
      - name: "Checkout"
        uses: "actions/checkout@v4"
      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "7.4"
          ini-file: "development"
          tools: composer:v2
      - name: "Install PHP 8 dependencies"
        run: "COMPOSER_ROOT_VERSION=dev-master composer update --no-progress --prefer-dist"
      - name: "Tests"
        run: "test_old/run-php-src.sh 8.4.0beta5"
  phpstan:
    runs-on: "ubuntu-latest"
    name: "PHPStan"
    steps:
      - name: "Checkout"
        uses: "actions/checkout@v4"
      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "8.3"
          tools: composer:v2
      - name: "Install dependencies"
        run: |
          cd tools && composer install
      - name: "PHPStan"
        run: "php tools/vendor/bin/phpstan"
  php-cs-fixer:
    runs-on: "ubuntu-latest"
    name: "PHP-CS-Fixer"
    steps:
      - name: "Checkout"
        uses: "actions/checkout@v4"
      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "8.3"
          tools: composer:v2
      - name: "Install dependencies"
        run: |
          cd tools && composer install
      - name: "php-cs-fixer"
        run: "php tools/vendor/bin/php-cs-fixer fix --dry-run"
