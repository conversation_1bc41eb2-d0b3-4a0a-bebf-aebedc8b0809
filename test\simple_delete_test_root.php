<?php
/**
 * Simple Delete Function Test - No Auth Required
 * This script tests basic components without requiring admin login
 */

echo "<h1>Delete Function Component Test</h1>\n";
echo "<pre>\n";

try {
    // Test 1: Check if files exist
    echo "1. Checking file existence...\n";
    $files_to_check = [
        'config/config.php',
        'config/database.php', 
        'admin/delete-user.php'
    ];
    
    foreach ($files_to_check as $file) {
        if (file_exists($file)) {
            echo "   ✓ $file exists\n";
        } else {
            echo "   ✗ $file missing\n";
        }
    }
    
    // Test 2: Check PHP syntax
    echo "\n2. Checking PHP syntax...\n";
    $syntax_check = shell_exec('php -l admin/delete-user.php 2>&1');
    if (strpos($syntax_check, 'No syntax errors') !== false) {
        echo "   ✓ delete-user.php has valid PHP syntax\n";
    } else {
        echo "   ✗ Syntax error in delete-user.php:\n";
        echo "   " . $syntax_check . "\n";
    }
    
    // Test 3: Check key functions in delete-user.php
    echo "\n3. Analyzing delete-user.php structure...\n";
    $delete_file_content = file_get_contents('admin/delete-user.php');
    
    $required_functions = [
        'sendJsonResponse',
        'getUserForDeletion', 
        'performSecurityChecks',
        'executeUserDeletion',
        'logUserDeletion'
    ];
    
    foreach ($required_functions as $func) {
        if (strpos($delete_file_content, "function $func") !== false) {
            echo "   ✓ Function '$func' found\n";
        } else {
            echo "   ✗ Function '$func' missing\n";
        }
    }
    
    // Test 4: Check security measures
    echo "\n4. Checking security measures...\n";
    $security_checks = [
        'requireAdmin()' => 'Admin authentication check',
        'performSecurityChecks' => 'Security validation function',
        'beginTransaction' => 'Database transaction support',
        'is_admin' => 'Admin role checking',
        'current_admin_id' => 'Self-deletion prevention'
    ];
    
    foreach ($security_checks as $check => $description) {
        if (strpos($delete_file_content, $check) !== false) {
            echo "   ✓ $description found\n";
        } else {
            echo "   ✗ $description missing\n";
        }
    }
    
    // Test 5: Check database table handling
    echo "\n5. Checking database table handling...\n";
    $tables_handled = [
        'user_otps' => 'OTP records deletion',
        'beneficiaries' => 'Beneficiaries deletion', 
        'virtual_cards' => 'Virtual cards deletion',
        'transfers' => 'Transfers handling',
        'tickets' => 'Tickets handling',
        'audit_logs' => 'Audit logs preservation'
    ];
    
    foreach ($tables_handled as $table => $description) {
        if (strpos($delete_file_content, $table) !== false) {
            echo "   ✓ $description implemented\n";
        } else {
            echo "   ✗ $description missing\n";
        }
    }
    
    echo "\n=== Analysis Summary ===\n";
    echo "The new delete-user.php implementation includes:\n";
    echo "• Comprehensive security checks\n";
    echo "• Transaction-based deletion\n";
    echo "• Proper handling of related records\n";
    echo "• JSON response format for AJAX\n";
    echo "• Audit logging\n";
    echo "• Error handling and rollback\n\n";
    
    echo "Next steps for testing:\n";
    echo "1. Start MAMP server if not running\n";
    echo "2. Login to admin panel\n";
    echo "3. Visit: test_delete_functionality.php\n";
    echo "4. Run security and deletion tests\n";
    
} catch (Exception $e) {
    echo "Error during analysis: " . $e->getMessage() . "\n";
}

echo "</pre>\n";
?>

<h2>Quick Integration Test</h2>
<p>If you're logged in as admin, you can test the delete functionality:</p>
<button onclick="testDeleteAPI()">Test Delete API (Invalid User)</button>
<div id="testResult" style="margin-top: 10px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; font-family: monospace; white-space: pre-wrap; min-height: 100px;">
Test results will appear here...
</div>

<script>
async function testDeleteAPI() {
    const resultDiv = document.getElementById('testResult');
    resultDiv.textContent = 'Testing delete API with invalid user ID...\n';
    
    try {
        const response = await fetch('admin/delete-user.php?id=99999&ajax=1', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });
        
        const text = await response.text();
        
        try {
            const data = JSON.parse(text);
            resultDiv.textContent += 'SUCCESS: API returned valid JSON response\n';
            resultDiv.textContent += 'Response: ' + JSON.stringify(data, null, 2) + '\n';
            
            if (!data.success) {
                resultDiv.textContent += '\n✓ API correctly rejected invalid user ID\n';
                resultDiv.textContent += '✓ Error message: ' + data.message + '\n';
            } else {
                resultDiv.textContent += '\n✗ API incorrectly accepted invalid user ID\n';
            }
        } catch (e) {
            resultDiv.textContent += 'ERROR: API did not return valid JSON\n';
            resultDiv.textContent += 'Raw response: ' + text.substring(0, 500) + '\n';
        }
        
    } catch (error) {
        resultDiv.textContent += 'NETWORK ERROR: ' + error.message + '\n';
    }
}
</script>
