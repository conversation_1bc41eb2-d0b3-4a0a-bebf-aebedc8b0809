<?php
// Test script to directly call the delete-user endpoint
// This test will call the delete-user.php script from within the admin directory

// Change to admin directory context
chdir(__DIR__ . '/admin');

// Simulate the AJAX request environment
$_SERVER['REQUEST_METHOD'] = 'POST';
$_GET['id'] = '9'; // Test user ID
$_GET['ajax'] = '1';

// Start output buffering to capture the response
ob_start();

// Include the delete-user.php file
try {
    include 'delete-user.php';
    $response = ob_get_contents();
    ob_end_clean();
    
    echo "<h2>Delete User Test Result</h2><hr>";
    echo "<h3>Response from delete-user.php:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    // Try to decode as JSON
    $json_response = json_decode($response, true);
    if ($json_response) {
        echo "<h3>Parsed JSON Response:</h3>";
        echo "<ul>";
        echo "<li><strong>Success:</strong> " . ($json_response['success'] ? 'Yes' : 'No') . "</li>";
        echo "<li><strong>Message:</strong> " . htmlspecialchars($json_response['message']) . "</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<h2>Test Error</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
