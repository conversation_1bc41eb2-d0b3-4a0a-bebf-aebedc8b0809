/**
 * Document Management JavaScript
 * Fresh implementation with comprehensive error handling
 */

// Ensure DOM is ready before executing functions
(function() {
    'use strict';

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeDocumentManagement);
    } else {
        initializeDocumentManagement();
    }

    function initializeDocumentManagement() {
        console.log('Document management system initialized');

        // Ensure jQuery is available
        if (typeof $ === 'undefined') {
            console.warn('jQuery not available, some features may not work');
        }

        // Make functions globally available
        window.uploadDocument = uploadDocument;
        window.viewDocument = viewDocument;
        window.approveDocument = approveDocument;
        window.rejectDocument = rejectDocument;
        window.deleteDocument = deleteDocument;
        window.displayDocumentDetails = displayDocumentDetails;
    }
})();

// Helper functions
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getDocumentStatusColor(status) {
    switch(status) {
        case 'approved': return 'success';
        case 'rejected': return 'danger';
        case 'pending': return 'warning';
        default: return 'secondary';
    }
}

// Document upload function
function uploadDocument(userId) {
    const modal = `
        <div class="modal modal-blur fade" id="uploadDocumentModal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Upload Document</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="uploadDocumentForm" enctype="multipart/form-data">
                            <input type="hidden" name="user_id" value="${userId}">
                            <div class="mb-3">
                                <label class="form-label">Document Type</label>
                                <select class="form-select" name="document_type" required>
                                    <option value="">Select document type</option>
                                    <option value="passport">Passport</option>
                                    <option value="id_card">ID Card</option>
                                    <option value="drivers_license">Driver's License</option>
                                    <option value="utility_bill">Utility Bill</option>
                                    <option value="bank_statement">Bank Statement</option>
                                    <option value="cheque">Cheque</option>
                                    <option value="kyc_selfie">KYC Selfie</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Document File</label>
                                <input type="file" class="form-control" name="document_file" accept=".pdf,.jpg,.jpeg,.png" required>
                                <small class="form-hint">Accepted formats: PDF, JPG, PNG (Max 10MB)</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Notes (Optional)</label>
                                <textarea class="form-control" name="notes" rows="3" placeholder="Additional notes about this document"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="submitDocumentUpload()">
                            <i class="fas fa-upload me-1"></i>
                            Upload Document
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    $('#uploadDocumentModal').remove();

    // Add modal to body and show
    $('body').append(modal);
    $('#uploadDocumentModal').modal('show');
}

// Submit document upload
function submitDocumentUpload() {
    try {
        const form = document.getElementById('uploadDocumentForm');
        if (!form) {
            throw new Error('Upload form not found');
        }

        const formData = new FormData(form);
        
        // Show loading state
        const submitBtn = document.querySelector('#uploadDocumentModal .btn-primary');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
        submitBtn.disabled = true;

        fetch('ajax/upload-document.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showSuccessMessage('Document uploaded successfully!');
                $('#uploadDocumentModal').modal('hide');
                setTimeout(() => location.reload(), 1000);
            } else {
                throw new Error(data.message || 'Upload failed');
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            showErrorMessage('Error uploading document: ' + error.message);
        })
        .finally(() => {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });

    } catch (error) {
        console.error('Submit error:', error);
        showErrorMessage('Error: ' + error.message);
    }
}

// View document function
function viewDocument(documentId) {
    try {
        console.log('viewDocument called with ID:', documentId);
        
        if (!documentId || isNaN(documentId)) {
            throw new Error('Invalid document ID');
        }

        // Show loading state
        const loadingContent = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="text-muted">Loading document details...</p>
            </div>
        `;

        // Try to get the content element safely
        let contentElement = null;
        try {
            contentElement = document.getElementById('documentPreviewContent');
        } catch (e) {
            console.error('Error getting content element:', e);
        }

        if (contentElement) {
            contentElement.innerHTML = loadingContent;

            // Show modal safely
            try {
                if (typeof $ !== 'undefined' && $.fn.modal) {
                    $('#documentPreviewModal').modal('show');
                } else {
                    console.warn('Bootstrap modal not available');
                }
            } catch (e) {
                console.error('Error showing modal:', e);
            }
        } else {
            throw new Error('Document preview modal not found in DOM');
        }

        // Load document details
        fetch(`ajax/get_user_document_details.php?id=${documentId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    displayDocumentDetails(data.document);
                } else {
                    throw new Error(data.message || 'Failed to load document');
                }
            })
            .catch(error => {
                console.error('View document error:', error);
                showDocumentError(error.message);
            });

    } catch (error) {
        console.error('viewDocument error:', error);
        showErrorMessage('Error: ' + error.message);
    }
}

// Display document details in modal
function displayDocumentDetails(document) {
    try {
        console.log('displayDocumentDetails called with:', document);

        // Check if document object exists
        if (!document) {
            throw new Error('Document data is missing');
        }

        // Ensure DOM is ready and document object exists
        if (typeof window.document === 'undefined' || !window.document) {
            throw new Error('Document object not available');
        }

        // Check if getElementById function exists
        if (typeof window.document.getElementById !== 'function') {
            throw new Error('getElementById function not available');
        }

        // Ensure required properties exist
        const requiredProps = ['document_type', 'document_name', 'file_size', 'verification_status', 'first_name', 'last_name'];
        for (const prop of requiredProps) {
            if (!document[prop]) {
                document[prop] = 'N/A';
            }
        }

        // Add file extension if missing
        if (!document.file_extension && document.file_path) {
            document.file_extension = document.file_path.split('.').pop().toLowerCase();
        }

        // Use uploaded_at or created_at
        const uploadDate = document.uploaded_at || document.created_at || new Date().toISOString();

        const modalContent = `
            <div class="row">
                <div class="col-md-6">
                    <h5>Document Information</h5>
                    <dl class="row">
                        <dt class="col-sm-5 border-bottom border-light pb-2 mb-3">Document Type:</dt>
                        <dd class="col-sm-7 border-bottom border-light pb-2 mb-3">${document.document_type.replace('_', ' ').split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}</dd>

                        <dt class="col-sm-5 border-bottom border-light pb-2 mb-3">Document Name:</dt>
                        <dd class="col-sm-7 border-bottom border-light pb-2 mb-3">${document.document_name}</dd>

                        <dt class="col-sm-5 border-bottom border-light pb-2 mb-3">File Size:</dt>
                        <dd class="col-sm-7 border-bottom border-light pb-2 mb-3">${formatBytes(document.file_size)}</dd>

                        <dt class="col-sm-5 border-bottom border-light pb-2 mb-3">Upload Date:</dt>
                        <dd class="col-sm-7 border-bottom border-light pb-2 mb-3">${formatDate(uploadDate)}</dd>

                        <dt class="col-sm-5 pb-2">Status:</dt>
                        <dd class="col-sm-7 pb-2">
                            <span class="badge bg-${getDocumentStatusColor(document.verification_status)}">${document.verification_status.charAt(0).toUpperCase() + document.verification_status.slice(1)}</span>
                        </dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    <h5>Additional Information</h5>
                    <dl class="row">
                        <dt class="col-sm-5 pb-2">File Type:</dt>
                        <dd class="col-sm-7 pb-2">${(document.file_extension || 'unknown').toUpperCase()}</dd>
                    </dl>

                    ${document.notes ? `
                    <h5 class="mt-3">Notes</h5>
                    <div class="alert alert-info">
                        ${document.notes}
                    </div>
                    ` : ''}
                </div>
            </div>

            <div class="mt-4">
                <h5>Document Preview</h5>
                <div class="text-center">
                    ${document.file_path ? generatePreview(document) : '<p class="text-muted">No document file available</p>'}
                </div>
            </div>
        `;

        // Try multiple ways to get the element
        let contentElement = null;

        try {
            contentElement = window.document.getElementById('documentPreviewContent');
        } catch (e) {
            console.error('Error with window.document.getElementById:', e);
        }

        if (!contentElement) {
            try {
                contentElement = document.getElementById('documentPreviewContent');
            } catch (e) {
                console.error('Error with document.getElementById:', e);
            }
        }

        if (!contentElement) {
            try {
                contentElement = document.querySelector('#documentPreviewContent');
            } catch (e) {
                console.error('Error with document.querySelector:', e);
            }
        }

        if (!contentElement) {
            throw new Error('Modal content element not found in DOM');
        }

        console.log('Setting modal content...');
        contentElement.innerHTML = modalContent;
        console.log('Modal content set successfully');

    } catch (error) {
        console.error('Display error:', error);
        showDocumentError(error.message);
    }
}

// Generate preview based on file type
function generatePreview(document) {
    const fileExt = (document.file_extension || '').toLowerCase();
    
    if (fileExt === 'pdf') {
        return `
            <embed src="${document.file_path}" type="application/pdf" width="100%" height="500px" style="border: 1px solid #ddd; border-radius: 8px;">
            <br><br>
            <a href="${document.file_path}" target="_blank" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-external-link-alt me-1"></i>
                View Full Size PDF
            </a>
        `;
    } else if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
        return `
            <img src="${document.file_path}" alt="Document Image" class="img-fluid" style="max-height: 500px; border: 1px solid #ddd; border-radius: 8px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div style="display: none; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa;">
                <i class="fas fa-file-alt" style="font-size: 3rem; color: #6c757d;"></i>
                <p class="mt-2 text-muted">Preview not available for this file type</p>
            </div>
            <br><br>
            <a href="${document.file_path}" target="_blank" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-external-link-alt me-1"></i>
                View Full Size
            </a>
        `;
    } else {
        return `
            <div class="p-4" style="border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa;">
                <i class="fas fa-file-alt" style="font-size: 3rem; color: #6c757d;"></i>
                <p class="mt-2 text-muted">Preview not available for this file type</p>
                <a href="${document.file_path}" target="_blank" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Download File
                </a>
            </div>
        `;
    }
}

// Show error in document modal
function showDocumentError(message) {
    const errorContent = `
        <div class="text-center py-5">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Error loading document:</strong><br>
                ${message}
            </div>
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
    `;

    // Try multiple ways to get the content element
    let contentElement = null;

    try {
        contentElement = document.getElementById('documentPreviewContent');
    } catch (e) {
        console.error('Error getting content element for error display:', e);
    }

    if (!contentElement) {
        try {
            contentElement = document.querySelector('#documentPreviewContent');
        } catch (e) {
            console.error('Error with querySelector for error display:', e);
        }
    }

    if (contentElement) {
        contentElement.innerHTML = errorContent;

        // Try to show the modal if it's not already visible
        try {
            if (typeof $ !== 'undefined' && $.fn.modal) {
                $('#documentPreviewModal').modal('show');
            }
        } catch (e) {
            console.error('Error showing error modal:', e);
        }
    } else {
        // Fallback to alert if modal is not available
        console.error('Modal content element not found, showing alert instead');
        showErrorMessage('Error loading document: ' + message);
    }
}

// Success message function
function showSuccessMessage(message) {
    // You can customize this to use your preferred notification system
    alert(message);
}

// Error message function
function showErrorMessage(message) {
    // You can customize this to use your preferred notification system
    alert(message);
}

// Document approval function
function approveDocument(documentId) {
    showDocumentConfirmModal(documentId, 'approved', 'Approve Document', 'Are you sure you want to approve this document?', 'success');
}

// Document rejection function
function rejectDocument(documentId) {
    showDocumentRejectModal(documentId);
}

// Document deletion function
function deleteDocument(documentId) {
    showDocumentConfirmModal(documentId, 'delete', 'Delete Document', 'Are you sure you want to permanently delete this document?', 'danger');
}

// Show confirmation modal
function showDocumentConfirmModal(documentId, action, title, message, buttonColor) {
    const modal = `
        <div class="modal modal-blur fade" id="documentConfirmModal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-${action === 'approved' ? 'check-circle' : (action === 'delete' ? 'trash' : 'question-circle')} text-${buttonColor}" style="font-size: 2rem;"></i>
                            </div>
                            <div>
                                <h4 class="mb-2">${title}</h4>
                                <p class="text-muted mb-0">${message}</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-${buttonColor}" onclick="confirmDocumentAction(${documentId}, '${action}')">
                            <i class="fas fa-${action === 'approved' ? 'check' : (action === 'delete' ? 'trash' : 'times')} me-2"></i>
                            ${action === 'approved' ? 'Approve' : (action === 'delete' ? 'Delete' : 'Confirm')}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#documentConfirmModal').remove();
    $('body').append(modal);
    $('#documentConfirmModal').modal('show');
}

// Show rejection modal with reason
function showDocumentRejectModal(documentId) {
    const modal = `
        <div class="modal modal-blur fade" id="documentRejectModal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Reject Document</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="d-flex mb-3">
                            <div class="me-3">
                                <i class="fas fa-times-circle text-danger" style="font-size: 2rem;"></i>
                            </div>
                            <div>
                                <h4 class="mb-2">Reject Document</h4>
                                <p class="text-muted mb-0">Please provide a reason for rejecting this document.</p>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Rejection Reason</label>
                            <textarea class="form-control" id="rejectionReason" rows="3" placeholder="Enter reason for rejection..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" onclick="confirmDocumentReject(${documentId})">
                            <i class="fas fa-times me-2"></i>
                            Reject Document
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#documentRejectModal').remove();
    $('body').append(modal);
    $('#documentRejectModal').modal('show');
}

// Confirm document action
function confirmDocumentAction(documentId, action) {
    $('#documentConfirmModal').modal('hide');
    
    if (action === 'delete') {
        updateDocumentStatus(documentId, action);
    } else {
        updateDocumentStatus(documentId, action);
    }
}

// Confirm document rejection
function confirmDocumentReject(documentId) {
    const reason = document.getElementById('rejectionReason').value.trim();
    if (!reason) {
        showErrorMessage('Please provide a reason for rejection');
        return;
    }

    $('#documentRejectModal').modal('hide');
    updateDocumentStatus(documentId, 'rejected', reason);
}

// Update document status
function updateDocumentStatus(documentId, status, reason = '') {
    const endpoint = status === 'delete' ? 'ajax/delete-document.php' : 'ajax/update-document-status.php';
    
    fetch(endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            document_id: documentId,
            status: status,
            reason: reason
        })
    })
    .then(response => {
        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        // Check if response has content
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Response is not JSON');
        }
        
        return response.text().then(text => {
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Invalid JSON response:', text);
                throw new Error('Invalid JSON response from server');
            }
        });
    })
    .then(data => {
        if (data.success) {
            showSuccessMessage(`Document ${status === 'delete' ? 'deleted' : 'status updated'} successfully!`);
            setTimeout(() => location.reload(), 1000);
        } else {
            throw new Error(data.message || 'Operation failed');
        }
    })
    .catch(error => {
        console.error('Update error:', error);
        showErrorMessage('Error: ' + error.message);
    });
}

// Debug information
console.log('Document management JavaScript loaded successfully');
console.log('DOM ready state:', document.readyState);
console.log('jQuery available:', typeof $ !== 'undefined');
console.log('Bootstrap modal available:', typeof $ !== 'undefined' && $.fn && $.fn.modal);

// Test function to verify everything is working
window.testDocumentManagement = function() {
    console.log('Testing document management system...');
    console.log('document.getElementById available:', typeof document.getElementById === 'function');
    console.log('Modal element exists:', !!document.getElementById('documentPreviewModal'));
    console.log('Content element exists:', !!document.getElementById('documentPreviewContent'));
    return 'Document management system test completed - check console for details';
};

// Fallback simple view function in case of issues
window.simpleViewDocument = function(documentId) {
    console.log('Using simple view document fallback for ID:', documentId);

    // Simple AJAX call without complex error handling
    fetch(`ajax/get_user_document_details.php?id=${documentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Simple alert with document info
                const doc = data.document;
                const info = `Document: ${doc.document_name}\nType: ${doc.document_type}\nStatus: ${doc.verification_status}\nSize: ${doc.file_size} bytes`;
                alert(info);

                // Try to open file in new window
                if (doc.file_path) {
                    window.open(doc.file_path, '_blank');
                }
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error loading document: ' + error.message);
        });
};
