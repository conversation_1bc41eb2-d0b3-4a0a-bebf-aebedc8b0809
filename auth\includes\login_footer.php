<?php
/**
 * User Login Page Footer
 * Contains closing HTML tags and additional scripts
 */
?>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Additional Security Scripts -->
<script>
// Prevent form resubmission on page refresh
if (window.history.replaceState) {
    window.history.replaceState(null, null, window.location.href);
}

// Clear form data on page unload for security
window.addEventListener('beforeunload', function() {
    const form = document.getElementById('loginForm');
    if (form) {
        form.reset();
    }
});

// Disable right-click context menu on sensitive areas
document.addEventListener('contextmenu', function(e) {
    if (e.target.closest('.login-form')) {
        e.preventDefault();
    }
});

// Basic protection against automated attacks
let loginAttempts = 0;
const maxAttempts = 5;

document.getElementById('loginForm')?.addEventListener('submit', function(e) {
    loginAttempts++;
    
    if (loginAttempts > maxAttempts) {
        e.preventDefault();
        alert('Too many login attempts. Please refresh the page and try again.');
        return false;
    }
});

// Session timeout warning (if user is logged in)
<?php if (isLoggedIn()): ?>
let sessionWarningShown = false;
const sessionTimeout = <?php echo SESSION_TIMEOUT; ?> * 1000; // Convert to milliseconds
const warningTime = sessionTimeout - (5 * 60 * 1000); // 5 minutes before timeout

setTimeout(function() {
    if (!sessionWarningShown) {
        sessionWarningShown = true;
        if (confirm('Your session will expire in 5 minutes. Would you like to extend it?')) {
            // Extend session via AJAX
            fetch('auth/extend-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    sessionWarningShown = false;
                    console.log('Session extended successfully');
                }
            })
            .catch(error => {
                console.error('Error extending session:', error);
            });
        }
    }
}, warningTime);
<?php endif; ?>

// Accessibility enhancements
document.addEventListener('keydown', function(e) {
    // Allow Escape key to clear form
    if (e.key === 'Escape') {
        const form = document.getElementById('loginForm');
        if (form && confirm('Clear the login form?')) {
            form.reset();
            document.getElementById('username')?.focus();
        }
    }
    
    // Allow Enter key to submit form from any input
    if (e.key === 'Enter' && e.target.matches('input')) {
        const form = e.target.closest('form');
        if (form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn && !submitBtn.disabled) {
                submitBtn.click();
            }
        }
    }
});

// Auto-focus on username field when page loads
window.addEventListener('load', function() {
    const usernameField = document.getElementById('username');
    if (usernameField && !usernameField.value) {
        usernameField.focus();
    }
});

// Form validation enhancements
function validateLoginForm() {
    const username = document.getElementById('username')?.value.trim();
    const password = document.getElementById('password')?.value;
    
    if (!username) {
        showValidationError('username', 'Username is required');
        return false;
    }
    
    if (!password) {
        showValidationError('password', 'Password is required');
        return false;
    }
    
    if (password.length < 6) {
        showValidationError('password', 'Password must be at least 6 characters');
        return false;
    }
    
    return true;
}

function showValidationError(fieldId, message) {
    const field = document.getElementById(fieldId);
    if (field) {
        field.style.borderColor = '#dc2626';
        field.focus();
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.validation-error');
        if (existingError) {
            existingError.remove();
        }
        
        // Add new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-error';
        errorDiv.style.color = '#dc2626';
        errorDiv.style.fontSize = '12px';
        errorDiv.style.marginTop = '4px';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
        
        // Remove error styling when user starts typing
        field.addEventListener('input', function() {
            this.style.borderColor = '#d1d5db';
            const error = this.parentNode.querySelector('.validation-error');
            if (error) {
                error.remove();
            }
        }, { once: true });
    }
}

// Performance monitoring
const pageLoadTime = performance.now();
window.addEventListener('load', function() {
    const loadTime = performance.now() - pageLoadTime;
    console.log(`Login page loaded in ${loadTime.toFixed(2)}ms`);
});
</script>

<!-- Analytics and Monitoring (if enabled) -->
<?php if (defined('ANALYTICS_ENABLED') && ANALYTICS_ENABLED): ?>
<script>
// Basic page view tracking for login page
if (typeof gtag !== 'undefined') {
    gtag('event', 'page_view', {
        page_title: 'User Login',
        page_location: window.location.href
    });
}
</script>
<?php endif; ?>

</body>
</html>
