<?php
/**
 * Direct Test for New Delete User Functionality
 * This script directly tests the delete functionality without UI
 */

// First, let's test if we can include the necessary files without errors
echo "Testing delete-user.php inclusion and basic functionality...\n\n";

try {
    // Test 1: Check if config files load properly
    echo "1. Testing config inclusion...\n";
    require_once 'config/config.php';
    echo "   ✓ Config loaded successfully\n";
    
    // Test 2: Check database connection
    echo "2. Testing database connection...\n";
    $db = getDB();
    echo "   ✓ Database connected successfully\n";
    
    // Test 3: Check if we can query users table
    echo "3. Testing users table access...\n";
    $result = $db->query("SELECT COUNT(*) as count FROM accounts");
    $count = $result->fetch_assoc()['count'];
    echo "   ✓ Found {$count} accounts in database\n";
    
    // Test 4: Check admin authentication functions
    echo "4. Testing authentication functions...\n";
    if (function_exists('requireAdmin')) {
        echo "   ✓ requireAdmin() function exists\n";
    } else {
        echo "   ✗ requireAdmin() function missing\n";
    }
    
    // Test 5: Check if we're logged in as admin for testing
    echo "5. Checking admin session...\n";
    session_start();
    if (isset($_SESSION['user_id']) && isset($_SESSION['is_admin'])) {
        echo "   ✓ Admin session active (User ID: {$_SESSION['user_id']})\n";
    } else {
        echo "   ⚠ No admin session - would need to login for actual testing\n";
        // Set a test session for validation
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'test_admin';
        $_SESSION['is_admin'] = true;
        echo "   ✓ Test admin session created\n";
    }
    
    // Test 6: Check table structure
    echo "6. Checking required tables...\n";
    $required_tables = ['accounts', 'transfers', 'beneficiaries', 'tickets', 'audit_logs'];
    foreach ($required_tables as $table) {
        try {
            $result = $db->query("SHOW TABLES LIKE '$table'");
            if ($result->num_rows > 0) {
                echo "   ✓ Table '$table' exists\n";
            } else {
                echo "   ✗ Table '$table' missing\n";
            }
        } catch (Exception $e) {
            echo "   ✗ Error checking table '$table': " . $e->getMessage() . "\n";
        }
    }
    
    // Test 7: Test the delete user function components
    echo "7. Testing delete function components...\n";
    
    // Simulate a delete request for a non-existent user
    $_GET['id'] = 99999;
    $_GET['ajax'] = 1;
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    // Capture output
    ob_start();
    try {
        include 'admin/delete-user.php';
        $output = ob_get_clean();
        
        // Try to decode JSON response
        $response = json_decode($output, true);
        if ($response) {
            if (!$response['success']) {
                echo "   ✓ Delete function properly rejected invalid user ID\n";
                echo "   ✓ Response: " . $response['message'] . "\n";
            } else {
                echo "   ✗ Delete function incorrectly accepted invalid user ID\n";
            }
        } else {
            echo "   ✗ Delete function did not return valid JSON\n";
            echo "   Raw output: " . substr($output, 0, 200) . "...\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "   ✗ Error testing delete function: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== Test Summary ===\n";
    echo "The new delete-user.php file appears to be properly structured.\n";
    echo "For full testing, please:\n";
    echo "1. Access the admin panel and login\n";
    echo "2. Visit test_delete_functionality.php\n";
    echo "3. Run the security tests\n";
    echo "4. Test with actual user deletion\n";
    
} catch (Exception $e) {
    echo "✗ Critical error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
