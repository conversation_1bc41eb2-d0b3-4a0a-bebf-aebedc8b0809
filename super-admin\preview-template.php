<?php
/**
 * Email Template Preview
 * Generates preview of email templates for super admin
 */

session_start();

// Include authentication functions
require_once 'includes/auth.php';

// Require super admin authentication
requireSuperAdminAuth();

// Include email template functions
require_once '../config/email_templates.php';

$template_type = $_GET['type'] ?? '';

// Sample user data for preview
$sample_user_data = [
    'first_name' => 'John',
    'last_name' => 'Doe',
    'username' => 'johndoe',
    'email' => '<EMAIL>',
    'account_number' => '**********',
    'account_type' => 'savings',
    'currency' => 'USD',
    'balance' => 15000.00,
    'status' => 'active'
];

$html_content = '';

try {
    switch ($template_type) {
        case 'welcome':
            $html_content = generateWelcomeEmailTemplate($sample_user_data);
            break;
            
        case 'otp':
            $html_content = generateOTPEmailTemplate($sample_user_data, '123456', 10);
            break;
            
        case 'credit_alert':
            $transaction_data = [
                'transaction_id' => 'TXN123456789',
                'amount' => 2500.00,
                'currency' => 'USD',
                'date_time' => date('F j, Y \a\t g:i A'),
                'sender_name' => 'ABC Corporation',
                'reference' => 'Salary Payment - March 2024',
                'new_balance' => 17500.00
            ];
            $html_content = generateCreditAlertEmailTemplate($sample_user_data, $transaction_data);
            break;
            
        case 'debit_alert':
            $transaction_data = [
                'transaction_id' => 'TXN987654321',
                'amount' => 750.00,
                'currency' => 'USD',
                'date_time' => date('F j, Y \a\t g:i A'),
                'recipient_name' => 'Online Store XYZ',
                'reference' => 'Online Purchase - Electronics',
                'new_balance' => 14250.00
            ];
            $html_content = generateDebitAlertEmailTemplate($sample_user_data, $transaction_data);
            break;
            
        case 'login_alert':
            $login_details = [
                'timestamp' => date('F j, Y \a\t g:i A'),
                'ip_address' => '*************',
                'device' => 'Windows 11 PC',
                'location' => 'New York, United States',
                'browser' => 'Chrome 120.0.6099.129'
            ];
            $html_content = generateLoginAlertEmailTemplate($sample_user_data, $login_details);
            break;
            
        case 'kyc_verified':
            $html_content = generateKYCStatusEmailTemplate($sample_user_data, 'verified');
            break;
            
        case 'kyc_rejected':
            $html_content = generateKYCStatusEmailTemplate($sample_user_data, 'rejected', 'Document quality is insufficient. Please provide clearer images.');
            break;
            
        case 'account_suspension':
            $html_content = generateAccountSuspensionEmailTemplate($sample_user_data, 'Suspicious activity detected - Multiple failed login attempts');
            break;
            
        case 'pin_reset':
            $html_content = generatePINResetEmailTemplate($sample_user_data, 'abc123def456ghi789');
            break;
            
        case 'failed_transaction':
            $transaction_data = [
                'transaction_id' => 'TXN_FAILED_001',
                'amount' => 5000.00,
                'currency' => 'USD',
                'date_time' => date('F j, Y \a\t g:i A'),
                'recipient_name' => 'External Bank Account',
                'reference' => 'Wire Transfer'
            ];
            $html_content = generateFailedTransactionEmailTemplate($sample_user_data, $transaction_data, 'Insufficient funds');
            break;
            
        case 'document_request':
            $required_documents = [
                ['name' => 'Government ID', 'description' => 'Valid passport, driver\'s license, or national ID card'],
                ['name' => 'Proof of Address', 'description' => 'Utility bill or bank statement from the last 3 months'],
                ['name' => 'Income Verification', 'description' => 'Recent pay stub or employment letter']
            ];
            $html_content = generateDocumentRequestEmailTemplate($sample_user_data, $required_documents, 'March 31, 2024');
            break;
            
        case 'general_notification':
            $html_content = generateGeneralNotificationEmailTemplate(
                $sample_user_data, 
                'System Maintenance Scheduled', 
                'We will be performing scheduled maintenance on our systems this weekend. During this time, some services may be temporarily unavailable.',
                'info'
            );
            break;
            
        default:
            $html_content = '<div style="padding: 50px; text-align: center; font-family: Arial, sans-serif;">
                <h2 style="color: #dc2626;">Template Not Found</h2>
                <p>The requested email template "' . htmlspecialchars($template_type) . '" was not found.</p>
                <p><a href="email-templates.php" style="color: #dc2626;">← Back to Email Templates</a></p>
            </div>';
    }
    
} catch (Exception $e) {
    $html_content = '<div style="padding: 50px; text-align: center; font-family: Arial, sans-serif;">
        <h2 style="color: #dc2626;">Preview Error</h2>
        <p>An error occurred while generating the template preview:</p>
        <p style="color: #666; font-style: italic;">' . htmlspecialchars($e->getMessage()) . '</p>
        <p><a href="email-templates.php" style="color: #dc2626;">← Back to Email Templates</a></p>
    </div>';
}

// Output the HTML content
echo $html_content;
?>
