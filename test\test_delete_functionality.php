<?php
/**
 * Test Script for New Delete User Functionality
 * This script tests the new delete-user.php implementation
 */

require_once '../config/config.php';
requireAdmin();

$page_title = 'Test Delete User Functionality';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-container { max-width: 1200px; margin: 20px auto; padding: 20px; }
        .test-section { margin-bottom: 30px; }
        .log-area { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; min-height: 200px; font-family: monospace; white-space: pre-wrap; }
        .user-card { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 15px; }
        .user-card.admin { border-color: #dc3545; background-color: #f8d7da; }
        .user-card.deletable { border-color: #28a745; background-color: #d4edda; }
        .user-card.warning { border-color: #ffc107; background-color: #fff3cd; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-vial me-2"></i><?php echo $page_title; ?></h1>
                <p class="text-muted">This page tests the new delete user functionality with comprehensive security checks.</p>
                <hr>
            </div>
        </div>

        <!-- Test Users Section -->
        <div class="test-section">
            <h3><i class="fas fa-users me-2"></i>Available Test Users</h3>
            <div id="testUsers">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> Loading users...
                </div>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="test-section">
            <h3><i class="fas fa-cogs me-2"></i>Test Controls</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-trash me-2"></i>Delete User Test</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="userIdInput" class="form-label">User ID to Delete:</label>
                                <input type="number" class="form-control" id="userIdInput" placeholder="Enter user ID">
                            </div>
                            <button class="btn btn-danger" onclick="testDeleteUser()">
                                <i class="fas fa-trash me-1"></i>Test Delete
                            </button>
                            <button class="btn btn-secondary ms-2" onclick="clearLog()">
                                <i class="fas fa-eraser me-1"></i>Clear Log
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-shield-alt me-2"></i>Security Tests</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-warning btn-sm mb-2 d-block" onclick="testSelfDeletion()">
                                <i class="fas fa-user-slash me-1"></i>Test Self-Deletion Prevention
                            </button>
                            <button class="btn btn-warning btn-sm mb-2 d-block" onclick="testAdminDeletion()">
                                <i class="fas fa-user-shield me-1"></i>Test Admin Deletion Prevention
                            </button>
                            <button class="btn btn-warning btn-sm mb-2 d-block" onclick="testInvalidUser()">
                                <i class="fas fa-question me-1"></i>Test Invalid User ID
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Log -->
        <div class="test-section">
            <h3><i class="fas fa-file-alt me-2"></i>Test Log</h3>
            <div class="log-area" id="testLog">Test log will appear here...\n</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentAdminId = <?php echo $_SESSION['user_id']; ?>;
        
        // Load test users on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadTestUsers();
            logMessage('Test interface loaded. Current admin ID: ' + currentAdminId);
        });

        function logMessage(message, type = 'info') {
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[ERROR]' : type === 'success' ? '[SUCCESS]' : '[INFO]';
            log.textContent += `${timestamp} ${prefix} ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').textContent = 'Test log cleared...\n';
        }

        async function loadTestUsers() {
            try {
                logMessage('Loading available users...');
                const response = await fetch('../config/config.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=get_users'
                });

                // Since we can't modify config.php, let's manually query users
                displaySampleUsers();
                
            } catch (error) {
                logMessage('Error loading users: ' + error.message, 'error');
                displaySampleUsers();
            }
        }

        function displaySampleUsers() {
            const usersContainer = document.getElementById('testUsers');
            usersContainer.innerHTML = `
                <div class="user-card admin">
                    <h6><i class="fas fa-shield-alt me-2"></i>Admin Account (ID: 1)</h6>
                    <p class="mb-1"><strong>Username:</strong> admin</p>
                    <p class="mb-1"><strong>Email:</strong> <EMAIL></p>
                    <p class="mb-0 text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Should NOT be deletable</p>
                </div>
                
                <div class="user-card deletable">
                    <h6><i class="fas fa-user me-2"></i>Regular User (ID: 2)</h6>
                    <p class="mb-1"><strong>Username:</strong> john_doe</p>
                    <p class="mb-1"><strong>Email:</strong> <EMAIL></p>
                    <p class="mb-0 text-success"><i class="fas fa-check me-1"></i>Safe to delete for testing</p>
                </div>

                <div class="user-card warning">
                    <h6><i class="fas fa-user-tie me-2"></i>Current Admin (ID: ${currentAdminId})</h6>
                    <p class="mb-1"><strong>Username:</strong> <?php echo $_SESSION['username']; ?></p>
                    <p class="mb-1"><strong>Email:</strong> <?php echo $_SESSION['email'] ?? 'N/A'; ?></p>
                    <p class="mb-0 text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Self-deletion should be prevented</p>
                </div>
            `;
            logMessage('Sample users displayed. Note: Actual user data may vary.');
        }

        async function testDeleteUser() {
            const userId = document.getElementById('userIdInput').value;
            if (!userId) {
                logMessage('Please enter a user ID to test', 'error');
                return;
            }

            logMessage(`Testing deletion of user ID: ${userId}`);
            
            try {
                const response = await fetch(`delete-user.php?id=${userId}&ajax=1`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();
                
                if (data.success) {
                    logMessage(`SUCCESS: ${data.message}`, 'success');
                    if (data.data) {
                        if (data.data.deletion_log) {
                            logMessage('Deletion log: ' + JSON.stringify(data.data.deletion_log, null, 2));
                        }
                        if (data.data.related_records) {
                            logMessage('Related records: ' + JSON.stringify(data.data.related_records, null, 2));
                        }
                    }
                } else {
                    logMessage(`EXPECTED FAILURE: ${data.message}`, 'error');
                }
                
            } catch (error) {
                logMessage('Network/Parse error: ' + error.message, 'error');
            }
        }

        async function testSelfDeletion() {
            logMessage(`Testing self-deletion prevention (admin ID: ${currentAdminId})`);
            
            try {
                const response = await fetch(`delete-user.php?id=${currentAdminId}&ajax=1`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();
                
                if (data.success) {
                    logMessage('SECURITY BREACH: Self-deletion was allowed!', 'error');
                } else {
                    logMessage(`SECURITY CHECK PASSED: ${data.message}`, 'success');
                }
                
            } catch (error) {
                logMessage('Network/Parse error: ' + error.message, 'error');
            }
        }

        async function testAdminDeletion() {
            logMessage('Testing admin account deletion prevention (user ID: 1)');
            
            try {
                const response = await fetch(`delete-user.php?id=1&ajax=1`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();
                
                if (data.success) {
                    logMessage('SECURITY BREACH: Admin deletion was allowed!', 'error');
                } else {
                    logMessage(`SECURITY CHECK PASSED: ${data.message}`, 'success');
                }
                
            } catch (error) {
                logMessage('Network/Parse error: ' + error.message, 'error');
            }
        }

        async function testInvalidUser() {
            const invalidId = 99999;
            logMessage(`Testing invalid user ID: ${invalidId}`);
            
            try {
                const response = await fetch(`delete-user.php?id=${invalidId}&ajax=1`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();
                
                if (data.success) {
                    logMessage('UNEXPECTED: Invalid user deletion succeeded!', 'error');
                } else {
                    logMessage(`EXPECTED FAILURE: ${data.message}`, 'success');
                }
                
            } catch (error) {
                logMessage('Network/Parse error: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
