name: Psalm Static analysis

on:
  pull_request:
  push:
    branches:
      - "[0-9]+.[0-9]+.x"
  # Allow manually triggering the workflow.
  workflow_dispatch:

concurrency:
  group: psalm-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  psalm:
    name: Psalm
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up php
        uses: shivammathur/setup-php@v2
        with:
          coverage: none
          php-version: 8.3

      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        working-directory: ${{ github.workspace }}
        run: |
          composerCommand="install"
          composerOptions=("--no-interaction" "--no-progress" "--ansi")
          # Use `update` if there is no composer.lock file
          if [ ! -f "composer.lock" ]; then
            composerCommand="update"
          fi
          fullCommand="composer ${composerCommand} ${composerOptions[*]}"
          echo "Running: ${fullCommand}"
          ${fullCommand}

      - name: Run Psalm
        run: composer psalm:shepherd
